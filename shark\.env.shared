# Shared Database Configuration
# This file contains database settings used by ALL microservices

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=shark_user
DB_PASSWORD=shark_password
DB_NAME=shark_db
DB_SSL_MODE=disable

# Shared JWT Configuration
JWT_SECRET=shark-super-secret-jwt-key-for-development-only

# Redis Configuration (if using caching)
REDIS_URL=redis://localhost:6379

# Environment
ENVIRONMENT=development

# External Services (shared across services)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587

# API Gateway (if you add one later)
API_GATEWAY_URL=http://localhost:8000
