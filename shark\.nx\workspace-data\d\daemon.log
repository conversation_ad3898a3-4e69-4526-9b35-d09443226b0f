[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.111Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\93b930b80122b1e14adb\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.120Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\OneDrive\Desktop\coding\dodonx\shark (native)
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.127Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.147Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.149Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.156Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.231Z - Time taken for 'loadSpecifiedNxPlugins' 4.741200000000049ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.320Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\OneDrive\Desktop\coding\dodonx\shark\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1157.3312ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.354Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\OneDrive\Desktop\coding\dodonx\shark\node_modules\nx\src\plugins\package-json' 1188.2214ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.379Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.379Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.379Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.391Z - Time taken for 'loadDefaultNxPlugins' 1219.0077999999999ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.475Z - Time taken for 'build-project-configs' 47.84270000000015ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.619Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.621Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.622Z - Time taken for 'total for creating and serializing project graph' 1464.8600999999999ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.629Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.629Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1465. Response time: 8.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.500Z - [WATCHER]: libs/shared/types/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.503Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.608Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.608Z - [REQUEST]: libs/shared/types/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.608Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.619Z - Time taken for 'hash changed files from watcher' 2.5177999999868916ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.642Z - Time taken for 'build-project-configs' 20.822500000009313ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.694Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.695Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.695Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.695Z - Time taken for 'total execution time for createProjectGraph()' 38.39549999999872ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.702Z - [WATCHER]: libs/shared/constants/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.702Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.904Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.904Z - [REQUEST]: libs/shared/constants/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.904Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.919Z - Time taken for 'hash changed files from watcher' 0.20450000000710133ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.947Z - Time taken for 'build-project-configs' 22.364100000006147ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:24.000Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:24.001Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:24.001Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:24.001Z - Time taken for 'total execution time for createProjectGraph()' 35.40189999999711ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:29.871Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:29.872Z - [WATCHER]: libs/shared/utils/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.277Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.277Z - [REQUEST]: libs/shared/utils/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.277Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.280Z - Time taken for 'hash changed files from watcher' 0.20279999999911524ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.303Z - Time taken for 'build-project-configs' 19.384599999990314ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.337Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.337Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.337Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.338Z - Time taken for 'total execution time for createProjectGraph()' 24.963100000008126ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:37.552Z - [WATCHER]: libs/shared/ui-components/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:37.563Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.361Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.361Z - [REQUEST]: libs/shared/ui-components/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.361Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.364Z - Time taken for 'hash changed files from watcher' 0.2450000000098953ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.388Z - Time taken for 'build-project-configs' 20.768700000000536ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.429Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.430Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.430Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.430Z - Time taken for 'total execution time for createProjectGraph()' 30.43520000000717ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:45.515Z - [WATCHER]: libs/shared/ui-components/src/index.ts was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:45.517Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.128Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.128Z - [REQUEST]: libs/shared/ui-components/src/index.ts
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.128Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.158Z - Time taken for 'hash changed files from watcher' 0.22809999997843988ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.181Z - Time taken for 'build-project-configs' 31.86030000000028ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.227Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.228Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.228Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.229Z - Time taken for 'total execution time for createProjectGraph()' 33.76209999999264ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.931Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.932Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.933Z - Time taken for 'total for creating and serializing project graph' 0.7439000000013039ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.933Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.933Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.940Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.940Z - Done responding to the client handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.941Z - Handled MULTI_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.950Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.951Z - Done responding to the client handleGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.951Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.956Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.956Z - Done responding to the client handleHashMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.956Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.380Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.380Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.381Z - Time taken for 'total for creating and serializing project graph' 0.7312999999849126ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.382Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.382Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.493Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.494Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.495Z - Time taken for 'total for creating and serializing project graph' 0.4805999999807682ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.496Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.497Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.503Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.503Z - Done responding to the client handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.504Z - Handled MULTI_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.506Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.506Z - Done responding to the client handleHashMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.506Z - Handled HASH_GLOB. Handling time: 0. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.532Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.805Z - [WATCHER]: libs/shared/ui-components/src/components/Button/Button.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.806Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.913Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.913Z - [REQUEST]: libs/shared/ui-components/src/components/Button/Button.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.914Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.916Z - Time taken for 'hash changed files from watcher' 0.2260000000242144ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.940Z - Time taken for 'build-project-configs' 19.81429999996908ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.982Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.983Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.983Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.983Z - Time taken for 'total execution time for createProjectGraph()' 28.506799999973737ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.215Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.217Z - [WATCHER]: libs/shared/ui-components/src/components/Card/Card.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.431Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.431Z - [REQUEST]: libs/shared/ui-components/src/components/Card/Card.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.431Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.437Z - Time taken for 'hash changed files from watcher' 1.0304999999934807ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.456Z - Time taken for 'build-project-configs' 17.48589999997057ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.494Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.495Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.495Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.496Z - Time taken for 'total execution time for createProjectGraph()' 29.04899999999907ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.553Z - [WATCHER]: libs/shared/ui-components/src/components/Input/Input.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.554Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.962Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.962Z - [REQUEST]: libs/shared/ui-components/src/components/Input/Input.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.962Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.968Z - Time taken for 'hash changed files from watcher' 0.3267999999807216ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.989Z - Time taken for 'build-project-configs' 18.510299999965355ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:10.024Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:10.025Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:10.025Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:10.025Z - Time taken for 'total execution time for createProjectGraph()' 26.934499999973923ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:31.976Z - [WATCHER]: libs/shared/ui-components/src/components/Spinner/Spinner.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:31.976Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.779Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.779Z - [REQUEST]: libs/shared/ui-components/src/components/Spinner/Spinner.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.779Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.783Z - Time taken for 'hash changed files from watcher' 0.27710000006482005ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.802Z - Time taken for 'build-project-configs' 16.240600000019185ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.833Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.834Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.834Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.834Z - Time taken for 'total execution time for createProjectGraph()' 20.40720000001602ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:43.395Z - [WATCHER]: libs/shared/ui-components/src/components/Modal/Modal.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:43.395Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:44.997Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:44.997Z - [REQUEST]: libs/shared/ui-components/src/components/Modal/Modal.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:44.997Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.000Z - Time taken for 'hash changed files from watcher' 0.28029999998398125ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.019Z - Time taken for 'build-project-configs' 14.724999999976717ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.051Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.052Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.052Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.052Z - Time taken for 'total execution time for createProjectGraph()' 23.41870000003837ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:52.710Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:52.713Z - [WATCHER]: libs/shared/ui-components/src/components/Avatar/Avatar.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.927Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.927Z - [REQUEST]: libs/shared/ui-components/src/components/Avatar/Avatar.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.927Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.929Z - Time taken for 'hash changed files from watcher' 0.15839999995660037ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.950Z - Time taken for 'build-project-configs' 15.73389999999199ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.984Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.985Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.985Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.985Z - Time taken for 'total execution time for createProjectGraph()' 26.926399999996647ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:01.830Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:01.833Z - [WATCHER]: libs/shared/ui-components/src/components/Badge/Badge.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.239Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.239Z - [REQUEST]: libs/shared/ui-components/src/components/Badge/Badge.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.239Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.246Z - Time taken for 'hash changed files from watcher' 0.6149000000441447ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.272Z - Time taken for 'build-project-configs' 20.74580000003334ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.309Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.310Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.310Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.311Z - Time taken for 'total execution time for createProjectGraph()' 31.11960000009276ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:17.053Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:17.059Z - [WATCHER]: libs/shared/ui-components/src/components/Dropdown/Dropdown.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.465Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.465Z - [REQUEST]: libs/shared/ui-components/src/components/Dropdown/Dropdown.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.465Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.472Z - Time taken for 'hash changed files from watcher' 0.28049999999348074ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.501Z - Time taken for 'build-project-configs' 25.59490000002552ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.585Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.586Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.586Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.586Z - Time taken for 'total execution time for createProjectGraph()' 69.9267000000691ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:33.490Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:33.493Z - [WATCHER]: libs/shared/types/project.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.903Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.903Z - [REQUEST]: libs/shared/types/project.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.903Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.908Z - Time taken for 'hash changed files from watcher' 0.540599999949336ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.954Z - Time taken for 'build-project-configs' 40.29719999991357ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.013Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.014Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.014Z - Time taken for 'total execution time for createProjectGraph()' 50.8355000000447ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.605Z - [WATCHER]: libs/shared/types/tsconfig.lib.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.605Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.008Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.009Z - [REQUEST]: libs/shared/types/tsconfig.lib.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.009Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.024Z - Time taken for 'hash changed files from watcher' 0.8610999999800697ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.052Z - Time taken for 'build-project-configs' 30.76899999997113ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.088Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.088Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.088Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.089Z - Time taken for 'total execution time for createProjectGraph()' 29.944999999948777ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:01.590Z - [WATCHER]: libs/shared/ui-components/src/desktop.ini was deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:01.591Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:07.996Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:07.996Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:07.997Z - [REQUEST]: libs/shared/ui-components/src/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:08.017Z - Time taken for 'hash changed files from watcher' 0.118700000224635ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:08.044Z - Time taken for 'build-project-configs' 22.62789999996312ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:08.097Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:08.098Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:08.098Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:53:08.098Z - Time taken for 'total execution time for createProjectGraph()' 45.44109999993816ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:24.562Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:24.564Z - [WATCHER]: libs/shared/ui-components/src/components/desktop.ini was deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:24.726Z - [WATCHER]: libs/shared/ui-components/src/components/Button/desktop.ini was deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:24.726Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:30.977Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:30.977Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:30.978Z - [REQUEST]: libs/shared/ui-components/src/components/desktop.ini,libs/shared/ui-components/src/components/Button/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:30.984Z - Time taken for 'hash changed files from watcher' 0.17370000015944242ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:31.005Z - Time taken for 'build-project-configs' 18.88659999985248ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:31.045Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:31.045Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:31.046Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:54:31.046Z - Time taken for 'total execution time for createProjectGraph()' 26.705300000030547ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:44.355Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:44.360Z - [WATCHER]: services/user-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.776Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.776Z - [REQUEST]: services/user-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.776Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.780Z - Time taken for 'hash changed files from watcher' 0.33080000011250377ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.810Z - Time taken for 'build-project-configs' 25.16950000007637ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.861Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.862Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.862Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:03:50.863Z - Time taken for 'total execution time for createProjectGraph()' 37.12000000011176ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:06:56.818Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 2 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T05:06:56.819Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:06:56.928Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T05:06:56.928Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:06:56.984Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 2 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T05:06:56.984Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.222Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.222Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.222Z - [REQUEST]: libs/shared/ui-components/src/components/Badge/desktop.ini,libs/shared/ui-components/src/components/Avatar/desktop.ini,libs/shared/ui-components/src/components/Input/desktop.ini,libs/shared/ui-components/src/components/Dropdown/desktop.ini,libs/shared/ui-components/src/components/Card/desktop.ini,libs/shared/ui-components/src/components/Spinner/desktop.ini,libs/shared/ui-components/src/components/Modal/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.225Z - Time taken for 'hash changed files from watcher' 0.1673999996855855ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.248Z - Time taken for 'build-project-configs' 17.289799999911338ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.283Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.284Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.284Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:07:03.284Z - Time taken for 'total execution time for createProjectGraph()' 28.31140000000596ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:47.951Z - [WATCHER]: 0 file(s) created or restored, 1 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:47.952Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:48.003Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 4 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:48.003Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:48.057Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:48.058Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.365Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.365Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.365Z - [REQUEST]: services/user-service/configs/desktop.ini,services/user-service/cmd/desktop.ini,services/user-service/pkg/desktop.ini,services/user-service/internal/handlers/desktop.ini,services/user-service/internal/desktop.ini,services/user-service/internal/config/desktop.ini,services/user-service/internal/middleware/desktop.ini,services/user-service/internal/services/desktop.ini,services/user-service/internal/models/desktop.ini,services/user-service/internal/repository/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.371Z - Time taken for 'hash changed files from watcher' 0.2984999995678663ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.396Z - Time taken for 'build-project-configs' 20.66260000038892ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.437Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.438Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.438Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:08:54.438Z - Time taken for 'total execution time for createProjectGraph()' 29.172900000121444ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:08.390Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:08.463Z - [WATCHER]: services/user-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.866Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.866Z - [REQUEST]: services/user-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.866Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.873Z - Time taken for 'hash changed files from watcher' 0.3849999997764826ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.894Z - Time taken for 'build-project-configs' 20.20839999988675ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.932Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.933Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.934Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:14.934Z - Time taken for 'total execution time for createProjectGraph()' 23.938999999780208ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:24.424Z - [WATCHER]: services/user-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:24.425Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.840Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.840Z - [REQUEST]: services/user-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.840Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.845Z - Time taken for 'hash changed files from watcher' 0.37919999985024333ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.867Z - Time taken for 'build-project-configs' 20.215600000228733ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.903Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.903Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.904Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:30.904Z - Time taken for 'total execution time for createProjectGraph()' 26.290200000163168ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:49.772Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:49.774Z - [WATCHER]: services/user-service/internal/models/user.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.176Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.176Z - [REQUEST]: services/user-service/internal/models/user.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.176Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.180Z - Time taken for 'hash changed files from watcher' 0.3108000000938773ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.206Z - Time taken for 'build-project-configs' 21.53389999968931ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.242Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.243Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.243Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:17:56.243Z - Time taken for 'total execution time for createProjectGraph()' 27.277599999681115ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:16.574Z - [WATCHER]: services/user-service/internal/repository/user_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:16.574Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:22.976Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:22.976Z - [REQUEST]: services/user-service/internal/repository/user_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:22.976Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:22.978Z - Time taken for 'hash changed files from watcher' 0.21409999998286366ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:22.996Z - Time taken for 'build-project-configs' 15.892200000118464ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:23.032Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:23.033Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:23.033Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:23.033Z - Time taken for 'total execution time for createProjectGraph()' 23.24279999965802ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:32.559Z - [WATCHER]: services/user-service/internal/repository/role_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:32.559Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:38.974Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:38.974Z - [REQUEST]: services/user-service/internal/repository/role_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:38.974Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:38.978Z - Time taken for 'hash changed files from watcher' 0.2604000000283122ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:38.996Z - Time taken for 'build-project-configs' 17.223400000017136ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:39.030Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:39.031Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:39.031Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:18:39.031Z - Time taken for 'total execution time for createProjectGraph()' 22.59580000024289ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:01.648Z - [WATCHER]: services/user-service/internal/services/auth_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:01.697Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.060Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.060Z - [REQUEST]: services/user-service/internal/services/auth_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.060Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.063Z - Time taken for 'hash changed files from watcher' 0.307799999602139ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.085Z - Time taken for 'build-project-configs' 18.930699999909848ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.118Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.119Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.119Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:08.119Z - Time taken for 'total execution time for createProjectGraph()' 23.191899999976158ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:10.264Z - [WATCHER]: services/user-service/internal/services/auth_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:10.264Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.668Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.668Z - [REQUEST]: services/user-service/internal/services/auth_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.668Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.671Z - Time taken for 'hash changed files from watcher' 0.24659999972209334ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.695Z - Time taken for 'build-project-configs' 21.973200000356883ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.731Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.731Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.732Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:16.732Z - Time taken for 'total execution time for createProjectGraph()' 22.67449999973178ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:34.910Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:34.945Z - [WATCHER]: services/user-service/internal/services/user_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.355Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.355Z - [REQUEST]: services/user-service/internal/services/user_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.356Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.360Z - Time taken for 'hash changed files from watcher' 0.2760000000707805ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.385Z - Time taken for 'build-project-configs' 20.946599999908358ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.425Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.426Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.427Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:41.427Z - Time taken for 'total execution time for createProjectGraph()' 30.721300000324845ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:57.066Z - [WATCHER]: services/user-service/internal/middleware/auth.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:19:57.066Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.478Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.478Z - [REQUEST]: services/user-service/internal/middleware/auth.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.479Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.481Z - Time taken for 'hash changed files from watcher' 0.25150000024586916ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.500Z - Time taken for 'build-project-configs' 16.762099999934435ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.529Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.530Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.530Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:03.531Z - Time taken for 'total execution time for createProjectGraph()' 20.61989999981597ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:26.234Z - [WATCHER]: services/user-service/internal/handlers/auth_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:26.235Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.647Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.647Z - [REQUEST]: services/user-service/internal/handlers/auth_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.647Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.650Z - Time taken for 'hash changed files from watcher' 0.30010000010952353ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.668Z - Time taken for 'build-project-configs' 15.717199999839067ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.706Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.707Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.707Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:32.708Z - Time taken for 'total execution time for createProjectGraph()' 25.909500000067055ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:34.214Z - [WATCHER]: services/user-service/internal/services/auth_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:34.215Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.630Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.630Z - [REQUEST]: services/user-service/internal/services/auth_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.630Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.633Z - Time taken for 'hash changed files from watcher' 0.1877000001259148ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.653Z - Time taken for 'build-project-configs' 16.583600000012666ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.685Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.686Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.686Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:40.686Z - Time taken for 'total execution time for createProjectGraph()' 21.40640000021085ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:45.848Z - [WATCHER]: services/user-service/internal/services/auth_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:45.849Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.264Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.264Z - [REQUEST]: services/user-service/internal/services/auth_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.264Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.271Z - Time taken for 'hash changed files from watcher' 0.343299999833107ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.293Z - Time taken for 'build-project-configs' 19.20810000039637ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.334Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.334Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.335Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:52.335Z - Time taken for 'total execution time for createProjectGraph()' 27.8218999998644ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:58.318Z - [WATCHER]: services/user-service/internal/services/auth_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:20:58.319Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.732Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.732Z - [REQUEST]: services/user-service/internal/services/auth_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.733Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.736Z - Time taken for 'hash changed files from watcher' 0.3270000000484288ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.758Z - Time taken for 'build-project-configs' 19.68640000000596ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.791Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.792Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.792Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:04.793Z - Time taken for 'total execution time for createProjectGraph()' 23.677600000053644ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:10.383Z - [WATCHER]: services/user-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:10.384Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.799Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.799Z - [REQUEST]: services/user-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.799Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.801Z - Time taken for 'hash changed files from watcher' 0.27780000027269125ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.821Z - Time taken for 'build-project-configs' 15.224799999967217ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.850Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.851Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.851Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:16.851Z - Time taken for 'total execution time for createProjectGraph()' 22.478399999905378ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:51.637Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:51.638Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:52.516Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:52.516Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.042Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.042Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.042Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.045Z - Time taken for 'hash changed files from watcher' 0.26150000002235174ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.070Z - Time taken for 'build-project-configs' 18.91300000017509ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.111Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.112Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.112Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:21:58.112Z - Time taken for 'total execution time for createProjectGraph()' 31.857199999969453ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:02.739Z - [WATCHER]: services/user-service/.env.example was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:02.740Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.150Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.150Z - [REQUEST]: services/user-service/.env.example
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.150Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.154Z - Time taken for 'hash changed files from watcher' 0.2332000001333654ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.178Z - Time taken for 'build-project-configs' 21.156200000084937ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.223Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.223Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.224Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:09.224Z - Time taken for 'total execution time for createProjectGraph()' 29.39919999986887ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:13.051Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:13.053Z - [WATCHER]: services/user-service/Dockerfile was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.504Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.504Z - [REQUEST]: services/user-service/Dockerfile
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.504Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.513Z - Time taken for 'hash changed files from watcher' 0.21790000004693866ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.536Z - Time taken for 'build-project-configs' 19.259599999990314ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.577Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.578Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.578Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:19.578Z - Time taken for 'total execution time for createProjectGraph()' 30.388199999928474ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:33.760Z - [WATCHER]: services/user-service/README.md was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:33.760Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.175Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.175Z - [REQUEST]: services/user-service/README.md
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.175Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.204Z - Time taken for 'hash changed files from watcher' 0.26459999987855554ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.229Z - Time taken for 'build-project-configs' 21.538499999791384ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.276Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.277Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.277Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:40.277Z - Time taken for 'total execution time for createProjectGraph()' 34.13370000012219ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:43.394Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:43.396Z - [WATCHER]: services/service-catalog/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:49.845Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:49.846Z - [REQUEST]: services/service-catalog/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:49.846Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:49.943Z - Time taken for 'hash changed files from watcher' 0.29490000009536743ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:49.974Z - Time taken for 'build-project-configs' 24.849099999759346ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:50.038Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:50.039Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:50.039Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:22:50.039Z - Time taken for 'total execution time for createProjectGraph()' 34.796099999919534ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:18.092Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:18.154Z - [WATCHER]: libs/shared/constants/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:18.276Z - [WATCHER]: libs/shared/types/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:18.276Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:18.338Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:18.342Z - [WATCHER]: libs/shared/ui-components/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:18.414Z - [WATCHER]: libs/shared/utils/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:18.414Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.567Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.567Z - [REQUEST]: libs/shared/constants/package.json,libs/shared/types/package.json,libs/shared/ui-components/package.json,libs/shared/utils/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.567Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.575Z - Time taken for 'hash changed files from watcher' 1.9777999999932945ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.619Z - Time taken for 'build-project-configs' 39.69210000010207ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.672Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.673Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.673Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:29:24.673Z - Time taken for 'total execution time for createProjectGraph()' 41.35479999985546ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:29.916Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:29.917Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.328Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.328Z - [REQUEST]: services/user-service/go.mod,services/user-service/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.328Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.338Z - Time taken for 'hash changed files from watcher' 0.4953000000678003ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.382Z - Time taken for 'build-project-configs' 21.113599999807775ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.432Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.433Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.433Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:36.433Z - Time taken for 'total execution time for createProjectGraph()' 55.85259999986738ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:31:39.853Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:39.568Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:39.638Z - [WATCHER]: services/user-service/internal/repository/user_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.054Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.054Z - [REQUEST]: services/user-service/internal/repository/user_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.054Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.057Z - Time taken for 'hash changed files from watcher' 0.3814000003039837ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.080Z - Time taken for 'build-project-configs' 17.997999999672174ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.123Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.124Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.124Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:46.124Z - Time taken for 'total execution time for createProjectGraph()' 29.11849999986589ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:50.385Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:50.386Z - [WATCHER]: services/user-service/internal/repository/role_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.794Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.794Z - [REQUEST]: services/user-service/internal/repository/role_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.794Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.797Z - Time taken for 'hash changed files from watcher' 0.25369999976828694ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.827Z - Time taken for 'build-project-configs' 23.334300000220537ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.870Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.871Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.871Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:56.871Z - Time taken for 'total execution time for createProjectGraph()' 30.33640000037849ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:59.182Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:32:59.222Z - [WATCHER]: services/user-service/internal/repository/user_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.623Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.623Z - [REQUEST]: services/user-service/internal/repository/user_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.623Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.625Z - Time taken for 'hash changed files from watcher' 0.18789999978616834ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.651Z - Time taken for 'build-project-configs' 19.078999999910593ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.696Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.696Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.697Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:05.697Z - Time taken for 'total execution time for createProjectGraph()' 33.96100000012666ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:52.728Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:52.730Z - [WATCHER]: services/user-service/internal/middleware/auth.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.139Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.139Z - [REQUEST]: services/user-service/internal/middleware/auth.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.139Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.144Z - Time taken for 'hash changed files from watcher' 0.39639999996870756ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.167Z - Time taken for 'build-project-configs' 20.334100000094622ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.214Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.215Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.215Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:33:59.216Z - Time taken for 'total execution time for createProjectGraph()' 30.721000000368804ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:44.141Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:44.141Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.546Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.547Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.547Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.551Z - Time taken for 'hash changed files from watcher' 0.30570000037550926ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.884Z - Time taken for 'build-project-configs' 328.68250000011176ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.929Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.930Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.930Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:34:50.930Z - Time taken for 'total execution time for createProjectGraph()' 31.607199999969453ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:23.124Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:23.127Z - [WATCHER]: libs/shared/ui-components/src/index.ts was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:29.551Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:29.551Z - [REQUEST]: libs/shared/ui-components/src/index.ts
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:29.551Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:29.574Z - Time taken for 'hash changed files from watcher' 0.29860000032931566ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:29.791Z - Time taken for 'build-project-configs' 125.84700000006706ms
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:30.084Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:30.085Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:30.085Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T05:42:30.086Z - Time taken for 'total execution time for createProjectGraph()' 181.27249999996275ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:00.846Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:00.856Z - [WATCHER]: 0 file(s) created or restored, 1 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:00.893Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:00.895Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 2 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.263Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.263Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.263Z - [REQUEST]: services/service-catalog/cmd/desktop.ini,services/service-catalog/pkg/desktop.ini,services/service-catalog/internal/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.337Z - Time taken for 'hash changed files from watcher' 0.2527999999001622ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.367Z - Time taken for 'build-project-configs' 78.68219999969006ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.446Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.448Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.448Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:07.448Z - Time taken for 'total execution time for createProjectGraph()' 61.4867000002414ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:53.816Z - [WATCHER]: services/service-catalog/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:06:53.816Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.227Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.227Z - [REQUEST]: services/service-catalog/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.227Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.230Z - Time taken for 'hash changed files from watcher' 0.26889999955892563ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.255Z - Time taken for 'build-project-configs' 19.892599999904633ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.309Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.310Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.310Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:00.310Z - Time taken for 'total execution time for createProjectGraph()' 37.94150000065565ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:33.824Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:33.830Z - [WATCHER]: services/service-catalog/internal/models/service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.234Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.234Z - [REQUEST]: services/service-catalog/internal/models/service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.234Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.242Z - Time taken for 'hash changed files from watcher' 0.6587000004947186ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.271Z - Time taken for 'build-project-configs' 29.071800000034273ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.317Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.318Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.318Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:40.318Z - Time taken for 'total execution time for createProjectGraph()' 27.820299999788404ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:52.283Z - [WATCHER]: services/service-catalog/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:52.283Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.695Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.695Z - [REQUEST]: services/service-catalog/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.695Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.698Z - Time taken for 'hash changed files from watcher' 0.37130000069737434ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.722Z - Time taken for 'build-project-configs' 17.169499999843538ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.759Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.759Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.760Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:07:58.761Z - Time taken for 'total execution time for createProjectGraph()' 26.638799999840558ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:25.305Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:25.320Z - [WATCHER]: 0 file(s) created or restored, 1 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:25.358Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:25.358Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:28.935Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:28.936Z - [WATCHER]: services/service-catalog/internal/repository/service_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.728Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.728Z - [REQUEST]: services/service-catalog/internal/repository/service_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.728Z - [REQUEST]: services/service-catalog/internal/handlers/desktop.ini,services/service-catalog/internal/config/desktop.ini,services/service-catalog/internal/middleware/desktop.ini,services/service-catalog/internal/models/desktop.ini,services/service-catalog/internal/services/desktop.ini,services/service-catalog/internal/repository/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.739Z - Time taken for 'hash changed files from watcher' 0.38530000019818544ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.766Z - Time taken for 'build-project-configs' 26.420699999667704ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.847Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.849Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.849Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:08:31.849Z - Time taken for 'total execution time for createProjectGraph()' 42.433400000445545ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:05.276Z - [WATCHER]: services/service-catalog/internal/repository/category_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:05.276Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.684Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.684Z - [REQUEST]: services/service-catalog/internal/repository/category_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.685Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.688Z - Time taken for 'hash changed files from watcher' 0.33719999995082617ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.716Z - Time taken for 'build-project-configs' 22.14890000037849ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.750Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.751Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.751Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:11.751Z - Time taken for 'total execution time for createProjectGraph()' 24.167899999767542ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:32.306Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:32.313Z - [WATCHER]: services/service-catalog/internal/services/service_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.720Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.720Z - [REQUEST]: services/service-catalog/internal/services/service_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.720Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.723Z - Time taken for 'hash changed files from watcher' 0.2924000006169081ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.745Z - Time taken for 'build-project-configs' 18.36049999948591ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.785Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.786Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.786Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:38.786Z - Time taken for 'total execution time for createProjectGraph()' 24.911499999463558ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:55.197Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:09:55.199Z - [WATCHER]: services/service-catalog/internal/services/category_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.604Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.604Z - [REQUEST]: services/service-catalog/internal/services/category_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.604Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.607Z - Time taken for 'hash changed files from watcher' 0.44660000037401915ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.640Z - Time taken for 'build-project-configs' 25.67489999998361ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.683Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.685Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.685Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:01.686Z - Time taken for 'total execution time for createProjectGraph()' 29.982800000347197ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:29.389Z - [WATCHER]: services/service-catalog/internal/middleware/auth.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:29.390Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.803Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.804Z - [REQUEST]: services/service-catalog/internal/middleware/auth.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.804Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.809Z - Time taken for 'hash changed files from watcher' 2.0848000003024936ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.834Z - Time taken for 'build-project-configs' 20.291699999943376ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.895Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.895Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.896Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:35.896Z - Time taken for 'total execution time for createProjectGraph()' 40.962600000202656ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:59.902Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:10:59.906Z - [WATCHER]: services/service-catalog/internal/handlers/service_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.313Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.313Z - [REQUEST]: services/service-catalog/internal/handlers/service_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.313Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.316Z - Time taken for 'hash changed files from watcher' 0.30429999995976686ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.336Z - Time taken for 'build-project-configs' 16.017599999904633ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.372Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.373Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.373Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:06.373Z - Time taken for 'total execution time for createProjectGraph()' 21.72250000014901ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:13.448Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:13.450Z - [WATCHER]: services/service-catalog/internal/handlers/service_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.864Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.864Z - [REQUEST]: services/service-catalog/internal/handlers/service_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.867Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.870Z - Time taken for 'hash changed files from watcher' 0.202199999243021ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.894Z - Time taken for 'build-project-configs' 17.571999999694526ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.929Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.930Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.930Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:19.930Z - Time taken for 'total execution time for createProjectGraph()' 25.08750000037253ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:26.154Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:26.166Z - [WATCHER]: services/service-catalog/internal/handlers/service_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.567Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.567Z - [REQUEST]: services/service-catalog/internal/handlers/service_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.567Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.570Z - Time taken for 'hash changed files from watcher' 0.27020000014454126ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.601Z - Time taken for 'build-project-configs' 27.150499999523163ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.633Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.634Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.634Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:11:32.634Z - Time taken for 'total execution time for createProjectGraph()' 23.05850000027567ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:08.161Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:08.165Z - [WATCHER]: services/service-catalog/internal/handlers/category_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.571Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.571Z - [REQUEST]: services/service-catalog/internal/handlers/category_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.572Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.575Z - Time taken for 'hash changed files from watcher' 0.24390000011771917ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.596Z - Time taken for 'build-project-configs' 16.552300000563264ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.634Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.635Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.635Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:14.636Z - Time taken for 'total execution time for createProjectGraph()' 25.507699999958277ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:32.502Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:32.502Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.015Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.015Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.015Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.100Z - Time taken for 'hash changed files from watcher' 44.830699999816716ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.232Z - Time taken for 'build-project-configs' 80.58310000039637ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.372Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.372Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.373Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:39.373Z - Time taken for 'total execution time for createProjectGraph()' 102.86660000029951ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:43.444Z - [WATCHER]: services/service-catalog/.env.example was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:43.445Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.846Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.846Z - [REQUEST]: services/service-catalog/.env.example
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.846Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.850Z - Time taken for 'hash changed files from watcher' 0.29440000001341105ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.867Z - Time taken for 'build-project-configs' 14.513499999418855ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.896Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.897Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.897Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:49.897Z - Time taken for 'total execution time for createProjectGraph()' 19.329599999822676ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:12:52.544Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:13:59.439Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:13:59.440Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.855Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.855Z - [REQUEST]: services/service-catalog/go.sum,services/service-catalog/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.855Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.859Z - Time taken for 'hash changed files from watcher' 0.618300000205636ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.886Z - Time taken for 'build-project-configs' 21.352500000037253ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.931Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.931Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.932Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:05.932Z - Time taken for 'total execution time for createProjectGraph()' 28.82909999974072ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:34.034Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:34.036Z - [WATCHER]: services/service-catalog/internal/services/category_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.449Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.449Z - [REQUEST]: services/service-catalog/internal/services/category_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.449Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.453Z - Time taken for 'hash changed files from watcher' 0.5249999994412065ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.479Z - Time taken for 'build-project-configs' 21.535999999381602ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.534Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.535Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.535Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:40.535Z - Time taken for 'total execution time for createProjectGraph()' 37.19969999976456ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:47.371Z - [WATCHER]: services/service-catalog/internal/services/category_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:47.371Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.775Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.775Z - [REQUEST]: services/service-catalog/internal/services/category_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.775Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.778Z - Time taken for 'hash changed files from watcher' 0.2918000007048249ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.800Z - Time taken for 'build-project-configs' 18.139000000432134ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.836Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.837Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.837Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:53.837Z - Time taken for 'total execution time for createProjectGraph()' 21.34250000026077ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:58.447Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:14:58.457Z - [WATCHER]: services/service-catalog/internal/services/category_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.859Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.859Z - [REQUEST]: services/service-catalog/internal/services/category_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.859Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.865Z - Time taken for 'hash changed files from watcher' 0.25179999973624945ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.889Z - Time taken for 'build-project-configs' 19.10939999949187ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.930Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.931Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.931Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:15:04.931Z - Time taken for 'total execution time for createProjectGraph()' 30.85950000025332ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:35.857Z - [WATCHER]: package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:35.858Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:36.275Z - [WATCHER]: package-lock.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:36.276Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:36.557Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:36.560Z - [WATCHER]: libs/shared/types/tsconfig.lib.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:36.722Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:36.724Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:36.790Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:36.792Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.052Z - [WATCHER]: libs/shared/ui-components/src/components/Card/Card.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.052Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.231Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.233Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.295Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.296Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.735Z - [WATCHER]: services/user-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.736Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.842Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.844Z - [WATCHER]: services/user-service/internal/handlers/auth_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.974Z - [WATCHER]: services/user-service/internal/services/user_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:37.974Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.028Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.030Z - [WATCHER]: services/user-service/internal/models/user.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.093Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.095Z - [WATCHER]: services/user-service/README.md was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.296Z - [WATCHER]: services/user-service/Dockerfile was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.296Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.437Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.438Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.547Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.548Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.673Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.675Z - [WATCHER]: services/user-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.750Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.923Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.925Z - [WATCHER]: services/user-service/internal/repository/user_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.988Z - [WATCHER]: services/user-service/internal/repository/role_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:38.988Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.081Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.088Z - [WATCHER]: services/user-service/internal/middleware/auth.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.185Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.189Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.361Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.362Z - [WATCHER]: services/service-catalog/internal/middleware/auth.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.440Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.442Z - [WATCHER]: services/service-catalog/internal/repository/service_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.536Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.536Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.648Z - [WATCHER]: services/service-catalog/internal/services/service_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.650Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.742Z - [WATCHER]: services/service-catalog/internal/handlers/service_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.742Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.910Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.943Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:39.974Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:40.052Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:40.054Z - [WATCHER]: services/service-catalog/internal/handlers/category_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:40.148Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:40.158Z - [WATCHER]: services/service-catalog/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:40.235Z - [WATCHER]: services/service-catalog/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:40.235Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:40.380Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:40.384Z - [WATCHER]: services/service-catalog/internal/services/category_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.263Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.263Z - [REQUEST]: package.json,package-lock.json,libs/shared/types/tsconfig.lib.json,libs/shared/types/project.json,libs/shared/ui-components/src/components/Avatar/Avatar.tsx,libs/shared/ui-components/src/components/Badge/Badge.tsx,libs/shared/ui-components/src/components/Button/Button.tsx,libs/shared/ui-components/src/components/Card/Card.tsx,libs/shared/ui-components/src/components/Dropdown/Dropdown.tsx,libs/shared/ui-components/src/components/Input/Input.tsx,libs/shared/ui-components/src/components/Spinner/Spinner.tsx,libs/shared/ui-components/src/components/Modal/Modal.tsx,services/user-service/internal/config/config.go,services/user-service/internal/handlers/auth_handler.go,services/user-service/internal/services/user_service.go,services/user-service/internal/models/user.go,services/user-service/README.md,services/user-service/Dockerfile,services/user-service/.env.example,services/user-service/cmd/main.go,services/user-service/internal/services/auth_service.go,services/user-service/go.sum,services/user-service/go.mod,services/user-service/internal/repository/user_repository.go,services/user-service/internal/repository/role_repository.go,services/user-service/internal/middleware/auth.go,services/user-service/internal/handlers/user_handler.go,services/service-catalog/internal/config/config.go,services/service-catalog/internal/middleware/auth.go,services/service-catalog/internal/repository/service_repository.go,services/service-catalog/internal/models/service.go,services/service-catalog/internal/repository/category_repository.go,services/service-catalog/internal/services/service_service.go,services/service-catalog/internal/handlers/service_handler.go,services/service-catalog/cmd/main.go,services/service-catalog/.env.example,services/service-catalog/internal/handlers/category_handler.go,services/service-catalog/go.sum,services/service-catalog/go.mod,services/service-catalog/internal/services/category_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.263Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.271Z - Time taken for 'hash changed files from watcher' 2.389200000092387ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.300Z - Time taken for 'build-project-configs' 23.485400000587106ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.359Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.360Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.360Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:30:42.360Z - Time taken for 'total execution time for createProjectGraph()' 43.29040000028908ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:25.777Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:25.780Z - [WATCHER]: services/booking-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.204Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.204Z - [REQUEST]: services/booking-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.204Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.285Z - Time taken for 'hash changed files from watcher' 1.451799999922514ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.372Z - Time taken for 'build-project-configs' 84.15919999964535ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.565Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.566Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.566Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:32.566Z - Time taken for 'total execution time for createProjectGraph()' 120.5527999997139ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:59.747Z - [WATCHER]: services/booking-service/internal/models/booking.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:42:59.747Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.159Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.159Z - [REQUEST]: services/booking-service/internal/models/booking.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.159Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.163Z - Time taken for 'hash changed files from watcher' 0.3854000000283122ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.182Z - Time taken for 'build-project-configs' 16.71140000037849ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.224Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.225Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.225Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:06.225Z - Time taken for 'total execution time for createProjectGraph()' 25.615800000727177ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:15.367Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:15.367Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.777Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.777Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.777Z - [REQUEST]: services/booking-service/internal/desktop.ini,services/booking-service/pkg/desktop.ini,services/booking-service/cmd/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.781Z - Time taken for 'hash changed files from watcher' 0.22990000061690807ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.803Z - Time taken for 'build-project-configs' 19.222800000570714ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.849Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.849Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.850Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:21.850Z - Time taken for 'total execution time for createProjectGraph()' 26.64890000037849ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:23.911Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:23.913Z - [WATCHER]: services/booking-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.320Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.320Z - [REQUEST]: services/booking-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.320Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.354Z - Time taken for 'hash changed files from watcher' 3.6385999992489815ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.402Z - Time taken for 'build-project-configs' 44.32919999957085ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.451Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.452Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.452Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:43:30.453Z - Time taken for 'total execution time for createProjectGraph()' 36.011400000192225ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:02.519Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:02.522Z - [WATCHER]: services/booking-service/internal/repository/booking_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:08.932Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:08.932Z - [REQUEST]: services/booking-service/internal/repository/booking_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:08.932Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:08.934Z - Time taken for 'hash changed files from watcher' 0.32210000045597553ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:08.953Z - Time taken for 'build-project-configs' 15.394199999980628ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:09.008Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:09.009Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:09.010Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:09.010Z - Time taken for 'total execution time for createProjectGraph()' 42.322800000198185ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:19.898Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:19.899Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:19.953Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:19.953Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:20.018Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:20.023Z - [WATCHER]: services/booking-service/internal/models/booking.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.306Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.307Z - [REQUEST]: services/booking-service/go.mod,services/booking-service/internal/config/config.go,services/booking-service/internal/models/booking.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.307Z - [REQUEST]: services/booking-service/internal/config/desktop.ini,services/booking-service/internal/handlers/desktop.ini,services/booking-service/internal/models/desktop.ini,services/booking-service/internal/services/desktop.ini,services/booking-service/internal/repository/desktop.ini,services/booking-service/internal/middleware/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.311Z - Time taken for 'hash changed files from watcher' 0.8552000001072884ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.333Z - Time taken for 'build-project-configs' 20.282300000078976ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.369Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.370Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.370Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:26.370Z - Time taken for 'total execution time for createProjectGraph()' 24.356100000441074ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:46.037Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:46.038Z - [WATCHER]: services/booking-service/internal/repository/availability_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.446Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.447Z - [REQUEST]: services/booking-service/internal/repository/availability_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.447Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.453Z - Time taken for 'hash changed files from watcher' 0.48330000042915344ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.473Z - Time taken for 'build-project-configs' 15.623399999924004ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.506Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.506Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.507Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:44:52.507Z - Time taken for 'total execution time for createProjectGraph()' 24.207500000484288ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:20.120Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:20.121Z - [WATCHER]: services/booking-service/internal/repository/booking_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.523Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.523Z - [REQUEST]: services/booking-service/internal/repository/booking_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.523Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.526Z - Time taken for 'hash changed files from watcher' 0.2895999997854233ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.556Z - Time taken for 'build-project-configs' 25.802099999971688ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.594Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.595Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.595Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:26.595Z - Time taken for 'total execution time for createProjectGraph()' 25.736899999901652ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:28.765Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:28.767Z - [WATCHER]: services/booking-service/internal/services/booking_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:35.169Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:35.169Z - [REQUEST]: services/booking-service/internal/services/booking_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:35.169Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:35.207Z - Time taken for 'hash changed files from watcher' 0.4780000001192093ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:35.274Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:35.274Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:35.275Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:45:35.275Z - Time taken for 'total execution time for createProjectGraph()' 50.65400000009686ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:12.330Z - [WATCHER]: services/booking-service/internal/services/availability_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:12.330Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.738Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.738Z - [REQUEST]: services/booking-service/internal/services/availability_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.738Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.740Z - Time taken for 'hash changed files from watcher' 0.2194999996572733ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.759Z - Time taken for 'build-project-configs' 14.888399999588728ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.793Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.794Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.794Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:18.794Z - Time taken for 'total execution time for createProjectGraph()' 21.44900000002235ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:22.584Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:22.586Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:28.997Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:28.997Z - [REQUEST]: services/booking-service/internal/repository/availability_repository.go,services/booking-service/internal/services/booking_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:28.997Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:29.004Z - Time taken for 'hash changed files from watcher' 0.4599999999627471ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:29.025Z - Time taken for 'build-project-configs' 21.044599999673665ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:29.074Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:29.075Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:29.075Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:29.076Z - Time taken for 'total execution time for createProjectGraph()' 24.197799999266863ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:38.422Z - [WATCHER]: services/booking-service/internal/middleware/auth.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:38.423Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.835Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.835Z - [REQUEST]: services/booking-service/internal/middleware/auth.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.835Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.839Z - Time taken for 'hash changed files from watcher' 0.4147000005468726ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.866Z - Time taken for 'build-project-configs' 22.857499999925494ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.911Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.916Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.916Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:46:44.916Z - Time taken for 'total execution time for createProjectGraph()' 32.38299999944866ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:14.888Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:14.890Z - [WATCHER]: services/booking-service/internal/handlers/booking_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.303Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.303Z - [REQUEST]: services/booking-service/internal/handlers/booking_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.303Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.305Z - Time taken for 'hash changed files from watcher' 0.41750000044703484ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.325Z - Time taken for 'build-project-configs' 17.26150000002235ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.365Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.366Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.366Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:21.366Z - Time taken for 'total execution time for createProjectGraph()' 28.229000000283122ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:26.114Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:26.116Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.521Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.522Z - [REQUEST]: services/booking-service/internal/services/availability_service.go,services/booking-service/internal/middleware/auth.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.522Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.529Z - Time taken for 'hash changed files from watcher' 0.5616999994963408ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.549Z - Time taken for 'build-project-configs' 17.915299999527633ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.596Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.596Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.597Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:32.597Z - Time taken for 'total execution time for createProjectGraph()' 31.217300000600517ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:46.956Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:46.957Z - [WATCHER]: services/booking-service/internal/handlers/availability_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.362Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.362Z - [REQUEST]: services/booking-service/internal/handlers/availability_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.362Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.365Z - Time taken for 'hash changed files from watcher' 0.31339999940246344ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.391Z - Time taken for 'build-project-configs' 20.43530000001192ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.426Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.427Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.427Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:47:53.427Z - Time taken for 'total execution time for createProjectGraph()' 24.6003999998793ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:11.958Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:11.966Z - [WATCHER]: services/booking-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.376Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.376Z - [REQUEST]: services/booking-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.376Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.379Z - Time taken for 'hash changed files from watcher' 0.3530000001192093ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.402Z - Time taken for 'build-project-configs' 19.89049999974668ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.433Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.434Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.435Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:18.435Z - Time taken for 'total execution time for createProjectGraph()' 21.483700000680983ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:25.876Z - [WATCHER]: services/booking-service/.env.example was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:25.879Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:31.200Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:31.201Z - [WATCHER]: services/booking-service/internal/handlers/booking_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.285Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.285Z - [REQUEST]: services/booking-service/.env.example,services/booking-service/internal/handlers/booking_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.286Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.300Z - Time taken for 'hash changed files from watcher' 1.3825000002980232ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.341Z - Time taken for 'build-project-configs' 42.524000000208616ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.387Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.388Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.388Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:32.388Z - Time taken for 'total execution time for createProjectGraph()' 33.252000000327826ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:48:36.227Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:11.420Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:11.422Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.828Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.828Z - [REQUEST]: services/booking-service/go.mod,services/booking-service/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.828Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.832Z - Time taken for 'hash changed files from watcher' 0.4429000001400709ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.854Z - Time taken for 'build-project-configs' 18.040599999949336ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.902Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.903Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.903Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:17.903Z - Time taken for 'total execution time for createProjectGraph()' 36.52680000010878ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:30.785Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:30.785Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.198Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.199Z - [REQUEST]: services/booking-service/internal/handlers/availability_handler.go,services/booking-service/cmd/main.go,services/booking-service/.env.example
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.199Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.201Z - Time taken for 'hash changed files from watcher' 0.4974000006914139ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.225Z - Time taken for 'build-project-configs' 19.675099999643862ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.260Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.261Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.261Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:49:37.261Z - Time taken for 'total execution time for createProjectGraph()' 22.975899999961257ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:28.721Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:28.723Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.134Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.134Z - [REQUEST]: services/booking-service/go.sum,services/booking-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.134Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.138Z - Time taken for 'hash changed files from watcher' 0.6319000013172626ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.168Z - Time taken for 'build-project-configs' 25.296500001102686ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.204Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.205Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.205Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:50:35.205Z - Time taken for 'total execution time for createProjectGraph()' 23.129399999976158ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:25.655Z - [WATCHER]: services/payment-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:25.656Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.067Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.067Z - [REQUEST]: services/payment-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.068Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.086Z - Time taken for 'hash changed files from watcher' 1.7561000008136034ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.127Z - Time taken for 'build-project-configs' 45.75870000012219ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.174Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.175Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.175Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:56:32.176Z - Time taken for 'total execution time for createProjectGraph()' 32.64479999989271ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:03.891Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:03.891Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:05.034Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:05.093Z - [WATCHER]: services/payment-service/internal/models/payment.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.302Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.303Z - [REQUEST]: services/payment-service/internal/models/payment.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.303Z - [REQUEST]: services/payment-service/cmd/desktop.ini,services/payment-service/internal/desktop.ini,services/payment-service/pkg/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.307Z - Time taken for 'hash changed files from watcher' 0.34769999980926514ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.327Z - Time taken for 'build-project-configs' 16.34239999949932ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.368Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.369Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.369Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:10.369Z - Time taken for 'total execution time for createProjectGraph()' 24.839800000190735ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:34.182Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:34.224Z - [WATCHER]: services/payment-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.630Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.630Z - [REQUEST]: services/payment-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.630Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.672Z - Time taken for 'hash changed files from watcher' 0.31850000098347664ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.729Z - Time taken for 'build-project-configs' 24.587100001052022ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.838Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.839Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.839Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:57:40.839Z - Time taken for 'total execution time for createProjectGraph()' 90.1272999998182ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:14.122Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:14.124Z - [WATCHER]: services/payment-service/internal/repository/payment_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:16.891Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:16.894Z - [WATCHER]: 0 file(s) created or restored, 1 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:16.942Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:16.943Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.536Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.536Z - [REQUEST]: services/payment-service/internal/repository/payment_repository.go,services/payment-service/internal/models/payment.go,services/payment-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.536Z - [REQUEST]: services/payment-service/internal/config/desktop.ini,services/payment-service/internal/models/desktop.ini,services/payment-service/internal/handlers/desktop.ini,services/payment-service/internal/middleware/desktop.ini,services/payment-service/internal/services/desktop.ini,services/payment-service/internal/repository/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.541Z - Time taken for 'hash changed files from watcher' 0.7595000006258488ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.563Z - Time taken for 'build-project-configs' 20.13679999858141ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.597Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.598Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.598Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:20.599Z - Time taken for 'total execution time for createProjectGraph()' 22.51460000127554ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:44.248Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:44.285Z - [WATCHER]: services/payment-service/internal/services/payment_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.692Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.692Z - [REQUEST]: services/payment-service/internal/services/payment_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.692Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.697Z - Time taken for 'hash changed files from watcher' 0.2770999986678362ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.717Z - Time taken for 'build-project-configs' 17.17850000038743ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.756Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.757Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.757Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:58:50.757Z - Time taken for 'total execution time for createProjectGraph()' 29.463400000706315ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:03.274Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:03.276Z - [WATCHER]: services/payment-service/internal/middleware/auth.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.679Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.679Z - [REQUEST]: services/payment-service/internal/middleware/auth.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.679Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.682Z - Time taken for 'hash changed files from watcher' 0.34009999968111515ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.702Z - Time taken for 'build-project-configs' 15.854199999943376ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.740Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.741Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.741Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:09.741Z - Time taken for 'total execution time for createProjectGraph()' 27.006300000473857ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:15.912Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:15.912Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.326Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.326Z - [REQUEST]: services/payment-service/internal/config/config.go,services/payment-service/internal/repository/payment_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.326Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.330Z - Time taken for 'hash changed files from watcher' 0.6513999998569489ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.352Z - Time taken for 'build-project-configs' 18.717800000682473ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.391Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.392Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.392Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:22.392Z - Time taken for 'total execution time for createProjectGraph()' 30.989000000059605ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:35.284Z - [WATCHER]: services/payment-service/internal/handlers/payment_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:35.285Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.689Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.690Z - [REQUEST]: services/payment-service/internal/handlers/payment_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.690Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.694Z - Time taken for 'hash changed files from watcher' 0.29790000058710575ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.717Z - Time taken for 'build-project-configs' 18.887900000438094ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.760Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.760Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.761Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:41.761Z - Time taken for 'total execution time for createProjectGraph()' 24.95009999908507ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:52.478Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:52.483Z - [WATCHER]: services/payment-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.894Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.894Z - [REQUEST]: services/payment-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.894Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.897Z - Time taken for 'hash changed files from watcher' 0.25399999879300594ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.917Z - Time taken for 'build-project-configs' 16.198099998757243ms
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.961Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.962Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.962Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T06:59:58.962Z - Time taken for 'total execution time for createProjectGraph()' 33.227500000968575ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:02.732Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:02.771Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:18.613Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:18.614Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.021Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.021Z - [REQUEST]: services/payment-service/internal/middleware/auth.go,services/payment-service/internal/services/payment_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.021Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.024Z - Time taken for 'hash changed files from watcher' 0.5456000007688999ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.044Z - Time taken for 'build-project-configs' 15.576999999582767ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.079Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.079Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.080Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:00:25.080Z - Time taken for 'total execution time for createProjectGraph()' 23.924599999561906ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:09.128Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:09.149Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.560Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.560Z - [REQUEST]: services/payment-service/cmd/main.go,services/payment-service/internal/handlers/payment_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.560Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.565Z - Time taken for 'hash changed files from watcher' 0.5065999999642372ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.583Z - Time taken for 'build-project-configs' 14.932400001212955ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.620Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.621Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.621Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:01:15.622Z - Time taken for 'total execution time for createProjectGraph()' 29.792099999263883ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:23.686Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:23.691Z - [WATCHER]: services/product-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.101Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.102Z - [REQUEST]: services/product-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.102Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.104Z - Time taken for 'hash changed files from watcher' 0.830800000578165ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.130Z - Time taken for 'build-project-configs' 20.30970000103116ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.170Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.170Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.171Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:30.171Z - Time taken for 'total execution time for createProjectGraph()' 28.193800000473857ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:58.883Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:02:58.888Z - [WATCHER]: services/product-service/internal/models/product.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.289Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.290Z - [REQUEST]: services/product-service/internal/models/product.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.290Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.294Z - Time taken for 'hash changed files from watcher' 0.4618999995291233ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.320Z - Time taken for 'build-project-configs' 20.744100000709295ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.362Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.363Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.363Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:05.363Z - Time taken for 'total execution time for createProjectGraph()' 29.269300000742078ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:11.565Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:11.565Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:17.968Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:17.968Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:17.968Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:17.994Z - Time taken for 'hash changed files from watcher' 0.3447999991476536ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:18.006Z - Time taken for 'build-project-configs' 24.501799998804927ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:18.050Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:18.051Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:18.051Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:18.052Z - Time taken for 'total execution time for createProjectGraph()' 32.187999999150634ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:18.301Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:48.216Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:48.217Z - [WATCHER]: services/notification-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.652Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.652Z - [REQUEST]: services/notification-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.653Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.657Z - Time taken for 'hash changed files from watcher' 0.3440999984741211ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.678Z - Time taken for 'build-project-configs' 18.56300000101328ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.715Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.716Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.716Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:03:54.716Z - Time taken for 'total execution time for createProjectGraph()' 25.67559999972582ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:03.516Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:03.682Z - [WATCHER]: services/notification-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.046Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.096Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.096Z - [REQUEST]: services/notification-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.096Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.099Z - Time taken for 'hash changed files from watcher' 0.3645999990403652ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.151Z - Time taken for 'build-project-configs' 37.51650000177324ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.210Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.211Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.211Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:10.211Z - Time taken for 'total execution time for createProjectGraph()' 56.09439999982715ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:12.572Z - [WATCHER]: 0 file(s) created or restored, 1 file(s) modified, 2 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:12.575Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:12.621Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 4 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:12.632Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:12.686Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:12.691Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 2 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:12.748Z - [WATCHER]: services/product-service/internal/models/product.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:12.748Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:18.978Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:18.978Z - [REQUEST]: services/product-service/cmd/main.go,services/product-service/go.mod,services/product-service/internal/models/product.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:18.978Z - [REQUEST]: services/product-service/cmd/desktop.ini,services/product-service/internal/desktop.ini,services/product-service/internal/handlers/desktop.ini,services/product-service/internal/models/desktop.ini,services/product-service/internal/config/desktop.ini,services/product-service/internal/middleware/desktop.ini,services/product-service/internal/repository/desktop.ini,services/product-service/internal/services/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:18.986Z - Time taken for 'hash changed files from watcher' 1.3429999984800816ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:19.013Z - Time taken for 'build-project-configs' 23.349799999967217ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:19.062Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:19.063Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:19.063Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:19.064Z - Time taken for 'total execution time for createProjectGraph()' 37.58640000037849ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:39.056Z - [WATCHER]: services/review-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:39.070Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.472Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.472Z - [REQUEST]: services/review-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.472Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.477Z - Time taken for 'hash changed files from watcher' 0.48300000093877316ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.501Z - Time taken for 'build-project-configs' 19.433400001376867ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.542Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.543Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.543Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:45.544Z - Time taken for 'total execution time for createProjectGraph()' 25.694099999964237ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:50.618Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:50.620Z - [WATCHER]: services/review-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.082Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.082Z - [REQUEST]: services/review-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.082Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.132Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.152Z - Time taken for 'hash changed files from watcher' 60.02129999920726ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.177Z - Time taken for 'build-project-configs' 34.94209999963641ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.252Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.253Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.253Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:04:57.253Z - Time taken for 'total execution time for createProjectGraph()' 52.14089999906719ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:20.430Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:20.432Z - [WATCHER]: 0 file(s) created or restored, 1 file(s) modified, 2 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:20.494Z - [WATCHER]: services/notification-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:20.495Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.847Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.847Z - [REQUEST]: services/notification-service/go.mod,services/notification-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.847Z - [REQUEST]: services/notification-service/internal/desktop.ini,services/notification-service/cmd/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.850Z - Time taken for 'hash changed files from watcher' 0.8149999994784594ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.877Z - Time taken for 'build-project-configs' 17.687400000169873ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.907Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.908Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.908Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:26.908Z - Time taken for 'total execution time for createProjectGraph()' 23.79590000025928ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:27.854Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:27.885Z - [WATCHER]: services/order-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.287Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.287Z - [REQUEST]: services/order-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.287Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.294Z - Time taken for 'hash changed files from watcher' 0.4315000008791685ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.323Z - Time taken for 'build-project-configs' 24.942500000819564ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.378Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.381Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.381Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:34.381Z - Time taken for 'total execution time for createProjectGraph()' 40.691700000315905ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:38.887Z - [WATCHER]: services/order-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:38.887Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.299Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.299Z - [REQUEST]: services/order-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.299Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.304Z - Time taken for 'hash changed files from watcher' 0.3410999998450279ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.328Z - Time taken for 'build-project-configs' 17.692299999296665ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.400Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.402Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.402Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.403Z - Time taken for 'total execution time for createProjectGraph()' 61.14000000059605ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:05:45.992Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:26.078Z - [WATCHER]: 0 file(s) created or restored, 1 file(s) modified, 3 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:26.079Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:26.133Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:26.134Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.483Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.483Z - [REQUEST]: services/review-service/cmd/main.go,services/review-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.483Z - [REQUEST]: services/order-service/internal/desktop.ini,services/order-service/cmd/desktop.ini,services/review-service/cmd/desktop.ini,services/review-service/internal/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.487Z - Time taken for 'hash changed files from watcher' 0.6533000003546476ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.514Z - Time taken for 'build-project-configs' 18.95299999974668ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.560Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.561Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.563Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:32.563Z - Time taken for 'total execution time for createProjectGraph()' 34.55360000021756ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:40.133Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:40.147Z - [WATCHER]: services/analytics-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:40.202Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:40.207Z - [WATCHER]: services/analytics-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.559Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.559Z - [REQUEST]: services/analytics-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.559Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.566Z - Time taken for 'hash changed files from watcher' 0.3410999998450279ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.590Z - Time taken for 'build-project-configs' 21.607699999585748ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.632Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.633Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.633Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:46.633Z - Time taken for 'total execution time for createProjectGraph()' 26.476700000464916ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:51.662Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:51.671Z - [WATCHER]: services/analytics-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.081Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.081Z - [REQUEST]: services/analytics-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.081Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.083Z - Time taken for 'hash changed files from watcher' 1.6232999991625547ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.107Z - Time taken for 'build-project-configs' 17.394900001585484ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.156Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.157Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.157Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:58.157Z - Time taken for 'total execution time for createProjectGraph()' 33.317500000819564ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:06:59.577Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:34.064Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:34.068Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.485Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.485Z - [REQUEST]: services/order-service/go.mod,services/order-service/cmd/main.go,services/payment-service/go.sum,services/payment-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.485Z - [REQUEST]: services/analytics-service/cmd/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.490Z - Time taken for 'hash changed files from watcher' 0.5471000000834465ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.513Z - Time taken for 'build-project-configs' 20.545700000599027ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.567Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.568Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.568Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:40.568Z - Time taken for 'total execution time for createProjectGraph()' 37.43040000088513ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:48.599Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:48.602Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.003Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.003Z - [REQUEST]: services/product-service/go.mod,services/product-service/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.003Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.008Z - Time taken for 'hash changed files from watcher' 0.46010000072419643ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.029Z - Time taken for 'build-project-configs' 17.505200000479817ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.148Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.149Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.149Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:07:55.149Z - Time taken for 'total execution time for createProjectGraph()' 65.28810000047088ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:09.586Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:09.586Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:16.086Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:16.087Z - [REQUEST]: services/notification-service/go.sum,services/notification-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:16.087Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:16.149Z - Time taken for 'hash changed files from watcher' 45.14409999921918ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:16.204Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:16.204Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:16.205Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:16.205Z - Time taken for 'total execution time for createProjectGraph()' 33.79319999925792ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:22.458Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:22.465Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.861Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.861Z - [REQUEST]: services/review-service/go.sum,services/review-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.861Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.863Z - Time taken for 'hash changed files from watcher' 0.3960999995470047ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.893Z - Time taken for 'build-project-configs' 19.152300000190735ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.945Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.946Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.946Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:28.946Z - Time taken for 'total execution time for createProjectGraph()' 40.375300001353025ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:34.546Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:34.548Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:40.951Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:40.951Z - [REQUEST]: services/order-service/go.sum,services/order-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:40.951Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:40.954Z - Time taken for 'hash changed files from watcher' 0.549600001424551ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:40.991Z - Time taken for 'build-project-configs' 20.130899999290705ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:41.118Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:41.120Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:41.123Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:41.124Z - Time taken for 'total execution time for createProjectGraph()' 44.697699999436736ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:50.085Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:50.087Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:50.152Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:50.154Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.489Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.489Z - [REQUEST]: services/analytics-service/go.mod,services/payment-service/go.sum,services/payment-service/go.mod,services/analytics-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.489Z - [REQUEST]: services/analytics-service/internal/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.492Z - Time taken for 'hash changed files from watcher' 0.5779999997466803ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.516Z - Time taken for 'build-project-configs' 18.618400000035763ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.573Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.576Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.576Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.577Z - Time taken for 'total execution time for createProjectGraph()' 32.23149999976158ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.955Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:08:56.957Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.361Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.361Z - [REQUEST]: services/analytics-service/go.sum,services/analytics-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.362Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.372Z - Time taken for 'hash changed files from watcher' 0.5266000013798475ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.401Z - Time taken for 'build-project-configs' 22.99430000036955ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.453Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.454Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.455Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:03.455Z - Time taken for 'total execution time for createProjectGraph()' 36.50149999931455ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:58.420Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:58.434Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:58.483Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:09:58.487Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.837Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.837Z - [REQUEST]: services/review-service/go.sum,services/product-service/go.sum,services/notification-service/go.mod,services/notification-service/go.sum,services/order-service/go.sum,services/product-service/go.mod,services/order-service/go.mod,services/review-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.837Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.840Z - Time taken for 'hash changed files from watcher' 0.8557999990880489ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.867Z - Time taken for 'build-project-configs' 22.42200000025332ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.918Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.919Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.919Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:10:04.919Z - Time taken for 'total execution time for createProjectGraph()' 35.29910000041127ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:00.464Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:00.467Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.883Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.883Z - [REQUEST]: services/analytics-service/go.sum,services/analytics-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.884Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.887Z - Time taken for 'hash changed files from watcher' 0.6122999992221594ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.909Z - Time taken for 'build-project-configs' 16.017100000754ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.944Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.945Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.945Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:11:06.945Z - Time taken for 'total execution time for createProjectGraph()' 26.97120000049472ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:04.197Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:04.200Z - [WATCHER]: services/payment-service/internal/repository/payment_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.607Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.607Z - [REQUEST]: services/payment-service/internal/repository/payment_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.607Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.609Z - Time taken for 'hash changed files from watcher' 0.35500000044703484ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.637Z - Time taken for 'build-project-configs' 20.526499999687076ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.681Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.683Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.684Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:12:10.684Z - Time taken for 'total execution time for createProjectGraph()' 28.497899999842048ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:00.847Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:00.850Z - [WATCHER]: services/payment-service/internal/repository/payment_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.252Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.252Z - [REQUEST]: services/payment-service/internal/repository/payment_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.252Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.257Z - Time taken for 'hash changed files from watcher' 0.4791000001132488ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.280Z - Time taken for 'build-project-configs' 22.499500000849366ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.321Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.322Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.322Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:13:07.322Z - Time taken for 'total execution time for createProjectGraph()' 26.70189999975264ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:19.569Z - [WATCHER]: services/service-catalog/internal/models/service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:19.570Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:21.616Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:21.620Z - [WATCHER]: services/service-catalog/internal/models/service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:25.976Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:25.976Z - [REQUEST]: services/service-catalog/internal/models/service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:25.976Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:25.980Z - Time taken for 'hash changed files from watcher' 0.7749000005424023ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:26.006Z - Time taken for 'build-project-configs' 20.91799999959767ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:26.065Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:26.066Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:26.066Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:26.066Z - Time taken for 'total execution time for createProjectGraph()' 31.093800000846386ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:45.036Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:45.038Z - [WATCHER]: services/service-catalog/internal/models/service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.447Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.448Z - [REQUEST]: services/service-catalog/internal/models/service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.448Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.454Z - Time taken for 'hash changed files from watcher' 0.5585999991744757ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.476Z - Time taken for 'build-project-configs' 20.19090000167489ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.523Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.523Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.524Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:18:51.524Z - Time taken for 'total execution time for createProjectGraph()' 27.144700000062585ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:16.741Z - [WATCHER]: services/service-catalog/internal/repository/variant_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:16.743Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.146Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.146Z - [REQUEST]: services/service-catalog/internal/repository/variant_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.146Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.159Z - Time taken for 'hash changed files from watcher' 0.4138999991118908ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.174Z - Time taken for 'build-project-configs' 17.8766999989748ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.220Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.221Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.221Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:23.222Z - Time taken for 'total execution time for createProjectGraph()' 28.631000000983477ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:51.688Z - [WATCHER]: services/service-catalog/internal/services/variant_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:51.695Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.093Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.093Z - [REQUEST]: services/service-catalog/internal/services/variant_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.093Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.095Z - Time taken for 'hash changed files from watcher' 0.3473000004887581ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.120Z - Time taken for 'build-project-configs' 20.09739999845624ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.159Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.160Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.160Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:19:58.160Z - Time taken for 'total execution time for createProjectGraph()' 24.922399999573827ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:28.932Z - [WATCHER]: services/service-catalog/internal/handlers/variant_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:28.932Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:29.323Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:29.330Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.351Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.351Z - [REQUEST]: services/service-catalog/internal/handlers/variant_handler.go,services/service-catalog/internal/models/service.go,services/service-catalog/internal/repository/variant_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.351Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.420Z - Time taken for 'hash changed files from watcher' 4.5181000009179115ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.517Z - Time taken for 'build-project-configs' 71.56130000017583ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.608Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.610Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.610Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:35.610Z - Time taken for 'total execution time for createProjectGraph()' 131.05940000154078ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:41.466Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:41.467Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.879Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.879Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.879Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.886Z - Time taken for 'hash changed files from watcher' 1.1745000015944242ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.913Z - Time taken for 'build-project-configs' 20.03819999843836ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.957Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.958Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.959Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:47.959Z - Time taken for 'total execution time for createProjectGraph()' 36.503900000825524ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:51.120Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:51.121Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.528Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.528Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.528Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.534Z - Time taken for 'hash changed files from watcher' 0.33980000019073486ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.558Z - Time taken for 'build-project-configs' 19.800999999046326ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.603Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.604Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.604Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:20:57.605Z - Time taken for 'total execution time for createProjectGraph()' 31.70879999920726ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:00.380Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:00.381Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.789Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.790Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.790Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.795Z - Time taken for 'hash changed files from watcher' 0.4097000006586313ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.820Z - Time taken for 'build-project-configs' 23.573700001463294ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.855Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.856Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.856Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:06.856Z - Time taken for 'total execution time for createProjectGraph()' 22.90240000002086ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:12.070Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:12.072Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.486Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.486Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.486Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.490Z - Time taken for 'hash changed files from watcher' 0.3601999990642071ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.508Z - Time taken for 'build-project-configs' 15.450000001117587ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.554Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.555Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.555Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:18.555Z - Time taken for 'total execution time for createProjectGraph()' 29.47840000130236ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:23.246Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:23.247Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.650Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.650Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.650Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.655Z - Time taken for 'hash changed files from watcher' 0.4331999998539686ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.679Z - Time taken for 'build-project-configs' 19.84349999949336ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.729Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.731Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.732Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:29.732Z - Time taken for 'total execution time for createProjectGraph()' 31.942999999970198ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:36.967Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:36.968Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:37.332Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:37.342Z - [WATCHER]: services/service-catalog/internal/services/service_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.373Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.373Z - [REQUEST]: services/service-catalog/internal/services/variant_service.go,services/service-catalog/internal/handlers/variant_handler.go,services/service-catalog/internal/services/service_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.373Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.380Z - Time taken for 'hash changed files from watcher' 1.4899999983608723ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.402Z - Time taken for 'build-project-configs' 20.5437000002712ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.443Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.444Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.445Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:43.446Z - Time taken for 'total execution time for createProjectGraph()' 24.502299999818206ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:52.818Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:52.841Z - [WATCHER]: services/service-catalog/internal/services/service_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.256Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.256Z - [REQUEST]: services/service-catalog/internal/services/service_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.256Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.263Z - Time taken for 'hash changed files from watcher' 0.3111000005155802ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.287Z - Time taken for 'build-project-configs' 22.846099998801947ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.322Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.323Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.323Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:21:59.323Z - Time taken for 'total execution time for createProjectGraph()' 23.886299999430776ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:04.280Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:04.281Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.684Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.684Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.684Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.689Z - Time taken for 'hash changed files from watcher' 0.42159999907016754ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.711Z - Time taken for 'build-project-configs' 18.667399998754263ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.764Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.764Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.765Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:10.765Z - Time taken for 'total execution time for createProjectGraph()' 41.29320000112057ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:16.507Z - [WATCHER]: services/service-catalog/internal/services/service_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:16.507Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.924Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.924Z - [REQUEST]: services/service-catalog/internal/services/service_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.924Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.929Z - Time taken for 'hash changed files from watcher' 0.3204000014811754ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.952Z - Time taken for 'build-project-configs' 17.812300000339746ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.989Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.989Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.989Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:22:22.990Z - Time taken for 'total execution time for createProjectGraph()' 26.556099999696016ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:32.000Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:32.002Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.415Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.416Z - [REQUEST]: services/service-catalog/cmd/main.go,services/service-catalog/internal/services/service_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.416Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.421Z - Time taken for 'hash changed files from watcher' 1.2532000001519918ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.458Z - Time taken for 'build-project-configs' 31.370100000873208ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.493Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.494Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.495Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:23:38.495Z - Time taken for 'total execution time for createProjectGraph()' 28.00510000064969ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:01.508Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:01.508Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:02.871Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:02.871Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:07.921Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:07.921Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:07.921Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:07.934Z - Time taken for 'hash changed files from watcher' 1.4785999990999699ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:07.988Z - Time taken for 'build-project-configs' 51.263199999928474ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:08.041Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:08.041Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:08.042Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:08.042Z - Time taken for 'total execution time for createProjectGraph()' 38.63700000010431ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:18.015Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:18.016Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.426Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.426Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.426Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.430Z - Time taken for 'hash changed files from watcher' 0.43070000037550926ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.453Z - Time taken for 'build-project-configs' 20.082200000062585ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.501Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.502Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.502Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:25:24.502Z - Time taken for 'total execution time for createProjectGraph()' 33.940399998798966ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:09.730Z - [WATCHER]: services/service-catalog/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:09.730Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.132Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.133Z - [REQUEST]: services/service-catalog/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.133Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.137Z - Time taken for 'hash changed files from watcher' 0.49829999916255474ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.164Z - Time taken for 'build-project-configs' 20.81790000014007ms
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.220Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.221Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.221Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T07:26:16.221Z - Time taken for 'total execution time for createProjectGraph()' 31.330299999564886ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:44.784Z - [WATCHER]: database/migrations/006_add_variants_and_addons.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:44.813Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:46.455Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:46.465Z - [WATCHER]: database/migrations/006_add_variants_and_addons.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.267Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.268Z - [REQUEST]: database/migrations/006_add_variants_and_addons.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.268Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.358Z - Time taken for 'hash changed files from watcher' 33.76539999991655ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.471Z - Time taken for 'build-project-configs' 115.16070000082254ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.534Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.535Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.535Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:25:51.538Z - Time taken for 'total execution time for createProjectGraph()' 49.114500001072884ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:47.347Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:47.349Z - [WATCHER]: database/migrate.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:48.782Z - [WATCHER]: database/migrate.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:48.783Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.759Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.759Z - [REQUEST]: database/migrate.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.759Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.767Z - Time taken for 'hash changed files from watcher' 0.346000000834465ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.802Z - Time taken for 'build-project-configs' 26.852699998766184ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.860Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.861Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.861Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:53.861Z - Time taken for 'total execution time for createProjectGraph()' 41.98970000073314ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:54.603Z - [WATCHER]: database/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:27:54.606Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.015Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.015Z - [REQUEST]: database/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.015Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.024Z - Time taken for 'hash changed files from watcher' 0.3410000018775463ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.060Z - Time taken for 'build-project-configs' 24.52710000053048ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.118Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.118Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.119Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:01.119Z - Time taken for 'total execution time for createProjectGraph()' 42.160199999809265ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:33.725Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:33.727Z - [WATCHER]: database/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.141Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.141Z - [REQUEST]: database/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.141Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.147Z - Time taken for 'hash changed files from watcher' 0.48829999938607216ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.178Z - Time taken for 'build-project-configs' 22.356699999421835ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.261Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.262Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.262Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:28:40.262Z - Time taken for 'total execution time for createProjectGraph()' 35.48429999873042ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:05.823Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:05.824Z - [WATCHER]: database/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.238Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.238Z - [REQUEST]: database/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.238Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.271Z - Time taken for 'hash changed files from watcher' 0.3343000002205372ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.314Z - Time taken for 'build-project-configs' 48.08089999854565ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.383Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.383Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.383Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:12.384Z - Time taken for 'total execution time for createProjectGraph()' 55.50659999996424ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:31.932Z - [WATCHER]: services/product-service/internal/models/product.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:31.933Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.345Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.345Z - [REQUEST]: services/product-service/internal/models/product.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.345Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.352Z - Time taken for 'hash changed files from watcher' 0.4904000014066696ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.377Z - Time taken for 'build-project-configs' 17.438200000673532ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.427Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.428Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.429Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:38.429Z - Time taken for 'total execution time for createProjectGraph()' 37.29970000311732ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:54.482Z - [WATCHER]: services/product-service/internal/models/product.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:29:54.482Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.900Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.900Z - [REQUEST]: services/product-service/internal/models/product.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.900Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.905Z - Time taken for 'hash changed files from watcher' 0.43539999797940254ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.924Z - Time taken for 'build-project-configs' 17.83339999988675ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.968Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.968Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.969Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:00.969Z - Time taken for 'total execution time for createProjectGraph()' 25.63340000063181ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:10.539Z - [WATCHER]: database/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:10.539Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:16.940Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:16.940Z - [REQUEST]: database/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:16.940Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:16.946Z - Time taken for 'hash changed files from watcher' 0.5465000011026859ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:16.965Z - Time taken for 'build-project-configs' 16.665400002151728ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:17.012Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:17.013Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:17.013Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:17.014Z - Time taken for 'total execution time for createProjectGraph()' 29.68080000206828ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:29.248Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:29.263Z - [WATCHER]: services/product-service/internal/repository/product_variant_repository.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.680Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.681Z - [REQUEST]: services/product-service/internal/repository/product_variant_repository.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.681Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.684Z - Time taken for 'hash changed files from watcher' 0.687100000679493ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.709Z - Time taken for 'build-project-configs' 20.50569999963045ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.751Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.752Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.752Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:35.753Z - Time taken for 'total execution time for createProjectGraph()' 23.436700001358986ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:55.254Z - [WATCHER]: services/product-service/internal/services/product_variant_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:55.255Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:55.280Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:30:55.282Z - [WATCHER]: services/product-service/internal/services/product_variant_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.668Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.668Z - [REQUEST]: services/product-service/internal/services/product_variant_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.668Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.671Z - Time taken for 'hash changed files from watcher' 0.5811000019311905ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.702Z - Time taken for 'build-project-configs' 20.749599996954203ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.756Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.757Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.758Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:01.758Z - Time taken for 'total execution time for createProjectGraph()' 44.86040000244975ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:13.206Z - [WATCHER]: services/product-service/internal/models/product.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:13.206Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.622Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.622Z - [REQUEST]: services/product-service/internal/models/product.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.622Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.627Z - Time taken for 'hash changed files from watcher' 0.5251999981701374ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.653Z - Time taken for 'build-project-configs' 21.16440000012517ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.691Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.692Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.692Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:19.692Z - Time taken for 'total execution time for createProjectGraph()' 23.721499998122454ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:35.009Z - [WATCHER]: services/product-service/internal/handlers/product_variant_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:35.009Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.418Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.419Z - [REQUEST]: services/product-service/internal/handlers/product_variant_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.419Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.422Z - Time taken for 'hash changed files from watcher' 0.31550000235438347ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.445Z - Time taken for 'build-project-configs' 18.20549999922514ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.489Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.490Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.490Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:41.490Z - Time taken for 'total execution time for createProjectGraph()' 29.91899999976158ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:51.375Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:51.376Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.787Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.787Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.787Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.802Z - Time taken for 'hash changed files from watcher' 0.47929999977350235ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.825Z - Time taken for 'build-project-configs' 30.53429999947548ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.862Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.863Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.863Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:31:57.863Z - Time taken for 'total execution time for createProjectGraph()' 22.588100001215935ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:16.779Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:16.780Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:21.720Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:21.723Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.192Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.192Z - [REQUEST]: services/product-service/internal/services/product_variant_service.go,services/product-service/internal/repository/product_variant_repository.go,services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.192Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.196Z - Time taken for 'hash changed files from watcher' 1.778599999845028ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.220Z - Time taken for 'build-project-configs' 17.992300000041723ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.271Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.272Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.272Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:23.272Z - Time taken for 'total execution time for createProjectGraph()' 35.52779999747872ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:46.828Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:46.865Z - [WATCHER]: services/product-service/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.266Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.266Z - [REQUEST]: services/product-service/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.266Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.271Z - Time taken for 'hash changed files from watcher' 0.46229999884963036ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.299Z - Time taken for 'build-project-configs' 23.221999999135733ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.338Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.338Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.339Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:32:53.339Z - Time taken for 'total execution time for createProjectGraph()' 24.945599999278784ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:10.188Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:10.188Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.599Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.599Z - [REQUEST]: services/product-service/go.mod,services/product-service/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.599Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.602Z - Time taken for 'hash changed files from watcher' 0.5812999978661537ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.628Z - Time taken for 'build-project-configs' 20.490600001066923ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.667Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.668Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.668Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:16.668Z - Time taken for 'total execution time for createProjectGraph()' 22.8885000012815ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:21.556Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:21.557Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:27.971Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:27.971Z - [REQUEST]: services/product-service/cmd/main.go,services/product-service/internal/handlers/product_variant_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:27.971Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:27.974Z - Time taken for 'hash changed files from watcher' 0.566100001335144ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:27.996Z - Time taken for 'build-project-configs' 16.88729999959469ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:28.030Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:28.031Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:28.031Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:33:28.031Z - Time taken for 'total execution time for createProjectGraph()' 21.798200000077486ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:07.058Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:07.080Z - [WATCHER]: services/product-service/internal/services/product_variant_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.493Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.493Z - [REQUEST]: services/product-service/internal/services/product_variant_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.493Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.495Z - Time taken for 'hash changed files from watcher' 0.37859999760985374ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.514Z - Time taken for 'build-project-configs' 15.13179999962449ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.553Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.553Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.553Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:13.554Z - Time taken for 'total execution time for createProjectGraph()' 25.360800001770258ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:17.419Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:17.420Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:18.399Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:18.400Z - [WATCHER]: services/product-service/internal/services/product_variant_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.826Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.826Z - [REQUEST]: services/product-service/go.sum,services/product-service/go.mod,services/product-service/internal/services/product_variant_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.826Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.829Z - Time taken for 'hash changed files from watcher' 0.6101000010967255ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.855Z - Time taken for 'build-project-configs' 21.375700000673532ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.898Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.899Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.899Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:23.900Z - Time taken for 'total execution time for createProjectGraph()' 27.131299998611212ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:32.309Z - [WATCHER]: services/product-service/internal/services/product_variant_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:32.310Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.724Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.724Z - [REQUEST]: services/product-service/internal/services/product_variant_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.724Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.732Z - Time taken for 'hash changed files from watcher' 0.5084999985992908ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.750Z - Time taken for 'build-project-configs' 19.109499998390675ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.787Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.788Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.788Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:38.788Z - Time taken for 'total execution time for createProjectGraph()' 24.112500000745058ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:45.470Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:45.471Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.880Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.880Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.880Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.884Z - Time taken for 'hash changed files from watcher' 0.30729999765753746ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.912Z - Time taken for 'build-project-configs' 20.701899997889996ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.947Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.948Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.948Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:34:51.948Z - Time taken for 'total execution time for createProjectGraph()' 25.09070000052452ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:15.528Z - [WATCHER]: services/product-service/internal/services/product_variant_service.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:15.528Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:21.932Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:21.932Z - [REQUEST]: services/product-service/internal/services/product_variant_service.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:21.932Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:21.936Z - Time taken for 'hash changed files from watcher' 0.29349999874830246ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:21.956Z - Time taken for 'build-project-configs' 16.682199999690056ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:22.000Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:22.001Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:22.001Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:35:22.002Z - Time taken for 'total execution time for createProjectGraph()' 29.240299999713898ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:09.489Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:13.204Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:13.205Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.613Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.613Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.613Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.618Z - Time taken for 'hash changed files from watcher' 0.4174000024795532ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.640Z - Time taken for 'build-project-configs' 17.703499998897314ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.684Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.685Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.685Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:36:19.685Z - Time taken for 'total execution time for createProjectGraph()' 24.86379999667406ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:37:20.987Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:05.186Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:05.188Z - [WATCHER]: test-apis.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:06.614Z - [WATCHER]: test-apis.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:06.614Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.594Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.595Z - [REQUEST]: test-apis.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.595Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.608Z - Time taken for 'hash changed files from watcher' 0.8572999984025955ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.630Z - Time taken for 'build-project-configs' 22.641699999570847ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.678Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.679Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.679Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:43:11.679Z - Time taken for 'total execution time for createProjectGraph()' 22.534099999815226ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:49.512Z - [WATCHER]: test-user-service.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:49.512Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:50.853Z - [WATCHER]: test-user-service.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:50.854Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:55.915Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:55.915Z - [REQUEST]: test-user-service.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:55.915Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:55.934Z - Time taken for 'hash changed files from watcher' 1.0718000009655952ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:55.996Z - Time taken for 'build-project-configs' 56.060599997639656ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:56.054Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:56.055Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:56.055Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:44:56.056Z - Time taken for 'total execution time for createProjectGraph()' 51.175400000065565ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:47.655Z - [WATCHER]: test-user-service.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:47.655Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:53.415Z - [WATCHER]: test-user-service.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:53.416Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.059Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.059Z - [REQUEST]: test-user-service.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.060Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.068Z - Time taken for 'hash changed files from watcher' 0.6966000013053417ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.101Z - Time taken for 'build-project-configs' 25.973099999129772ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.140Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.141Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.141Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:45:54.141Z - Time taken for 'total execution time for createProjectGraph()' 33.148899998515844ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:02.640Z - [WATCHER]: test-user-service.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:02.641Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.042Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.042Z - [REQUEST]: test-user-service.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.042Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.046Z - Time taken for 'hash changed files from watcher' 0.37150000035762787ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.074Z - Time taken for 'build-project-configs' 18.298599999397993ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.118Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.119Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.119Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:46:09.119Z - Time taken for 'total execution time for createProjectGraph()' 36.148299999535084ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:19.192Z - [WATCHER]: api-status-report.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:19.193Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.600Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.600Z - [REQUEST]: api-status-report.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.600Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.604Z - Time taken for 'hash changed files from watcher' 1.7969999983906746ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.625Z - Time taken for 'build-project-configs' 18.286800000816584ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.683Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.684Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.684Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:25.685Z - Time taken for 'total execution time for createProjectGraph()' 39.64249999821186ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:56.994Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:47:56.995Z - [WATCHER]: test-user-service.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.409Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.409Z - [REQUEST]: test-user-service.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.409Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.412Z - Time taken for 'hash changed files from watcher' 1.0581000000238419ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.437Z - Time taken for 'build-project-configs' 17.96389999985695ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.470Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.471Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.471Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:03.471Z - Time taken for 'total execution time for createProjectGraph()' 22.91560000181198ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:47.238Z - [WATCHER]: api-status-report.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:47.238Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.647Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.647Z - [REQUEST]: api-status-report.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.647Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.651Z - Time taken for 'hash changed files from watcher' 0.5458000004291534ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.670Z - Time taken for 'build-project-configs' 16.309799998998642ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.713Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.713Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.713Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:48:53.714Z - Time taken for 'total execution time for createProjectGraph()' 27.09690000116825ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:02.517Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:02.519Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:03.780Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:03.781Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:08.932Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:08.933Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:08.933Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:08.940Z - Time taken for 'hash changed files from watcher' 2.4481000006198883ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:09.026Z - Time taken for 'build-project-configs' 50.572899997234344ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:09.070Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:09.071Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:09.071Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:09.071Z - Time taken for 'total execution time for createProjectGraph()' 30.874800000339746ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:37.048Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:37.049Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.452Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.452Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.452Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.459Z - Time taken for 'hash changed files from watcher' 0.4497999995946884ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.488Z - Time taken for 'build-project-configs' 26.73760000243783ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.531Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.532Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.532Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:43.532Z - Time taken for 'total execution time for createProjectGraph()' 28.910699997097254ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:49.710Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:49.710Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.123Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.126Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.126Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.173Z - Time taken for 'hash changed files from watcher' 0.7430999986827374ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.190Z - Time taken for 'build-project-configs' 43.47110000252724ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.264Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.265Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.265Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:51:56.265Z - Time taken for 'total execution time for createProjectGraph()' 46.64970000088215ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:13.199Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:13.200Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.603Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.603Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.603Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.608Z - Time taken for 'hash changed files from watcher' 0.3968000002205372ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.628Z - Time taken for 'build-project-configs' 18.825300000607967ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.672Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.672Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.672Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:19.673Z - Time taken for 'total execution time for createProjectGraph()' 31.453499998897314ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:24.834Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:24.835Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.239Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.239Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.239Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.242Z - Time taken for 'hash changed files from watcher' 0.36339999735355377ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.264Z - Time taken for 'build-project-configs' 18.14919999986887ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.307Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.308Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.308Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:31.308Z - Time taken for 'total execution time for createProjectGraph()' 25.752500001341105ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:44.071Z - [WATCHER]: services/user-service/internal/handlers/user_handler.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:44.071Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.476Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.477Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.477Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.486Z - Time taken for 'hash changed files from watcher' 0.43059999868273735ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.507Z - Time taken for 'build-project-configs' 18.571199998259544ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.549Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.550Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.550Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:52:50.551Z - Time taken for 'total execution time for createProjectGraph()' 27.05849999934435ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:04.501Z - [WATCHER]: services/user-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:04.503Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.907Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.907Z - [REQUEST]: services/user-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.907Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.910Z - Time taken for 'hash changed files from watcher' 0.4023999981582165ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.944Z - Time taken for 'build-project-configs' 30.42280000075698ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.985Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.986Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.987Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:10.987Z - Time taken for 'total execution time for createProjectGraph()' 26.014500003308058ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:53:50.831Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:20.883Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:20.883Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:22.710Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.290Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.291Z - [REQUEST]: services/user-service/internal/handlers/user_handler.go,services/user-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.291Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.296Z - Time taken for 'hash changed files from watcher' 0.9314999990165234ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.323Z - Time taken for 'build-project-configs' 22.03490000218153ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.362Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.363Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.363Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:54:27.363Z - Time taken for 'total execution time for createProjectGraph()' 25.927200000733137ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:12.868Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:12.871Z - [WATCHER]: test-database-apis.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:14.253Z - [WATCHER]: test-database-apis.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:14.253Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.282Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.282Z - [REQUEST]: test-database-apis.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.282Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.290Z - Time taken for 'hash changed files from watcher' 0.7531000003218651ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.348Z - Time taken for 'build-project-configs' 53.26469999924302ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.390Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.391Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.392Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:56:19.392Z - Time taken for 'total execution time for createProjectGraph()' 33.93699999898672ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:21.205Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:21.223Z - [WATCHER]: database/inspect-db.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:21.319Z - [WATCHER]: database/inspect-db.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:21.319Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:22.904Z - [WATCHER]: database/inspect-db.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:22.905Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.638Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.639Z - [REQUEST]: database/inspect-db.go
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.639Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.655Z - Time taken for 'hash changed files from watcher' 3.921799998730421ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.674Z - Time taken for 'build-project-configs' 26.62900000065565ms
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.709Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.710Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.710Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T10:58:27.711Z - Time taken for 'total execution time for createProjectGraph()' 20.671000000089407ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:03.802Z - [WATCHER]: verify-database-setup.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:03.812Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:05.558Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:05.560Z - [WATCHER]: verify-database-setup.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.232Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.233Z - [REQUEST]: verify-database-setup.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.233Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.255Z - Time taken for 'hash changed files from watcher' 4.629999998956919ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.720Z - Time taken for 'build-project-configs' 235.61840000003576ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.808Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.809Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.809Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:39:10.809Z - Time taken for 'total execution time for createProjectGraph()' 208.3615000024438ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:30.636Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:30.749Z - [WATCHER]: check-db-setup.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:32.106Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:32.108Z - [WATCHER]: check-db-setup.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:37.277Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:37.277Z - [REQUEST]: check-db-setup.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:37.277Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:37.399Z - Time taken for 'hash changed files from watcher' 1.7561999969184399ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:37.465Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:37.466Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:37.466Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:40:37.466Z - Time taken for 'total execution time for createProjectGraph()' 54.58500000089407ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:42:58.619Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:42:58.621Z - [WATCHER]: .env.shared was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:00.001Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:00.005Z - [WATCHER]: .env.shared was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.060Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.060Z - [REQUEST]: .env.shared
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.060Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.064Z - Time taken for 'hash changed files from watcher' 3.841499999165535ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.094Z - Time taken for 'build-project-configs' 20.716400001198053ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.133Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.134Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.135Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:05.135Z - Time taken for 'total execution time for createProjectGraph()' 28.379099998623133ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:10.328Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:31.798Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:31.801Z - [WATCHER]: services/user-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.210Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.210Z - [REQUEST]: services/user-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.210Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.214Z - Time taken for 'hash changed files from watcher' 0.5832000002264977ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.238Z - Time taken for 'build-project-configs' 21.11989999935031ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.281Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.282Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.282Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:43:38.282Z - Time taken for 'total execution time for createProjectGraph()' 27.95320000126958ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:44:40.680Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:44:40.682Z - [WATCHER]: config/database.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:44:51.432Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:44:51.435Z - [REQUEST]: config/database.env
[NX v21.2.1 Daemon Server] - 2025-06-26T11:44:51.435Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:44:55.571Z - Time taken for 'hash changed files from watcher' 0.4299999997019768ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:44:55.621Z - Time taken for 'build-project-configs' 38.395600002259016ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:01.428Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:01.429Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:01.429Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:01.429Z - Time taken for 'total execution time for createProjectGraph()' 1744.2818000018597ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:21.636Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:21.884Z - [WATCHER]: config/shared.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:28.318Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:28.318Z - [REQUEST]: config/shared.env
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:28.318Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:28.724Z - Time taken for 'hash changed files from watcher' 0.6257000006735325ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:28.817Z - Time taken for 'build-project-configs' 170.70199999958277ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:29.133Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:29.135Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:29.135Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:29.135Z - Time taken for 'total execution time for createProjectGraph()' 255.35649999976158ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:30.454Z - [WATCHER]: config/services.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:30.457Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.857Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.857Z - [REQUEST]: config/services.env
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.857Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.864Z - Time taken for 'hash changed files from watcher' 0.48270000144839287ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.886Z - Time taken for 'build-project-configs' 19.628199998289347ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.941Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.942Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.942Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:36.942Z - Time taken for 'total execution time for createProjectGraph()' 30.74890000000596ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:51.312Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:51.315Z - [WATCHER]: shared/config/loader.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.729Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.729Z - [REQUEST]: shared/config/loader.go
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.729Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.732Z - Time taken for 'hash changed files from watcher' 0.7140999995172024ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.752Z - Time taken for 'build-project-configs' 15.425899997353554ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.805Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.806Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.807Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:45:57.807Z - Time taken for 'total execution time for createProjectGraph()' 38.56989999860525ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:14.160Z - [WATCHER]: test-config-system.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:14.160Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.572Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.572Z - [REQUEST]: test-config-system.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.572Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.575Z - Time taken for 'hash changed files from watcher' 0.4302999973297119ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.615Z - Time taken for 'build-project-configs' 34.02290000021458ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.665Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.666Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.666Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:20.666Z - Time taken for 'total execution time for createProjectGraph()' 31.204799998551607ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:34.235Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:34.237Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:34.308Z - [WATCHER]: services/user-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:34.309Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.652Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.652Z - [REQUEST]: config/database.env,config/shared.env,services/user-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.652Z - [REQUEST]: config/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.656Z - Time taken for 'hash changed files from watcher' 2.3146000020205975ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.677Z - Time taken for 'build-project-configs' 15.775800000876188ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.715Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.716Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.717Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:46:40.717Z - Time taken for 'total execution time for createProjectGraph()' 25.887499999254942ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:31.109Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:31.116Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 2 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:31.173Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:31.174Z - [WATCHER]: shared/config/loader.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.524Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.524Z - [REQUEST]: config/services.env,test-config-system.ps1,shared/config/loader.go
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.526Z - [REQUEST]: shared/desktop.ini,shared/config/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.532Z - Time taken for 'hash changed files from watcher' 3.033399999141693ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.560Z - Time taken for 'build-project-configs' 20.113400001078844ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.619Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.620Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.620Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:37.620Z - Time taken for 'total execution time for createProjectGraph()' 41.65109999850392ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:51.541Z - [WATCHER]: test-config-system.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:51.541Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:57.956Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:57.956Z - [REQUEST]: test-config-system.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:57.956Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:57.960Z - Time taken for 'hash changed files from watcher' 0.2824999988079071ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:57.981Z - Time taken for 'build-project-configs' 16.1875ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:58.027Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:58.028Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:58.028Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:47:58.028Z - Time taken for 'total execution time for createProjectGraph()' 25.993400000035763ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:30.875Z - [WATCHER]: test-config-system.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:30.875Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.289Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.289Z - [REQUEST]: test-config-system.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.289Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.292Z - Time taken for 'hash changed files from watcher' 0.4285999983549118ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.325Z - Time taken for 'build-project-configs' 26.675499998033047ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.372Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.373Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.373Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:37.373Z - Time taken for 'total execution time for createProjectGraph()' 29.74159999936819ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:44.547Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:44.551Z - [WATCHER]: show-config-structure.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:50.966Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:50.967Z - [REQUEST]: show-config-structure.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:50.967Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:50.970Z - Time taken for 'hash changed files from watcher' 0.9630000032484531ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:51.007Z - Time taken for 'build-project-configs' 23.23370000347495ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:51.100Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:51.101Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:51.102Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:49:51.102Z - Time taken for 'total execution time for createProjectGraph()' 63.90210000053048ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:01.101Z - [WATCHER]: show-config-structure.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:01.101Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.509Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.509Z - [REQUEST]: show-config-structure.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.509Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.518Z - Time taken for 'hash changed files from watcher' 0.846900001168251ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.551Z - Time taken for 'build-project-configs' 30.682700000703335ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.591Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.592Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.592Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:52:07.593Z - Time taken for 'total execution time for createProjectGraph()' 27.573300000280142ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:37.244Z - [WATCHER]: find-duplicate-db-connections.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:37.258Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:38.623Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:38.625Z - [WATCHER]: find-duplicate-db-connections.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.654Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.654Z - [REQUEST]: find-duplicate-db-connections.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.654Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.671Z - Time taken for 'hash changed files from watcher' 1.384699996560812ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.710Z - Time taken for 'build-project-configs' 36.996300000697374ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.761Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.762Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.762Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:56:43.762Z - Time taken for 'total execution time for createProjectGraph()' 32.46379999816418ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:32.556Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:32.560Z - [WATCHER]: shared/database/connection.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:39.208Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:39.208Z - [REQUEST]: shared/database/connection.go
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:39.208Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:39.519Z - Time taken for 'hash changed files from watcher' 234.46399999782443ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:39.924Z - Time taken for 'build-project-configs' 480.138599999249ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:40.006Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:40.006Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:40.006Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:40.007Z - Time taken for 'total execution time for createProjectGraph()' 64.46169999986887ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:45.225Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:45.239Z - [WATCHER]: shared/database/connection.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.641Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.641Z - [REQUEST]: shared/database/connection.go
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.641Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.646Z - Time taken for 'hash changed files from watcher' 0.4033999964594841ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.668Z - Time taken for 'build-project-configs' 17.774100001901388ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.712Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.713Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.713Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:51.713Z - Time taken for 'total execution time for createProjectGraph()' 28.455299999564886ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:53.374Z - [WATCHER]: shared/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:53.374Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.790Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.790Z - [REQUEST]: shared/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.790Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.793Z - Time taken for 'hash changed files from watcher' 1.5082000009715557ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.827Z - Time taken for 'build-project-configs' 20.719899997115135ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.884Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.885Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.891Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:57:59.891Z - Time taken for 'total execution time for createProjectGraph()' 42.08070000261068ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:15.017Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:15.023Z - [WATCHER]: remove-duplicate-db-connections.md was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.430Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.431Z - [REQUEST]: remove-duplicate-db-connections.md
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.431Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.442Z - Time taken for 'hash changed files from watcher' 0.42980000004172325ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.463Z - Time taken for 'build-project-configs' 17.812399998307228ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.503Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.504Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.504Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:21.504Z - Time taken for 'total execution time for createProjectGraph()' 29.082100000232458ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:31.625Z - [WATCHER]: test-shared-database.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:31.711Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.041Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.041Z - [REQUEST]: test-shared-database.go
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.041Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.052Z - Time taken for 'hash changed files from watcher' 0.3986999988555908ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.278Z - Time taken for 'build-project-configs' 187.03009999915957ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.352Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.353Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.353Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.353Z - Time taken for 'total execution time for createProjectGraph()' 94.67770000174642ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.354Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:38.373Z - [WATCHER]: go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.787Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.787Z - [REQUEST]: go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.788Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.796Z - Time taken for 'hash changed files from watcher' 0.3902999982237816ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.822Z - Time taken for 'build-project-configs' 21.29839999973774ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.872Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.873Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.873Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:58:44.873Z - Time taken for 'total execution time for createProjectGraph()' 36.01089999824762ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:05.739Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:05.741Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.154Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.154Z - [REQUEST]: go.sum,go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.154Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.164Z - Time taken for 'hash changed files from watcher' 1.9506000019609928ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.194Z - Time taken for 'build-project-configs' 27.361200001090765ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.246Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.247Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.247Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:12.247Z - Time taken for 'total execution time for createProjectGraph()' 33.96519999951124ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:15.273Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:15.273Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.676Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.676Z - [REQUEST]: shared/database/connection.go,shared/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.677Z - [REQUEST]: shared/database/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.683Z - Time taken for 'hash changed files from watcher' 0.5663999989628792ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.711Z - Time taken for 'build-project-configs' 24.69979999959469ms
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.755Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.756Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.757Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T11:59:21.757Z - Time taken for 'total execution time for createProjectGraph()' 32.106100000441074ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:01.200Z - [WATCHER]: test-shared-database.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:01.206Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.619Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.620Z - [REQUEST]: test-shared-database.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.620Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.623Z - Time taken for 'hash changed files from watcher' 0.45979999750852585ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.646Z - Time taken for 'build-project-configs' 16.95879999920726ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.683Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.683Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.683Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:07.684Z - Time taken for 'total execution time for createProjectGraph()' 28.243100002408028ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:20.172Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:20.172Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.576Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.576Z - [REQUEST]: go.sum,go.mod,test-shared-database.go,remove-duplicate-db-connections.md
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.576Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.581Z - Time taken for 'hash changed files from watcher' 0.5854000002145767ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.599Z - Time taken for 'build-project-configs' 14.937900003045797ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.640Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.641Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.641Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:00:26.641Z - Time taken for 'total execution time for createProjectGraph()' 27.92960000038147ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:21.038Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:21.039Z - [WATCHER]: test-shared-database.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.466Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.466Z - [REQUEST]: test-shared-database.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.466Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.481Z - Time taken for 'hash changed files from watcher' 0.4077000021934509ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.499Z - Time taken for 'build-project-configs' 14.123700000345707ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.535Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.535Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.536Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:01:27.536Z - Time taken for 'total execution time for createProjectGraph()' 21.579100001603365ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:35.829Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:35.835Z - [WATCHER]: config/shared.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:37.164Z - [WATCHER]: config/shared.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:37.164Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.245Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.245Z - [REQUEST]: config/shared.env
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.246Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.264Z - Time taken for 'hash changed files from watcher' 1.1115000024437904ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.303Z - Time taken for 'build-project-configs' 44.71110000088811ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.343Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.344Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.344Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:42.344Z - Time taken for 'total execution time for createProjectGraph()' 28.49210000038147ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:55.186Z - [WATCHER]: compare-config-systems.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:07:55.186Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.589Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.590Z - [REQUEST]: compare-config-systems.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.590Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.592Z - Time taken for 'hash changed files from watcher' 0.41909999772906303ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.613Z - Time taken for 'build-project-configs' 15.882100000977516ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.658Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.659Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.659Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:08:01.659Z - Time taken for 'total execution time for createProjectGraph()' 31.214900001883507ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:00.442Z - [WATCHER]: services/user-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:00.443Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.859Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.859Z - [REQUEST]: services/user-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.859Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.866Z - Time taken for 'hash changed files from watcher' 1.528899997472763ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.883Z - Time taken for 'build-project-configs' 17.128699999302626ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.922Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.923Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.924Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:06.924Z - Time taken for 'total execution time for createProjectGraph()' 24.915800001472235ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:13.327Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:13.329Z - [WATCHER]: services/user-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.744Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.744Z - [REQUEST]: services/user-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.744Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.748Z - Time taken for 'hash changed files from watcher' 0.27219999954104424ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.768Z - Time taken for 'build-project-configs' 15.6114999987185ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.808Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.810Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.810Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:09:19.810Z - Time taken for 'total execution time for createProjectGraph()' 25.070700000971556ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:19.114Z - [WATCHER]: .env.shared was deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:19.115Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:24.187Z - [WATCHER]: compare-config-systems.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:24.187Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.517Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.517Z - [REQUEST]: compare-config-systems.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.517Z - [REQUEST]: .env.shared
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.535Z - Time taken for 'hash changed files from watcher' 1.2466000020503998ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.567Z - Time taken for 'build-project-configs' 36.697399999946356ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.612Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.613Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.613Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:10:25.613Z - Time taken for 'total execution time for createProjectGraph()' 33.55220000073314ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:23.165Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:23.168Z - [WATCHER]: services/user-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.575Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.576Z - [REQUEST]: services/user-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.576Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.579Z - Time taken for 'hash changed files from watcher' 0.596500001847744ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.606Z - Time taken for 'build-project-configs' 18.332499999552965ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.652Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.653Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.653Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:29.653Z - Time taken for 'total execution time for createProjectGraph()' 34.821800000965595ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:46.726Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:46.728Z - [WATCHER]: test-current-db-config.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.144Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.144Z - [REQUEST]: test-current-db-config.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.144Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.153Z - Time taken for 'hash changed files from watcher' 1.530799999833107ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.180Z - Time taken for 'build-project-configs' 25.34169999882579ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.225Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.226Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.226Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:11:53.226Z - Time taken for 'total execution time for createProjectGraph()' 29.578499998897314ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:47.239Z - [WATCHER]: test-current-db-config.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:47.239Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.653Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.653Z - [REQUEST]: test-current-db-config.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.653Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.657Z - Time taken for 'hash changed files from watcher' 0.561599999666214ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.678Z - Time taken for 'build-project-configs' 16.8293999992311ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.723Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.726Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.726Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:12:53.726Z - Time taken for 'total execution time for createProjectGraph()' 34.23539999872446ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:32.601Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:32.605Z - [WATCHER]: config/database.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:33.901Z - [WATCHER]: config/database.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:33.901Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.023Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.023Z - [REQUEST]: config/database.env
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.023Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.045Z - Time taken for 'hash changed files from watcher' 0.5361000001430511ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.075Z - Time taken for 'build-project-configs' 23.322799999266863ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.228Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.229Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.229Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:22:39.229Z - Time taken for 'total execution time for createProjectGraph()' 99.73739999905229ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:33.225Z - [WATCHER]: database/create-all-tables.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:33.226Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.654Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.655Z - [REQUEST]: database/create-all-tables.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.655Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.699Z - Time taken for 'hash changed files from watcher' 14.7989999987185ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.762Z - Time taken for 'build-project-configs' 38.19599999859929ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.926Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.927Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.927Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:23:39.927Z - Time taken for 'total execution time for createProjectGraph()' 104.80810000002384ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:04.128Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:04.130Z - [WATCHER]: database/create-all-tables.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.540Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.540Z - [REQUEST]: database/create-all-tables.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.540Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.544Z - Time taken for 'hash changed files from watcher' 0.5373999997973442ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.574Z - Time taken for 'build-project-configs' 19.53830000013113ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.615Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.616Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.616Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:10.616Z - Time taken for 'total execution time for createProjectGraph()' 28.028400000184774ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:36.075Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:36.085Z - [WATCHER]: database/create-tables.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.495Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.495Z - [REQUEST]: database/create-tables.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.495Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.498Z - Time taken for 'hash changed files from watcher' 1.4770999997854233ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.527Z - Time taken for 'build-project-configs' 23.38110000267625ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.588Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.588Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.588Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:24:42.589Z - Time taken for 'total execution time for createProjectGraph()' 42.169800002127886ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:25:58.250Z - [WATCHER]: database/test-connection.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:25:58.275Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:25:59.318Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T12:25:59.318Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.656Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.656Z - [REQUEST]: database/test-connection.go,database/create-tables.go,database/create-all-tables.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.656Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.660Z - Time taken for 'hash changed files from watcher' 2.8953000009059906ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.702Z - Time taken for 'build-project-configs' 29.713800001889467ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.766Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.767Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.767Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:04.767Z - Time taken for 'total execution time for createProjectGraph()' 43.79580000042915ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:50.504Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:50.529Z - [WATCHER]: database/test-manual-connection.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:56.943Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:56.944Z - [REQUEST]: database/test-manual-connection.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:56.944Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:56.951Z - Time taken for 'hash changed files from watcher' 0.39729999750852585ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:56.976Z - Time taken for 'build-project-configs' 20.54949999973178ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:57.032Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:57.033Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:57.033Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:26:57.033Z - Time taken for 'total execution time for createProjectGraph()' 42.23990000039339ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:03.971Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:03.973Z - [WATCHER]: database/test-manual-connection.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:08.410Z - [WATCHER]: database/test-connection.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:08.410Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.374Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.374Z - [REQUEST]: database/test-manual-connection.go,database/test-connection.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.374Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.377Z - Time taken for 'hash changed files from watcher' 0.4627999998629093ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.404Z - Time taken for 'build-project-configs' 18.514299999922514ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.451Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.453Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.453Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:27:10.453Z - Time taken for 'total execution time for createProjectGraph()' 29.5472999997437ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:02.837Z - [WATCHER]: database/test-manual-connection.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:02.838Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.244Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.245Z - [REQUEST]: database/test-manual-connection.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.245Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.247Z - Time taken for 'hash changed files from watcher' 0.3302000015974045ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.273Z - Time taken for 'build-project-configs' 18.518300000578165ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.324Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.324Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.325Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:09.325Z - Time taken for 'total execution time for createProjectGraph()' 31.567100003361702ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:19.938Z - [WATCHER]: database/pgadmin-setup-guide.md was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:19.938Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.346Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.346Z - [REQUEST]: database/pgadmin-setup-guide.md
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.346Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.364Z - Time taken for 'hash changed files from watcher' 0.37550000101327896ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.393Z - Time taken for 'build-project-configs' 18.059499997645617ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.437Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.438Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.439Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:26.439Z - Time taken for 'total execution time for createProjectGraph()' 31.66780000180006ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:40.682Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:40.686Z - [WATCHER]: database/verify-setup.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.094Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.094Z - [REQUEST]: database/verify-setup.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.094Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.098Z - Time taken for 'hash changed files from watcher' 0.5766000002622604ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.119Z - Time taken for 'build-project-configs' 16.89699999988079ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.165Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.166Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.166Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:28:47.166Z - Time taken for 'total execution time for createProjectGraph()' 30.494699999690056ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:29:58.021Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T12:29:58.022Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.427Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.427Z - [REQUEST]: database/verify-setup.sql,database/pgadmin-setup-guide.md
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.427Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.430Z - Time taken for 'hash changed files from watcher' 0.5171000026166439ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.454Z - Time taken for 'build-project-configs' 18.800000000745058ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.504Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.505Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.505Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:30:04.505Z - Time taken for 'total execution time for createProjectGraph()' 31.329000003635883ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:41.680Z - [WATCHER]: database/setup-database.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:41.680Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:43.762Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:43.766Z - [WATCHER]: database/setup-database.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.084Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.084Z - [REQUEST]: database/setup-database.go
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.084Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.106Z - Time taken for 'hash changed files from watcher' 1.7592000029981136ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.129Z - Time taken for 'build-project-configs' 28.11770000308752ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.168Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.170Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.170Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:33:48.170Z - Time taken for 'total execution time for createProjectGraph()' 28.287999998778105ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:32.080Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:32.083Z - [WATCHER]: database/setup-with-psql.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.499Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.499Z - [REQUEST]: database/setup-with-psql.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.499Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.504Z - Time taken for 'hash changed files from watcher' 0.5315999984741211ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.531Z - Time taken for 'build-project-configs' 20.428599998354912ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.570Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.573Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.573Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:34:38.573Z - Time taken for 'total execution time for createProjectGraph()' 28.392700001597404ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:43.150Z - [WATCHER]: database/run-in-pgadmin.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:43.151Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.555Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.555Z - [REQUEST]: database/run-in-pgadmin.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.555Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.558Z - Time taken for 'hash changed files from watcher' 1.0935000032186508ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.585Z - Time taken for 'build-project-configs' 20.975000001490116ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.636Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.637Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.637Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:49.638Z - Time taken for 'total execution time for createProjectGraph()' 38.31179999932647ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:51.711Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:51.712Z - [WATCHER]: database/setup-with-psql.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.128Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.128Z - [REQUEST]: database/setup-with-psql.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.128Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.133Z - Time taken for 'hash changed files from watcher' 0.4578999988734722ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.157Z - Time taken for 'build-project-configs' 20.084300000220537ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.206Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.207Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.208Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:35:58.208Z - Time taken for 'total execution time for createProjectGraph()' 32.037399999797344ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:00.148Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:00.152Z - [WATCHER]: database/run-in-pgadmin.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.568Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.568Z - [REQUEST]: database/run-in-pgadmin.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.568Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.572Z - Time taken for 'hash changed files from watcher' 0.34050000086426735ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.595Z - Time taken for 'build-project-configs' 17.786499999463558ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.648Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.649Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.649Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:06.656Z - Time taken for 'total execution time for createProjectGraph()' 40.23299999907613ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:13.206Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:13.215Z - [WATCHER]: database/diagnose-postgresql.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.626Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.626Z - [REQUEST]: database/diagnose-postgresql.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.626Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.628Z - Time taken for 'hash changed files from watcher' 0.32100000232458115ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.662Z - Time taken for 'build-project-configs' 24.493000000715256ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.703Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.704Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.704Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:37:19.705Z - Time taken for 'total execution time for createProjectGraph()' 30.457100000232458ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:49.659Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:49.661Z - [WATCHER]: database/test-auth.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.069Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.069Z - [REQUEST]: database/test-auth.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.070Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.073Z - Time taken for 'hash changed files from watcher' 0.654899999499321ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.093Z - Time taken for 'build-project-configs' 15.506299998611212ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.144Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.145Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.145Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:38:56.145Z - Time taken for 'total execution time for createProjectGraph()' 34.40479999780655ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:07.854Z - [WATCHER]: database/diagnose-postgresql.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:07.854Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.268Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.268Z - [REQUEST]: database/diagnose-postgresql.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.268Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.271Z - Time taken for 'hash changed files from watcher' 0.32349999994039536ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.290Z - Time taken for 'build-project-configs' 15.641800001263618ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.325Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.326Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.329Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:39:14.329Z - Time taken for 'total execution time for createProjectGraph()' 22.127500001341105ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:04.349Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:04.376Z - [WATCHER]: database/test-auth.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.788Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.788Z - [REQUEST]: database/test-auth.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.788Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.796Z - Time taken for 'hash changed files from watcher' 0.38829999789595604ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.811Z - Time taken for 'build-project-configs' 16.437699999660254ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.846Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.848Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.848Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:10.849Z - Time taken for 'total execution time for createProjectGraph()' 21.843899998813868ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:36.610Z - [WATCHER]: config/database.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:36.611Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.015Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.015Z - [REQUEST]: config/database.env
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.015Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.017Z - Time taken for 'hash changed files from watcher' 0.4010999985039234ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.043Z - Time taken for 'build-project-configs' 18.83169999718666ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.079Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.079Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.080Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:40:43.080Z - Time taken for 'total execution time for createProjectGraph()' 25.61880000308156ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:51.220Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:51.222Z - [WATCHER]: config/database.env was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.628Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.629Z - [REQUEST]: config/database.env
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.629Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.631Z - Time taken for 'hash changed files from watcher' 0.5179999992251396ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.651Z - Time taken for 'build-project-configs' 14.913399998098612ms
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.687Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.688Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.688Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T12:41:57.688Z - Time taken for 'total execution time for createProjectGraph()' 26.096499998122454ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:16.031Z - [WATCHER]: database/create-tables-ordered.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:16.195Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:18.627Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:18.632Z - [WATCHER]: database/create-tables-ordered.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.578Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.578Z - [REQUEST]: database/create-tables-ordered.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.579Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.621Z - Time taken for 'hash changed files from watcher' 16.403900001198053ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.757Z - Time taken for 'build-project-configs' 123.4666000008583ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.854Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.856Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.856Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:22.856Z - Time taken for 'total execution time for createProjectGraph()' 79.15930000320077ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:33.709Z - [WATCHER]: database/setup-database.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:33.710Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.120Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.120Z - [REQUEST]: database/setup-database.go
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.120Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.139Z - Time taken for 'hash changed files from watcher' 5.61260000243783ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.267Z - Time taken for 'build-project-configs' 78.44060000032187ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.389Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.390Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.390Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:42:40.390Z - Time taken for 'total execution time for createProjectGraph()' 121.0190000012517ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:21.483Z - [WATCHER]: database/setup-database.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:21.486Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:27.890Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:27.891Z - [REQUEST]: database/setup-database.go
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:27.891Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:27.923Z - Time taken for 'hash changed files from watcher' 3.5170999988913536ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:27.989Z - Time taken for 'build-project-configs' 56.99870000034571ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:28.057Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:28.065Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:28.065Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:45:28.065Z - Time taken for 'total execution time for createProjectGraph()' 59.389900002628565ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:48.291Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:48.316Z - [WATCHER]: database/test-uuid-extension.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:50.425Z - [WATCHER]: database/test-uuid-extension.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:50.425Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.728Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.728Z - [REQUEST]: database/test-uuid-extension.go
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.728Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.732Z - Time taken for 'hash changed files from watcher' 4.504599995911121ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.760Z - Time taken for 'build-project-configs' 20.94479999691248ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.812Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.813Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.813Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:52:54.814Z - Time taken for 'total execution time for createProjectGraph()' 36.384900003671646ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:25.205Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:25.258Z - [WATCHER]: analyze-project-issues.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.660Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.660Z - [REQUEST]: analyze-project-issues.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.660Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.670Z - Time taken for 'hash changed files from watcher' 0.5450999960303307ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.693Z - Time taken for 'build-project-configs' 22.735500000417233ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.759Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.760Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.760Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:54:31.760Z - Time taken for 'total execution time for createProjectGraph()' 38.873099997639656ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:44.919Z - [WATCHER]: database/cmd/create-tables/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:44.920Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.322Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.323Z - [REQUEST]: database/cmd/create-tables/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.323Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.347Z - Time taken for 'hash changed files from watcher' 0.5850000008940697ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.373Z - Time taken for 'build-project-configs' 19.405199997127056ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.428Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.429Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.429Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:51.430Z - Time taken for 'total execution time for createProjectGraph()' 35.59780000150204ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:56.594Z - [WATCHER]: database/cmd/create-tables/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:55:56.601Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.017Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.017Z - [REQUEST]: database/cmd/create-tables/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.017Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.087Z - Time taken for 'hash changed files from watcher' 0.7934999987483025ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.163Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.164Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.165Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.165Z - Time taken for 'total execution time for createProjectGraph()' 45.248000003397465ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.832Z - [WATCHER]: analyze-project-issues.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:03.832Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.243Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.243Z - [REQUEST]: analyze-project-issues.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.243Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.247Z - Time taken for 'hash changed files from watcher' 0.4560999944806099ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.279Z - Time taken for 'build-project-configs' 19.90700000524521ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.339Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.340Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.340Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:10.340Z - Time taken for 'total execution time for createProjectGraph()' 53.878199994564056ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:37.500Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:37.502Z - [WATCHER]: database/cmd/create-tables/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:43.931Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:43.931Z - [REQUEST]: database/cmd/create-tables/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:43.932Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:43.943Z - Time taken for 'hash changed files from watcher' 1.1162000000476837ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:43.992Z - Time taken for 'build-project-configs' 25.929399996995926ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:44.062Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:44.063Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:44.063Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:56:44.063Z - Time taken for 'total execution time for createProjectGraph()' 52.995800003409386ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:32.843Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 2 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:32.844Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.250Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.251Z - [REQUEST]: database/cmd/create-tables/go.mod,database/cmd/create-tables/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.251Z - [REQUEST]: database/cmd/desktop.ini,database/cmd/create-tables/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.254Z - Time taken for 'hash changed files from watcher' 2.097699999809265ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.279Z - Time taken for 'build-project-configs' 18.70489999651909ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.328Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.329Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.329Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:57:39.329Z - Time taken for 'total execution time for createProjectGraph()' 29.527300000190735ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:43.351Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:43.354Z - [WATCHER]: database/cmd/create-tables/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:49.762Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:49.762Z - [REQUEST]: database/cmd/create-tables/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:49.762Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:49.766Z - Time taken for 'hash changed files from watcher' 0.4381000027060509ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:49.864Z - Time taken for 'build-project-configs' 37.13509999960661ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:50.015Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:50.016Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:50.017Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:58:50.017Z - Time taken for 'total execution time for createProjectGraph()' 130.22320000082254ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:27.746Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:27.953Z - [WATCHER]: services/product-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.426Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.426Z - [REQUEST]: services/product-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.426Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.436Z - Time taken for 'hash changed files from watcher' 0.42909999936819077ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.461Z - Time taken for 'build-project-configs' 20.38010000437498ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.512Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.513Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.513Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:34.513Z - Time taken for 'total execution time for createProjectGraph()' 42.364500001072884ms
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:53.729Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T13:59:53.730Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.141Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.141Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.141Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.144Z - Time taken for 'hash changed files from watcher' 0.9211999997496605ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.169Z - Time taken for 'build-project-configs' 17.552199997007847ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.223Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.224Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.224Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:00.225Z - Time taken for 'total execution time for createProjectGraph()' 35.09390000253916ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:14.264Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:14.320Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.732Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.732Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.732Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.746Z - Time taken for 'hash changed files from watcher' 0.5054000020027161ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.770Z - Time taken for 'build-project-configs' 28.451999999582767ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.821Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.822Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.823Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:20.823Z - Time taken for 'total execution time for createProjectGraph()' 30.122600004076958ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:30.605Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:30.655Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.023Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.023Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.024Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.059Z - Time taken for 'hash changed files from watcher' 2.510000005364418ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.087Z - Time taken for 'build-project-configs' 30.696899995207787ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.136Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.137Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.137Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:37.137Z - Time taken for 'total execution time for createProjectGraph()' 30.643200002610683ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:47.133Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:47.135Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.538Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.538Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.538Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.547Z - Time taken for 'hash changed files from watcher' 0.4699999988079071ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.571Z - Time taken for 'build-project-configs' 22.280200004577637ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.635Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.636Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.636Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:53.636Z - Time taken for 'total execution time for createProjectGraph()' 40.943399995565414ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:59.348Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:00:59.349Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.751Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.751Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.751Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.755Z - Time taken for 'hash changed files from watcher' 0.4034000039100647ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.781Z - Time taken for 'build-project-configs' 19.91220000386238ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.833Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.834Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.834Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:05.834Z - Time taken for 'total execution time for createProjectGraph()' 33.770599998533726ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:23.491Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:23.499Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.904Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.904Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.904Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.910Z - Time taken for 'hash changed files from watcher' 0.9307999983429909ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.926Z - Time taken for 'build-project-configs' 16.165700003504753ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.969Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.969Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.970Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:29.970Z - Time taken for 'total execution time for createProjectGraph()' 25.495099999010563ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:31.956Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:31.958Z - [WATCHER]: services/product-service/internal/config/config.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.364Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.365Z - [REQUEST]: services/product-service/internal/config/config.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.366Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.426Z - Time taken for 'hash changed files from watcher' 2.6468000039458275ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.452Z - Time taken for 'build-project-configs' 63.04410000145435ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.533Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.534Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.534Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:01:38.534Z - Time taken for 'total execution time for createProjectGraph()' 43.535199999809265ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:21.603Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:21.646Z - [WATCHER]: final-verification.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.050Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.050Z - [REQUEST]: final-verification.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.050Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.055Z - Time taken for 'hash changed files from watcher' 1.9577000066637993ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.084Z - Time taken for 'build-project-configs' 21.614000000059605ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.151Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.152Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.152Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:28.152Z - Time taken for 'total execution time for createProjectGraph()' 46.784099996089935ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:50.144Z - [WATCHER]: services/product-service/cmd/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:50.144Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.553Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.553Z - [REQUEST]: services/product-service/cmd/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.553Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.570Z - Time taken for 'hash changed files from watcher' 8.038000002503395ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.598Z - Time taken for 'build-project-configs' 27.392699994146824ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.679Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.680Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.680Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:02:56.680Z - Time taken for 'total execution time for createProjectGraph()' 38.9049000069499ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:28.320Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:28.322Z - [WATCHER]: final-verification.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.727Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.727Z - [REQUEST]: final-verification.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.727Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.734Z - Time taken for 'hash changed files from watcher' 0.40309999883174896ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.765Z - Time taken for 'build-project-configs' 22.206200003623962ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.827Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.828Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.828Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:03:34.828Z - Time taken for 'total execution time for createProjectGraph()' 44.666900001466274ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:10.863Z - [WATCHER]: final-verification.ps1 was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:10.864Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.267Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.267Z - [REQUEST]: final-verification.ps1
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.267Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.271Z - Time taken for 'hash changed files from watcher' 0.43469999730587006ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.304Z - Time taken for 'build-project-configs' 26.831799998879433ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.349Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.350Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.350Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:05:17.350Z - Time taken for 'total execution time for createProjectGraph()' 31.23300000280142ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:39.508Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:39.540Z - [WATCHER]: database/cmd/verify-tables/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:45.949Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:45.949Z - [REQUEST]: database/cmd/verify-tables/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:45.949Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:45.970Z - Time taken for 'hash changed files from watcher' 1.7748000025749207ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:46.008Z - Time taken for 'build-project-configs' 35.78539999574423ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:46.111Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:46.113Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:46.113Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:46.113Z - Time taken for 'total execution time for createProjectGraph()' 66.20409999787807ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:48.818Z - [WATCHER]: database/cmd/verify-tables/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:48.819Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.224Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.225Z - [REQUEST]: database/cmd/verify-tables/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.225Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.231Z - Time taken for 'hash changed files from watcher' 0.4390000030398369ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.259Z - Time taken for 'build-project-configs' 21.883099995553493ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.305Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.306Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.306Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:06:55.306Z - Time taken for 'total execution time for createProjectGraph()' 35.65410000085831ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:06.395Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:06.399Z - [WATCHER]: database/cmd/verify-tables/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.809Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.809Z - [REQUEST]: database/cmd/verify-tables/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.809Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.821Z - Time taken for 'hash changed files from watcher' 0.5234000012278557ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.839Z - Time taken for 'build-project-configs' 21.26179999858141ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.897Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.899Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.899Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:12.899Z - Time taken for 'total execution time for createProjectGraph()' 29.847599998116493ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:51.581Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:51.584Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.001Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.001Z - [REQUEST]: database/cmd/verify-tables/main.go,database/cmd/verify-tables/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.001Z - [REQUEST]: database/cmd/verify-tables/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.006Z - Time taken for 'hash changed files from watcher' 2.904600001871586ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.032Z - Time taken for 'build-project-configs' 24.341499999165535ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.073Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.073Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.074Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:07:58.074Z - Time taken for 'total execution time for createProjectGraph()' 22.947899997234344ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:08:56.950Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:08:56.953Z - [WATCHER]: database/cmd/verify-tables/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.360Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.360Z - [REQUEST]: database/cmd/verify-tables/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.361Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.364Z - Time taken for 'hash changed files from watcher' 0.4895000010728836ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.391Z - Time taken for 'build-project-configs' 18.996799997985363ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.472Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.473Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.480Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:03.480Z - Time taken for 'total execution time for createProjectGraph()' 65.46339999884367ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:35.319Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:35.323Z - [WATCHER]: database/cmd/verify-tables/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.724Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.724Z - [REQUEST]: database/cmd/verify-tables/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.724Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.746Z - Time taken for 'hash changed files from watcher' 0.5043999999761581ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.767Z - Time taken for 'build-project-configs' 33.83259999752045ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.856Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.857Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.857Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:09:41.857Z - Time taken for 'total execution time for createProjectGraph()' 42.64699999988079ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:44.594Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:44.596Z - [WATCHER]: database/cmd/verify-tables/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:50.999Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:50.999Z - [REQUEST]: database/cmd/verify-tables/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:50.999Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:51.004Z - Time taken for 'hash changed files from watcher' 0.4994000047445297ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:51.027Z - Time taken for 'build-project-configs' 18.692800000309944ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:51.066Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:51.067Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:51.067Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:10:51.067Z - Time taken for 'total execution time for createProjectGraph()' 24.447299994528294ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:13.827Z - [WATCHER]: database/cmd/add-sample-data/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:13.855Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.258Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.258Z - [REQUEST]: database/cmd/add-sample-data/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.258Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.270Z - Time taken for 'hash changed files from watcher' 0.590499997138977ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.304Z - Time taken for 'build-project-configs' 31.92799999564886ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.365Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.366Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.367Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:20.367Z - Time taken for 'total execution time for createProjectGraph()' 30.601000003516674ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:24.269Z - [WATCHER]: database/cmd/add-sample-data/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:24.275Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.676Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.676Z - [REQUEST]: database/cmd/add-sample-data/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.676Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.691Z - Time taken for 'hash changed files from watcher' 0.5804999992251396ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.718Z - Time taken for 'build-project-configs' 20.113300003111362ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.785Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.791Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.792Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:15:30.792Z - Time taken for 'total execution time for createProjectGraph()' 65.20100000500679ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:28.206Z - [WATCHER]: database/sample-data.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:28.207Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.611Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.611Z - [REQUEST]: database/sample-data.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.611Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.618Z - Time taken for 'hash changed files from watcher' 0.5600000023841858ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.639Z - Time taken for 'build-project-configs' 19.889299996197224ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.713Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.714Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.714Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:34.714Z - Time taken for 'total execution time for createProjectGraph()' 39.69990000128746ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:38.128Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:38.130Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.548Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.548Z - [REQUEST]: database/cmd/add-sample-data/main.go,database/cmd/add-sample-data/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.548Z - [REQUEST]: database/cmd/add-sample-data/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.557Z - Time taken for 'hash changed files from watcher' 5.36879999935627ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.576Z - Time taken for 'build-project-configs' 17.75729999691248ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.639Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.639Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.640Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:16:44.640Z - Time taken for 'total execution time for createProjectGraph()' 40.16649999469519ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:37.296Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:37.297Z - [WATCHER]: database/sample-data.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.711Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.711Z - [REQUEST]: database/sample-data.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.711Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.715Z - Time taken for 'hash changed files from watcher' 0.3496999964118004ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.754Z - Time taken for 'build-project-configs' 24.841899998486042ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.795Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.796Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.796Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:17:43.796Z - Time taken for 'total execution time for createProjectGraph()' 31.670999996364117ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:28.167Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:28.170Z - [WATCHER]: database/cmd/add-sample-data/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.584Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.584Z - [REQUEST]: database/cmd/add-sample-data/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.584Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.589Z - Time taken for 'hash changed files from watcher' 1.8155999928712845ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.617Z - Time taken for 'build-project-configs' 20.079599998891354ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.686Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.686Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.687Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:18:34.687Z - Time taken for 'total execution time for createProjectGraph()' 49.95040000230074ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:19:58.720Z - [WATCHER]: database/cmd/add-sample-data/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:19:58.720Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:05.226Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:05.226Z - [REQUEST]: database/cmd/add-sample-data/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:05.226Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:05.548Z - Time taken for 'hash changed files from watcher' 0.5807999968528748ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:06.025Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:06.025Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:06.026Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:06.026Z - Time taken for 'total execution time for createProjectGraph()' 168.26539999991655ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:09.692Z - [WATCHER]: database/cmd/add-sample-data/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:09.693Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:16.113Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:16.113Z - [REQUEST]: database/cmd/add-sample-data/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:16.113Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:16.195Z - Time taken for 'hash changed files from watcher' 3.5450999960303307ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:16.329Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:16.330Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:16.330Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:20:16.330Z - Time taken for 'total execution time for createProjectGraph()' 69.3386999964714ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:22.978Z - [WATCHER]: database/cmd/add-sample-data/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:22.979Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.394Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.394Z - [REQUEST]: database/cmd/add-sample-data/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.394Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.397Z - Time taken for 'hash changed files from watcher' 1.742799997329712ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.432Z - Time taken for 'build-project-configs' 25.359600000083447ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.491Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.493Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.493Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:29.493Z - Time taken for 'total execution time for createProjectGraph()' 34.118599995970726ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:31.931Z - [WATCHER]: database/cmd/add-sample-data-simple/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:31.932Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.349Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.349Z - [REQUEST]: database/cmd/add-sample-data-simple/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.349Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.357Z - Time taken for 'hash changed files from watcher' 0.6992000043392181ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.395Z - Time taken for 'build-project-configs' 27.943499997258186ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.453Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.454Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.454Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:38.455Z - Time taken for 'total execution time for createProjectGraph()' 51.95879999548197ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:40.782Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:40.937Z - [WATCHER]: database/cmd/add-sample-data-simple/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.345Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.345Z - [REQUEST]: database/cmd/add-sample-data-simple/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.345Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.350Z - Time taken for 'hash changed files from watcher' 1.9803000018000603ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.371Z - Time taken for 'build-project-configs' 16.051399998366833ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.436Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.437Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.437Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:21:47.437Z - Time taken for 'total execution time for createProjectGraph()' 35.912399999797344ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:05.032Z - [WATCHER]: database/cmd/add-sample-data-simple/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:05.032Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:11.442Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:11.442Z - [REQUEST]: database/cmd/add-sample-data-simple/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:11.443Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:11.574Z - Time taken for 'hash changed files from watcher' 0.5771000012755394ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:11.627Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:11.630Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:11.630Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:22:11.630Z - Time taken for 'total execution time for createProjectGraph()' 61.360699996352196ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:36.919Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:36.993Z - [WATCHER]: database/cmd/show-sample-data/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.574Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.574Z - [REQUEST]: database/cmd/show-sample-data/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.574Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.664Z - Time taken for 'hash changed files from watcher' 170.36070000380278ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.742Z - Time taken for 'build-project-configs' 82.10490000247955ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.825Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.826Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.826Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:43.826Z - Time taken for 'total execution time for createProjectGraph()' 82.27899999916553ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:50.842Z - [WATCHER]: database/cmd/show-sample-data/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:50.842Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.252Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.253Z - [REQUEST]: database/cmd/show-sample-data/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.253Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.260Z - Time taken for 'hash changed files from watcher' 0.6389999985694885ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.293Z - Time taken for 'build-project-configs' 27.658399999141693ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.389Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.392Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.393Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:23:57.393Z - Time taken for 'total execution time for createProjectGraph()' 73.28980000317097ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:45.299Z - [WATCHER]: database/cmd/show-sample-data/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:45.301Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:51.729Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:51.729Z - [REQUEST]: database/cmd/show-sample-data/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:51.729Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:51.796Z - Time taken for 'hash changed files from watcher' 1.656199999153614ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:51.979Z - Time taken for 'build-project-configs' 125.86420000344515ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:52.351Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:52.352Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:52.353Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:24:52.353Z - Time taken for 'total execution time for createProjectGraph()' 140.02480000257492ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:11.589Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:11.591Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.000Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.000Z - [REQUEST]: database/cmd/add-sample-data-simple/go.mod,database/cmd/add-sample-data-simple/go.sum,database/cmd/add-sample-data-simple/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.000Z - [REQUEST]: database/cmd/add-sample-data-simple/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.037Z - Time taken for 'hash changed files from watcher' 2.48140000551939ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.050Z - Time taken for 'build-project-configs' 28.561699993908405ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.169Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.170Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.170Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:25:18.170Z - Time taken for 'total execution time for createProjectGraph()' 43.56660000234842ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:32.897Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:32.899Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.311Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.311Z - [REQUEST]: database/cmd/show-sample-data/go.mod,database/cmd/show-sample-data/main.go,database/cmd/show-sample-data/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.311Z - [REQUEST]: database/cmd/show-sample-data/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.314Z - Time taken for 'hash changed files from watcher' 1.41160000115633ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.356Z - Time taken for 'build-project-configs' 37.260399997234344ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.407Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.408Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.408Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:26:39.408Z - Time taken for 'total execution time for createProjectGraph()' 31.20840000361204ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:29.435Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:29.570Z - [WATCHER]: database/enhanced-schema.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:32.029Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:32.031Z - [WATCHER]: database/enhanced-schema.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:35.991Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:35.991Z - [REQUEST]: database/enhanced-schema.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:35.991Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:36.041Z - Time taken for 'hash changed files from watcher' 9.120099999010563ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:36.138Z - Time taken for 'build-project-configs' 98.89689999818802ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:36.376Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:36.378Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:36.378Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:53:36.378Z - Time taken for 'total execution time for createProjectGraph()' 131.08240000158548ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:04.419Z - [WATCHER]: database/cmd/add-enhanced-tables/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:04.473Z - [WATCHER]: database/cmd/add-enhanced-tables/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:04.474Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:04.474Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:10.872Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:10.873Z - [REQUEST]: database/cmd/add-enhanced-tables/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:10.873Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:10.994Z - Time taken for 'hash changed files from watcher' 0.6600000038743019ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:11.021Z - Time taken for 'build-project-configs' 84.72069999575615ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:11.085Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:11.086Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:11.086Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:11.087Z - Time taken for 'total execution time for createProjectGraph()' 39.915899999439716ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:12.583Z - [WATCHER]: database/cmd/add-enhanced-tables/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:12.584Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:18.999Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:18.999Z - [REQUEST]: database/cmd/add-enhanced-tables/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:18.999Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:19.008Z - Time taken for 'hash changed files from watcher' 0.5874000042676926ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:19.044Z - Time taken for 'build-project-configs' 26.230999998748302ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:19.117Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:19.118Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:19.118Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:54:19.118Z - Time taken for 'total execution time for createProjectGraph()' 57.868000000715256ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:55:54.140Z - [WATCHER]: database/cmd/add-enhanced-tables/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:55:54.140Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.546Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.546Z - [REQUEST]: database/cmd/add-enhanced-tables/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.546Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.567Z - Time taken for 'hash changed files from watcher' 2.397799998521805ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.617Z - Time taken for 'build-project-configs' 49.04159999638796ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.681Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.682Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.682Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:00.682Z - Time taken for 'total execution time for createProjectGraph()' 45.18930000066757ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:20.888Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:20.890Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:27.386Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:27.387Z - [REQUEST]: database/cmd/add-enhanced-tables/main.go,database/cmd/add-enhanced-tables/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:27.387Z - [REQUEST]: database/cmd/add-enhanced-tables/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:27.481Z - Time taken for 'hash changed files from watcher' 39.63080000132322ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:27.708Z - Time taken for 'build-project-configs' 182.9569000005722ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:28.080Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:28.081Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:28.081Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:28.081Z - Time taken for 'total execution time for createProjectGraph()' 248.98330000042915ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:53.383Z - [WATCHER]: database/cmd/add-enhanced-tables/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:53.383Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.790Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.790Z - [REQUEST]: database/cmd/add-enhanced-tables/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.790Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.795Z - Time taken for 'hash changed files from watcher' 0.7230999991297722ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.819Z - Time taken for 'build-project-configs' 19.809299997985363ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.861Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.862Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.862Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:56:59.862Z - Time taken for 'total execution time for createProjectGraph()' 23.575300000607967ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:08.492Z - [WATCHER]: database/cmd/add-enhanced-tables/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:08.492Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.909Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.909Z - [REQUEST]: database/cmd/add-enhanced-tables/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.909Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.919Z - Time taken for 'hash changed files from watcher' 0.6741999983787537ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.949Z - Time taken for 'build-project-configs' 22.333700001239777ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.989Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.990Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.991Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:14.991Z - Time taken for 'total execution time for createProjectGraph()' 34.36300000548363ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:40.874Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:40.876Z - [WATCHER]: database/cmd/add-enhanced-tables/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.325Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.325Z - [REQUEST]: database/cmd/add-enhanced-tables/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.325Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.435Z - Time taken for 'hash changed files from watcher' 0.7710999995470047ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.467Z - Time taken for 'build-project-configs' 92.02849999815226ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.637Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.638Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.638Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:57:47.639Z - Time taken for 'total execution time for createProjectGraph()' 68.31849999725819ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:41.948Z - [WATCHER]: database/ENHANCED_SCHEMA_SUMMARY.md was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:41.948Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.370Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.370Z - [REQUEST]: database/ENHANCED_SCHEMA_SUMMARY.md
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.370Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.375Z - Time taken for 'hash changed files from watcher' 1.589299999177456ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.402Z - Time taken for 'build-project-configs' 21.39310000091791ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.470Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.470Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.471Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:58:48.471Z - Time taken for 'total execution time for createProjectGraph()' 44.219299994409084ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:04.214Z - [WATCHER]: database/cmd/add-enhanced-tables/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:04.215Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.615Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.616Z - [REQUEST]: database/cmd/add-enhanced-tables/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.616Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.625Z - Time taken for 'hash changed files from watcher' 0.5857999995350838ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.652Z - Time taken for 'build-project-configs' 21.383200004696846ms
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.726Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.727Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.727Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T14:59:10.728Z - Time taken for 'total execution time for createProjectGraph()' 51.80070000141859ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:24.852Z - [WATCHER]: database/ENHANCED_SCHEMA_SUMMARY.md was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:24.853Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.267Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.268Z - [REQUEST]: database/ENHANCED_SCHEMA_SUMMARY.md
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.268Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.275Z - Time taken for 'hash changed files from watcher' 0.4635000005364418ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.297Z - Time taken for 'build-project-configs' 16.669100001454353ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.367Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.369Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.369Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:00:31.369Z - Time taken for 'total execution time for createProjectGraph()' 39.48140000551939ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:24.935Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:24.958Z - [WATCHER]: database/add-missing-tables.sql was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.360Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.360Z - [REQUEST]: database/add-missing-tables.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.363Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.382Z - Time taken for 'hash changed files from watcher' 0.6481000036001205ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.504Z - Time taken for 'build-project-configs' 90.80889999866486ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.658Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.659Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.660Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:31.660Z - Time taken for 'total execution time for createProjectGraph()' 106.30020000040531ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:43.279Z - [WATCHER]: database/cmd/add-enhanced-tables/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:43.279Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.692Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.692Z - [REQUEST]: database/cmd/add-enhanced-tables/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.692Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.698Z - Time taken for 'hash changed files from watcher' 0.759100005030632ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.733Z - Time taken for 'build-project-configs' 22.57550000399351ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.797Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.798Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.798Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:01:49.798Z - Time taken for 'total execution time for createProjectGraph()' 49.11420000344515ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:25.357Z - [WATCHER]: database/cmd/check-category-structure/main.go was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:25.362Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:31.772Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:31.772Z - [REQUEST]: database/cmd/check-category-structure/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:31.773Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:31.804Z - Time taken for 'hash changed files from watcher' 7.730399996042252ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:31.983Z - Time taken for 'build-project-configs' 108.13859999924898ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:32.099Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:32.100Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:32.100Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:32.100Z - Time taken for 'total execution time for createProjectGraph()' 130.7221000045538ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:36.987Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:36.990Z - [WATCHER]: database/cmd/check-category-structure/go.mod was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:43.408Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:43.408Z - [REQUEST]: database/cmd/check-category-structure/go.mod
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:43.408Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:43.602Z - Time taken for 'hash changed files from watcher' 0.4916999936103821ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:43.915Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:43.916Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:43.916Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:03:43.917Z - Time taken for 'total execution time for createProjectGraph()' 220.01749999821186ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:00.922Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:00.929Z - [WATCHER]: database/cmd/check-category-structure/go.sum was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.349Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.349Z - [REQUEST]: database/cmd/check-category-structure/go.sum
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.349Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.467Z - Time taken for 'hash changed files from watcher' 0.61769999563694ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.551Z - Time taken for 'build-project-configs' 32.98629999905825ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.655Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.656Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.656Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:07.657Z - Time taken for 'total execution time for createProjectGraph()' 98.20679999887943ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:28.249Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:28.249Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.665Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.666Z - [REQUEST]: database/cmd/add-enhanced-tables/main.go,database/add-missing-tables.sql
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.669Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.693Z - Time taken for 'hash changed files from watcher' 3.340599998831749ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.738Z - Time taken for 'build-project-configs' 41.54800000041723ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.983Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.984Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.984Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:04:34.984Z - Time taken for 'total execution time for createProjectGraph()' 104.05550000071526ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:40.374Z - [WATCHER]: database/COMPLETE_DATABASE_STATUS.md was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:40.380Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:40.420Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.779Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.779Z - [REQUEST]: database/COMPLETE_DATABASE_STATUS.md
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.779Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.827Z - Time taken for 'hash changed files from watcher' 3.0410000011324883ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.886Z - Time taken for 'build-project-configs' 45.21969999372959ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.936Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.937Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.944Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:05:46.944Z - Time taken for 'total execution time for createProjectGraph()' 57.82420000433922ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:07.599Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 1 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:07.617Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.004Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.004Z - [REQUEST]: database/cmd/check-category-structure/go.sum,database/cmd/check-category-structure/go.mod,database/cmd/check-category-structure/main.go
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.004Z - [REQUEST]: database/cmd/check-category-structure/desktop.ini
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.013Z - Time taken for 'hash changed files from watcher' 0.8144000023603439ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.053Z - Time taken for 'build-project-configs' 21.27759999781847ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.111Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.111Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.111Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:06:14.112Z - Time taken for 'total execution time for createProjectGraph()' 51.649400003254414ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:12.471Z - [WATCHER]: database/COMPLETE_DATABASE_STATUS.md was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:12.471Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.872Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.872Z - [REQUEST]: database/COMPLETE_DATABASE_STATUS.md
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.872Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.875Z - Time taken for 'hash changed files from watcher' 0.7269999980926514ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.908Z - Time taken for 'build-project-configs' 26.89199999719858ms
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.955Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.956Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.956Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T15:07:18.956Z - Time taken for 'total execution time for createProjectGraph()' 30.994699999690056ms
