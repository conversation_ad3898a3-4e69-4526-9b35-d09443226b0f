[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.111Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\93b930b80122b1e14adb\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.120Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\OneDrive\Desktop\coding\dodonx\shark (native)
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.127Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.147Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.149Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.156Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:28.231Z - Time taken for 'loadSpecifiedNxPlugins' 4.741200000000049ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.320Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\OneDrive\Desktop\coding\dodonx\shark\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1157.3312ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.354Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\OneDrive\Desktop\coding\dodonx\shark\node_modules\nx\src\plugins\package-json' 1188.2214ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.379Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.379Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.379Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.391Z - Time taken for 'loadDefaultNxPlugins' 1219.0077999999999ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.475Z - Time taken for 'build-project-configs' 47.84270000000015ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.619Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.621Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.622Z - Time taken for 'total for creating and serializing project graph' 1464.8600999999999ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.629Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:30:29.629Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1465. Response time: 8.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.500Z - [WATCHER]: libs/shared/types/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.503Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.608Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.608Z - [REQUEST]: libs/shared/types/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.608Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.619Z - Time taken for 'hash changed files from watcher' 2.5177999999868916ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.642Z - Time taken for 'build-project-configs' 20.822500000009313ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.694Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.695Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.695Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:17.695Z - Time taken for 'total execution time for createProjectGraph()' 38.39549999999872ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.702Z - [WATCHER]: libs/shared/constants/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.702Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.904Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.904Z - [REQUEST]: libs/shared/constants/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.904Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.919Z - Time taken for 'hash changed files from watcher' 0.20450000000710133ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:23.947Z - Time taken for 'build-project-configs' 22.364100000006147ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:24.000Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:24.001Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:24.001Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:24.001Z - Time taken for 'total execution time for createProjectGraph()' 35.40189999999711ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:29.871Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:29.872Z - [WATCHER]: libs/shared/utils/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.277Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.277Z - [REQUEST]: libs/shared/utils/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.277Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.280Z - Time taken for 'hash changed files from watcher' 0.20279999999911524ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.303Z - Time taken for 'build-project-configs' 19.384599999990314ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.337Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.337Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.337Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:30.338Z - Time taken for 'total execution time for createProjectGraph()' 24.963100000008126ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:37.552Z - [WATCHER]: libs/shared/ui-components/package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:37.563Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.361Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.361Z - [REQUEST]: libs/shared/ui-components/package.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.361Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.364Z - Time taken for 'hash changed files from watcher' 0.2450000000098953ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.388Z - Time taken for 'build-project-configs' 20.768700000000536ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.429Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.430Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.430Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:38.430Z - Time taken for 'total execution time for createProjectGraph()' 30.43520000000717ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:45.515Z - [WATCHER]: libs/shared/ui-components/src/index.ts was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:45.517Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.128Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.128Z - [REQUEST]: libs/shared/ui-components/src/index.ts
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.128Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.158Z - Time taken for 'hash changed files from watcher' 0.22809999997843988ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.181Z - Time taken for 'build-project-configs' 31.86030000000028ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.227Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.228Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.228Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:32:47.229Z - Time taken for 'total execution time for createProjectGraph()' 33.76209999999264ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.931Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.932Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.933Z - Time taken for 'total for creating and serializing project graph' 0.7439000000013039ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.933Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.933Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.940Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.940Z - Done responding to the client handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.941Z - Handled MULTI_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.950Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.951Z - Done responding to the client handleGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.951Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.956Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.956Z - Done responding to the client handleHashMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:22.956Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.380Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.380Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.381Z - Time taken for 'total for creating and serializing project graph' 0.7312999999849126ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.382Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.382Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.493Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.494Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.495Z - Time taken for 'total for creating and serializing project graph' 0.4805999999807682ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.496Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.497Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.503Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.503Z - Done responding to the client handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.504Z - Handled MULTI_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.506Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.506Z - Done responding to the client handleHashMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.506Z - Handled HASH_GLOB. Handling time: 0. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-26T04:34:23.532Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.805Z - [WATCHER]: libs/shared/ui-components/src/components/Button/Button.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.806Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.913Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.913Z - [REQUEST]: libs/shared/ui-components/src/components/Button/Button.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.914Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.916Z - Time taken for 'hash changed files from watcher' 0.2260000000242144ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.940Z - Time taken for 'build-project-configs' 19.81429999996908ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.982Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.983Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.983Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:42.983Z - Time taken for 'total execution time for createProjectGraph()' 28.506799999973737ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.215Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.217Z - [WATCHER]: libs/shared/ui-components/src/components/Card/Card.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.431Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.431Z - [REQUEST]: libs/shared/ui-components/src/components/Card/Card.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.431Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.437Z - Time taken for 'hash changed files from watcher' 1.0304999999934807ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.456Z - Time taken for 'build-project-configs' 17.48589999997057ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.494Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.495Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.495Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:38:54.496Z - Time taken for 'total execution time for createProjectGraph()' 29.04899999999907ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.553Z - [WATCHER]: libs/shared/ui-components/src/components/Input/Input.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.554Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.962Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.962Z - [REQUEST]: libs/shared/ui-components/src/components/Input/Input.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.962Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.968Z - Time taken for 'hash changed files from watcher' 0.3267999999807216ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:09.989Z - Time taken for 'build-project-configs' 18.510299999965355ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:10.024Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:10.025Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:10.025Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:10.025Z - Time taken for 'total execution time for createProjectGraph()' 26.934499999973923ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:31.976Z - [WATCHER]: libs/shared/ui-components/src/components/Spinner/Spinner.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:31.976Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.779Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.779Z - [REQUEST]: libs/shared/ui-components/src/components/Spinner/Spinner.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.779Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.783Z - Time taken for 'hash changed files from watcher' 0.27710000006482005ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.802Z - Time taken for 'build-project-configs' 16.240600000019185ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.833Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.834Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.834Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:32.834Z - Time taken for 'total execution time for createProjectGraph()' 20.40720000001602ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:43.395Z - [WATCHER]: libs/shared/ui-components/src/components/Modal/Modal.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:43.395Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:44.997Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:44.997Z - [REQUEST]: libs/shared/ui-components/src/components/Modal/Modal.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:44.997Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.000Z - Time taken for 'hash changed files from watcher' 0.28029999998398125ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.019Z - Time taken for 'build-project-configs' 14.724999999976717ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.051Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.052Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.052Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:45.052Z - Time taken for 'total execution time for createProjectGraph()' 23.41870000003837ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:52.710Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:52.713Z - [WATCHER]: libs/shared/ui-components/src/components/Avatar/Avatar.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.927Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.927Z - [REQUEST]: libs/shared/ui-components/src/components/Avatar/Avatar.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.927Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.929Z - Time taken for 'hash changed files from watcher' 0.15839999995660037ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.950Z - Time taken for 'build-project-configs' 15.73389999999199ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.984Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.985Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.985Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:39:55.985Z - Time taken for 'total execution time for createProjectGraph()' 26.926399999996647ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:01.830Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:01.833Z - [WATCHER]: libs/shared/ui-components/src/components/Badge/Badge.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.239Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.239Z - [REQUEST]: libs/shared/ui-components/src/components/Badge/Badge.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.239Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.246Z - Time taken for 'hash changed files from watcher' 0.6149000000441447ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.272Z - Time taken for 'build-project-configs' 20.74580000003334ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.309Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.310Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.310Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:08.311Z - Time taken for 'total execution time for createProjectGraph()' 31.11960000009276ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:17.053Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:17.059Z - [WATCHER]: libs/shared/ui-components/src/components/Dropdown/Dropdown.tsx was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.465Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.465Z - [REQUEST]: libs/shared/ui-components/src/components/Dropdown/Dropdown.tsx
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.465Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.472Z - Time taken for 'hash changed files from watcher' 0.28049999999348074ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.501Z - Time taken for 'build-project-configs' 25.59490000002552ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.585Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.586Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.586Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:23.586Z - Time taken for 'total execution time for createProjectGraph()' 69.9267000000691ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:33.490Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:33.493Z - [WATCHER]: libs/shared/types/project.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.903Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.903Z - [REQUEST]: libs/shared/types/project.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.903Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.908Z - Time taken for 'hash changed files from watcher' 0.540599999949336ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:39.954Z - Time taken for 'build-project-configs' 40.29719999991357ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.013Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.014Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.014Z - Time taken for 'total execution time for createProjectGraph()' 50.8355000000447ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.605Z - [WATCHER]: libs/shared/types/tsconfig.lib.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:40.605Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.008Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.009Z - [REQUEST]: libs/shared/types/tsconfig.lib.json
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.009Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.024Z - Time taken for 'hash changed files from watcher' 0.8610999999800697ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.052Z - Time taken for 'build-project-configs' 30.76899999997113ms
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.088Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.088Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.088Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-26T04:40:47.089Z - Time taken for 'total execution time for createProjectGraph()' 29.944999999948777ms
