{"version": "6.0", "nxVersion": "21.2.1", "pathMappings": {"@shark/shared/ui-components": ["libs/shared/ui-components/src/index.ts"], "@shark/shared/types": ["libs/shared/types/src/index.ts"], "@shark/shared/utils": ["libs/shared/utils/src/index.ts"], "@shark/shared/constants": ["libs/shared/constants/src/index.ts"], "@shark/customer": ["libs/customer/src/index.ts"], "@shark/vendor": ["libs/vendor/src/index.ts"], "@shark/admin": ["libs/admin/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": ".editorconfig", "hash": "5443105041930014821"}, {"file": "services/order-service/go.mod", "hash": "5603191363495060515"}, {"file": "services/notification-service/go.mod", "hash": "*******************"}, {"file": "README.md", "hash": "16073280826438291839"}, {"file": "libs/shared/ui-components/src/components/Card/Card.tsx", "hash": "13379593159199557829"}, {"file": "services/payment-service/internal/middleware/auth.go", "hash": "2594131583024586920"}, {"file": "services/user-service/internal/repository/user_repository.go", "hash": "*******************"}, {"file": ".giti<PERSON>re", "hash": "14507554082285941040"}, {"file": "libs/shared/ui-components/src/components/Input/Input.tsx", "hash": "*******************"}, {"file": "services/product-service/internal/models/product.go", "hash": "12839053873709390856"}, {"file": "services/payment-service/internal/config/config.go", "hash": "14654168479703266658"}, {"file": "services/booking-service/internal/services/availability_service.go", "hash": "11431452827624762510"}, {"file": "services/order-service/cmd/main.go", "hash": "11737731927517714024"}, {"file": "services/booking-service/go.mod", "hash": "5387075379712547971"}, {"file": "services/payment-service/go.mod", "hash": "*******************"}, {"file": "libs/shared/ui-components/src/components/Button/Button.tsx", "hash": "14015571202765753770"}, {"file": "services/service-catalog/internal/services/variant_service.go", "hash": "16614243147255413168"}, {"file": "services/review-service/cmd/main.go", "hash": "16258933596796085267"}, {"file": "libs/shared/ui-components/src/components/Spinner/Spinner.tsx", "hash": "9512777642940281091"}, {"file": "services/review-service/go.sum", "hash": "10558235300218976548"}, {"file": "services/payment-service/internal/repository/payment_repository.go", "hash": "9814393610049284263"}, {"file": "tsconfig.base.json", "hash": "1206537870262394623"}, {"file": "services/user-service/internal/handlers/auth_handler.go", "hash": "2516032839410104605"}, {"file": "services/booking-service/internal/handlers/booking_handler.go", "hash": "15067862772764062091"}, {"file": "services/payment-service/internal/services/payment_service.go", "hash": "6465533481428019739"}, {"file": "libs/shared/ui-components/src/components/Avatar/Avatar.tsx", "hash": "17978128378919132945"}, {"file": "services/service-catalog/cmd/main.go", "hash": "3963421619174509516"}, {"file": "libs/shared/utils/package.json", "hash": "14230491973115361734"}, {"file": "services/service-catalog/.env.example", "hash": "5337367040098554402"}, {"file": "services/service-catalog/internal/handlers/variant_handler.go", "hash": "17975624773986523069"}, {"file": "services/service-catalog/internal/handlers/service_handler.go", "hash": "7495246351687506285"}, {"file": "database/go.mod", "hash": "4423950300816596679"}, {"file": "services/service-catalog/internal/config/config.go", "hash": "508712504963272197"}, {"file": "services/booking-service/internal/services/booking_service.go", "hash": "12472470311862450187"}, {"file": "services/booking-service/.env.example", "hash": "13446768579205124707"}, {"file": "services/notification-service/go.sum", "hash": "10558235300218976548"}, {"file": "services/service-catalog/internal/services/category_service.go", "hash": "14418965604574061749"}, {"file": "services/payment-service/internal/handlers/payment_handler.go", "hash": "16975754111219473448"}, {"file": "services/product-service/internal/services/product_variant_service.go", "hash": "7779709716966634062"}, {"file": "services/booking-service/go.sum", "hash": "51958465708738389"}, {"file": "services/product-service/go.mod", "hash": "5657774651950366588"}, {"file": "services/product-service/go.sum", "hash": "16525285326504231567"}, {"file": "services/payment-service/cmd/main.go", "hash": "13652352348683739271"}, {"file": "services/product-service/internal/handlers/product_variant_handler.go", "hash": "9803948997921816283"}, {"file": "services/service-catalog/internal/middleware/auth.go", "hash": "11309319215430565007"}, {"file": "services/user-service/README.md", "hash": "15982605518905931640"}, {"file": "database/migrations/006_add_variants_and_addons.sql", "hash": "13970662738167292865"}, {"file": "libs/shared/utils/src/index.ts", "hash": "3895136279849134011"}, {"file": "database/seeds/sample_data.sql", "hash": "13590348411743245328"}, {"file": "database/schemas/init.sql", "hash": "6437750427317587748"}, {"file": "services/user-service/Dockerfile", "hash": "15405630057215672723"}, {"file": "services/analytics-service/cmd/main.go", "hash": "8848663184499327765"}, {"file": "services/service-catalog/internal/repository/category_repository.go", "hash": "15870467123197431667"}, {"file": "services/booking-service/internal/config/config.go", "hash": "18309655228233201354"}, {"file": "services/user-service/internal/repository/role_repository.go", "hash": "15081286440970397738"}, {"file": "docker-compose.yml", "hash": "1925207611585819933"}, {"file": "libs/shared/ui-components/src/components/Badge/Badge.tsx", "hash": "5356344937095701952"}, {"file": "services/analytics-service/go.sum", "hash": "10558235300218976548"}, {"file": "services/user-service/.env.example", "hash": "5279662879724511638"}, {"file": "libs/shared/ui-components/src/components/Modal/Modal.tsx", "hash": "9955660126984467847"}, {"file": "services/booking-service/internal/handlers/availability_handler.go", "hash": "2686193291485696982"}, {"file": "services/user-service/internal/handlers/user_handler.go", "hash": "1754701193631211169"}, {"file": "services/booking-service/internal/repository/availability_repository.go", "hash": "2741759911935136296"}, {"file": "services/payment-service/go.sum", "hash": "17283238013495244827"}, {"file": "services/user-service/internal/services/user_service.go", "hash": "2286274164970018345"}, {"file": "services/user-service/internal/services/auth_service.go", "hash": "6864436261554489907"}, {"file": "services/user-service/go.sum", "hash": "1102238564653326899"}, {"file": "database/go.sum", "hash": "13802777141461892676"}, {"file": "services/booking-service/cmd/main.go", "hash": "874065588210230385"}, {"file": "services/user-service/internal/middleware/auth.go", "hash": "2881894761414403502"}, {"file": "database/migrate.go", "hash": "8699562659395508799"}, {"file": "package.json", "hash": "2896754191538028432"}, {"file": "services/service-catalog/internal/services/service_service.go", "hash": "16831184661770400985"}, {"file": "libs/shared/constants/package.json", "hash": "10565926970300603532"}, {"file": "services/notification-service/cmd/main.go", "hash": "5720445134856073206"}, {"file": "services/service-catalog/go.sum", "hash": "51958465708738389"}, {"file": "libs/shared/ui-components/src/index.ts", "hash": "5200880867290526606"}, {"file": ".vscode/extensions.json", "hash": "3833545590435012078"}, {"file": "libs/shared/ui-components/package.json", "hash": "9223956466176375941"}, {"file": "services/review-service/go.mod", "hash": "17739131757350878789"}, {"file": "services/service-catalog/internal/repository/service_repository.go", "hash": "17000658170965731723"}, {"file": "services/payment-service/internal/models/payment.go", "hash": "13923866543946118411"}, {"file": "services/booking-service/internal/repository/booking_repository.go", "hash": "11045740099842553214"}, {"file": "services/product-service/cmd/main.go", "hash": "9816697506608980793"}, {"file": "services/booking-service/internal/models/booking.go", "hash": "11659346108113022650"}, {"file": "package-lock.json", "hash": "16232239202108223990"}, {"file": "services/booking-service/internal/middleware/auth.go", "hash": "11309319215430565007"}, {"file": "services/user-service/internal/models/user.go", "hash": "17768298651513677714"}, {"file": "services/service-catalog/internal/repository/variant_repository.go", "hash": "18197770012837089858"}, {"file": "services/user-service/cmd/main.go", "hash": "11961781328008569610"}, {"file": "services/service-catalog/go.mod", "hash": "1898789426254194097"}, {"file": "services/analytics-service/go.mod", "hash": "9564907057095469311"}, {"file": "services/order-service/go.sum", "hash": "10558235300218976548"}, {"file": "services/service-catalog/internal/models/service.go", "hash": "18220941852900503351"}, {"file": "services/service-catalog/internal/handlers/category_handler.go", "hash": "9063862334246361637"}, {"file": "services/user-service/go.mod", "hash": "3365492214139205673"}, {"file": "services/product-service/internal/repository/product_variant_repository.go", "hash": "7719605013740622734"}, {"file": "services/user-service/internal/config/config.go", "hash": "17379564870949559096"}, {"file": "libs/shared/ui-components/src/components/Dropdown/Dropdown.tsx", "hash": "8059291971035999431"}, {"file": "nx.json", "hash": "14977852485590650733"}, {"file": "libs/shared/constants/src/index.ts", "hash": "8372692330286801310"}], "projectFileMap": {"shared-types": [{"file": "libs/shared/types/package.json", "hash": "2030705768070659394"}, {"file": "libs/shared/types/project.json", "hash": "14570513658430381633"}, {"file": "libs/shared/types/src/index.ts", "hash": "4516684862106513872"}, {"file": "libs/shared/types/tsconfig.lib.json", "hash": "6528012108708162577"}]}}}