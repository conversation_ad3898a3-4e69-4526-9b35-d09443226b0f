{"version": "6.0", "nxVersion": "21.2.1", "pathMappings": {"@shark/shared/ui-components": ["libs/shared/ui-components/src/index.ts"], "@shark/shared/types": ["libs/shared/types/src/index.ts"], "@shark/shared/utils": ["libs/shared/utils/src/index.ts"], "@shark/shared/constants": ["libs/shared/constants/src/index.ts"], "@shark/customer": ["libs/customer/src/index.ts"], "@shark/vendor": ["libs/vendor/src/index.ts"], "@shark/admin": ["libs/admin/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": "services/user-service/internal/repository/user_repository.go", "hash": "4635995611179498107"}, {"file": "services/service-catalog/internal/handlers/variant_handler.go", "hash": "17975624773986523069"}, {"file": "services/scheduling-service/internal/models/scheduling.go", "hash": "14639475241720216390"}, {"file": "services/user-service/cmd/main.go", "hash": "14744525254297102906"}, {"file": "database/enhanced-schema.sql", "hash": "16707216126611159128"}, {"file": "services/booking-service/internal/handlers/availability_handler.go", "hash": "2686193291485696982"}, {"file": "test-database-apis.ps1", "hash": "4202397364937789611"}, {"file": "database/cmd/add-sample-data/go.sum", "hash": "13802777141461892676"}, {"file": "services/cart-service/internal/repository/tax_repository.go", "hash": "7239887886394765394"}, {"file": "database/cmd/verify-tables/main.go", "hash": "8170191344433967512"}, {"file": "database/cmd/add-enhanced-tables/go.sum", "hash": "13802777141461892676"}, {"file": "database/cmd/verify-tables/go.sum", "hash": "13802777141461892676"}, {"file": "services/payment-service/internal/config/config.go", "hash": "14654168479703266658"}, {"file": "shared/go.mod", "hash": "16317853197569334125"}, {"file": "services/booking-service/internal/repository/availability_repository.go", "hash": "2741759911935136296"}, {"file": "services/scheduling-service/internal/service/scheduling_service.go", "hash": "14426867044355683733"}, {"file": "services/product-service/internal/models/product.go", "hash": "1451884927898539119"}, {"file": "services/user-service/internal/config/config.go", "hash": "14310083417354846773"}, {"file": "services/wishlist-service/go.mod", "hash": "1860836642323291443"}, {"file": "database/cmd/add-sample-data-simple/go.sum", "hash": "13802777141461892676"}, {"file": "database/cmd/add-enhanced-sample-data/go.mod", "hash": "17782328389277851450"}, {"file": "services/booking-service/internal/services/booking_service.go", "hash": "12472470311862450187"}, {"file": "services/payment-service/internal/services/payment_service.go", "hash": "6465533481428019739"}, {"file": "FINAL_COMPLETION_SUMMARY.md", "hash": "17878580742633244867"}, {"file": "remove-duplicate-db-connections.md", "hash": "16423448415540165682"}, {"file": "services/service-catalog/internal/services/category_service.go", "hash": "14418965604574061749"}, {"file": "services/notification-service/go.sum", "hash": "10558235300218976548"}, {"file": "services/address-shipping-service/internal/models/address_shipping.go", "hash": "12711787392256963523"}, {"file": "database/cmd/create-tables/main.go", "hash": "8170387736501794903"}, {"file": "config/database.env", "hash": "1222430613404161447"}, {"file": "find-duplicate-db-connections.ps1", "hash": "6867565067598079608"}, {"file": "database/cmd/check-category-structure/go.sum", "hash": "13802777141461892676"}, {"file": "services/review-service/go.mod", "hash": "17739131757350878789"}, {"file": "services/cart-service/internal/repository/fee_repository.go", "hash": "11132359994236591284"}, {"file": "database/cmd/create-tables/go.sum", "hash": "13802777141461892676"}, {"file": "api-status-report.ps1", "hash": "784582700105355614"}, {"file": ".giti<PERSON>re", "hash": "14507554082285941040"}, {"file": "services/product-service/go.mod", "hash": "5657774651950366588"}, {"file": "libs/shared/ui-components/src/components/Spinner/Spinner.tsx", "hash": "9512777642940281091"}, {"file": "services/scheduling-service/internal/handlers/scheduling_handler.go", "hash": "17893081706050341271"}, {"file": "services/service-catalog/go.mod", "hash": "1898789426254194097"}, {"file": "package.json", "hash": "2896754191538028432"}, {"file": "database/cmd/add-enhanced-sample-data/go.sum", "hash": "13802777141461892676"}, {"file": "services/user-service/internal/services/auth_service.go", "hash": "6864436261554489907"}, {"file": "services/user-service/go.sum", "hash": "1102238564653326899"}, {"file": "database/test-connection.go", "hash": "555292062524436943"}, {"file": "services/analytics-service/cmd/main.go", "hash": "8848663184499327765"}, {"file": "services/analytics-service/go.sum", "hash": "10558235300218976548"}, {"file": "config/shared.env", "hash": "11159383063951270181"}, {"file": "compare-config-systems.ps1", "hash": "3325489518331707794"}, {"file": "services/booking-service/go.sum", "hash": "51958465708738389"}, {"file": "database/create-tables.go", "hash": "13592643618795930354"}, {"file": "config/services.env", "hash": "6816653300402085572"}, {"file": "services/service-catalog/internal/repository/variant_repository.go", "hash": "18197770012837089858"}, {"file": "services/tax-fee-service/internal/repository/tax_repository.go", "hash": "11211110985487729522"}, {"file": "database/go.sum", "hash": "13802777141461892676"}, {"file": "services/product-service/internal/handlers/product_variant_handler.go", "hash": "9803948997921816283"}, {"file": "shared/database/connection.go", "hash": "6602988727657475322"}, {"file": "docker-compose.yml", "hash": "1925207611585819933"}, {"file": "services/booking-service/cmd/main.go", "hash": "874065588210230385"}, {"file": "test-apis.ps1", "hash": "12969210694267892457"}, {"file": "services/user-service/go.mod", "hash": "3365492214139205673"}, {"file": "services/analytics-service/go.mod", "hash": "9564907057095469311"}, {"file": "tsconfig.base.json", "hash": "1206537870262394623"}, {"file": "services/wishlist-service/cmd/main.go", "hash": "3664111910003226875"}, {"file": "database/test-uuid-extension.go", "hash": "795942304145422532"}, {"file": ".editorconfig", "hash": "5443105041930014821"}, {"file": "check-db-setup.ps1", "hash": "16767263619816409522"}, {"file": "services/coupon-service/internal/models/coupon.go", "hash": "4698287960099515156"}, {"file": "database/cmd/add-sample-data-simple/go.mod", "hash": "13673428283899120656"}, {"file": "database/go.mod", "hash": "4423950300816596679"}, {"file": "database/cmd/show-sample-data/main.go", "hash": "11495476549099086621"}, {"file": "MICROSERVICES_COMPLETION_STATUS.md", "hash": "7858298083229384309"}, {"file": "services/scheduling-service/go.mod", "hash": "9060154970407033901"}, {"file": "services/cart-service/cmd/main.go", "hash": "15491370049144987540"}, {"file": "database/run-in-pgadmin.sql", "hash": "12153109692996462740"}, {"file": "database/migrate.go", "hash": "8699562659395508799"}, {"file": "services/order-service/go.mod", "hash": "5603191363495060515"}, {"file": "database/cmd/add-enhanced-tables/go.mod", "hash": "2524003892401844645"}, {"file": "services/product-service/internal/repository/product_variant_repository.go", "hash": "7719605013740622734"}, {"file": "database/cmd/check-category-structure/go.mod", "hash": "7509370133361980780"}, {"file": "libs/shared/ui-components/src/components/Dropdown/Dropdown.tsx", "hash": "8059291971035999431"}, {"file": "services/booking-service/internal/services/availability_service.go", "hash": "11431452827624762510"}, {"file": "test-config-system.ps1", "hash": "7932888477378077713"}, {"file": "database/inspect-db.go", "hash": "13982200110588838210"}, {"file": "services/scheduling-service/internal/repository/scheduling_repository.go", "hash": "5186246093977457552"}, {"file": "services/product-service/internal/repository/product_repository.go", "hash": "12426730406204258555"}, {"file": "services/service-catalog/internal/middleware/auth.go", "hash": "11309319215430565007"}, {"file": "services/user-service/Dockerfile", "hash": "15405630057215672723"}, {"file": "services/user-service/internal/middleware/auth.go", "hash": "2881894761414403502"}, {"file": "services/tax-fee-service/cmd/main.go", "hash": "12832857244382522782"}, {"file": "database/cmd/show-enhanced-features/go.sum", "hash": "13802777141461892676"}, {"file": "services/cart-service/internal/service/cart_service.go", "hash": "9895048305277830424"}, {"file": "services/service-catalog/internal/services/service_service.go", "hash": "16831184661770400985"}, {"file": "services/tax-fee-service/internal/handlers/tax_fee_handler.go", "hash": "3005555230126295573"}, {"file": "database/cmd/add-sample-data/go.mod", "hash": "17905756835694009785"}, {"file": "services/payment-service/internal/repository/payment_repository.go", "hash": "9814393610049284263"}, {"file": "services/payment-service/internal/models/payment.go", "hash": "13923866543946118411"}, {"file": "database/schemas/init.sql", "hash": "6437750427317587748"}, {"file": "test-user-service.ps1", "hash": "16531350008051133444"}, {"file": "database/cmd/show-sample-data/go.sum", "hash": "13802777141461892676"}, {"file": "services/payment-service/go.sum", "hash": "17283238013495244827"}, {"file": "database/create-all-tables.sql", "hash": "17957883991749160115"}, {"file": "services/payment-service/go.mod", "hash": "4715910787764700011"}, {"file": "services/booking-service/internal/config/config.go", "hash": "18309655228233201354"}, {"file": "services/coupon-service/cmd/main.go", "hash": "15309429170956168949"}, {"file": "services/service-catalog/internal/repository/service_repository.go", "hash": "17000658170965731723"}, {"file": "services/user-service/internal/handlers/user_handler.go", "hash": "7305742803606129836"}, {"file": "services/cart-service/internal/repository/cart_repository.go", "hash": "11198023846357083692"}, {"file": "services/order-service/go.sum", "hash": "10558235300218976548"}, {"file": "database/diagnose-postgresql.ps1", "hash": "8932302912334397955"}, {"file": "database/cmd/create-tables/go.mod", "hash": "25661490438158578"}, {"file": "services/service-catalog/internal/models/service.go", "hash": "18220941852900503351"}, {"file": "database/migrations/006_add_variants_and_addons.sql", "hash": "13970662738167292865"}, {"file": "database/cmd/show-enhanced-features/main.go", "hash": "13127454248906221040"}, {"file": "services/user-service/internal/services/user_service.go", "hash": "2286274164970018345"}, {"file": "services/service-catalog/internal/config/config.go", "hash": "508712504963272197"}, {"file": "libs/shared/ui-components/src/index.ts", "hash": "5200880867290526606"}, {"file": "services/service-catalog/internal/handlers/category_handler.go", "hash": "9063862334246361637"}, {"file": "services/service-catalog/internal/services/variant_service.go", "hash": "16614243147255413168"}, {"file": "services/payment-service/internal/middleware/auth.go", "hash": "2594131583024586920"}, {"file": "services/review-service/go.sum", "hash": "10558235300218976548"}, {"file": "go.sum", "hash": "13802777141461892676"}, {"file": "services/tax-fee-service/go.mod", "hash": "5480979064804092450"}, {"file": "services/cart-service/internal/handlers/cart_handler.go", "hash": "5361230963025549671"}, {"file": "services/coupon-service/internal/handlers/coupon_handler.go", "hash": "13070661133189071224"}, {"file": "services/tax-fee-service/internal/repository/fee_repository.go", "hash": "10708205718161263443"}, {"file": "database/cmd/add-sample-data-simple/main.go", "hash": "15449895235289859166"}, {"file": "services/booking-service/.env.example", "hash": "13446768579205124707"}, {"file": "services/cart-service/internal/repository/coupon_repository.go", "hash": "17244006117495318945"}, {"file": "database/setup-database.go", "hash": "14984137476374097994"}, {"file": "libs/shared/ui-components/src/components/Card/Card.tsx", "hash": "13379593159199557829"}, {"file": "nx.json", "hash": "14977852485590650733"}, {"file": "services/cart-service/internal/models/cart.go", "hash": "16877303713426873904"}, {"file": "libs/shared/ui-components/src/components/Button/Button.tsx", "hash": "14015571202765753770"}, {"file": "analyze-project-issues.ps1", "hash": "506032369371594818"}, {"file": "database/add-missing-tables.sql", "hash": "9268812628837098976"}, {"file": "services/user-service/internal/repository/role_repository.go", "hash": "15081286440970397738"}, {"file": "libs/shared/constants/package.json", "hash": "10565926970300603532"}, {"file": "services/review-service/cmd/main.go", "hash": "16258933596796085267"}, {"file": "services/user-service/.env.example", "hash": "5279662879724511638"}, {"file": "show-config-structure.ps1", "hash": "14314471391199122631"}, {"file": "REMAINING_SERVICES_COMPLETION.md", "hash": "12842777540783686974"}, {"file": "test-shared-database.go", "hash": "384112866124138346"}, {"file": "database/seeds/sample_data.sql", "hash": "13590348411743245328"}, {"file": "services/booking-service/internal/repository/booking_repository.go", "hash": "11045740099842553214"}, {"file": ".vscode/extensions.json", "hash": "3833545590435012078"}, {"file": "database/cmd/add-sample-data/main.go", "hash": "1340874398255976071"}, {"file": "shared/config/loader.go", "hash": "10335608173192338126"}, {"file": "services/cart-service/go.mod", "hash": "12379475827722305229"}, {"file": "database/COMPLETE_DATABASE_STATUS.md", "hash": "6442016450676287821"}, {"file": "libs/shared/utils/package.json", "hash": "14230491973115361734"}, {"file": "database/verify-setup.sql", "hash": "9068346457457827045"}, {"file": "libs/shared/ui-components/src/components/Input/Input.tsx", "hash": "4558912545023761021"}, {"file": "services/service-catalog/.env.example", "hash": "5337367040098554402"}, {"file": "services/coupon-service/go.mod", "hash": "12580592640234553979"}, {"file": "services/product-service/internal/services/product_variant_service.go", "hash": "7779709716966634062"}, {"file": "test-current-db-config.ps1", "hash": "17044326112091813182"}, {"file": "verify-database-setup.ps1", "hash": "16061912306617934748"}, {"file": "services/address-shipping-service/go.mod", "hash": "5671725062041310525"}, {"file": "services/payment-service/cmd/main.go", "hash": "13652352348683739271"}, {"file": "services/product-service/cmd/main.go", "hash": "7637953634337768519"}, {"file": "services/user-service/internal/models/user.go", "hash": "17768298651513677714"}, {"file": "services/coupon-service/internal/repository/coupon_repository.go", "hash": "381842665397676966"}, {"file": "services/user-service/internal/handlers/auth_handler.go", "hash": "2516032839410104605"}, {"file": "services/notification-service/cmd/main.go", "hash": "5720445134856073206"}, {"file": "services/payment-service/internal/handlers/payment_handler.go", "hash": "16975754111219473448"}, {"file": "services/service-catalog/internal/handlers/service_handler.go", "hash": "7495246351687506285"}, {"file": "database/cmd/add-enhanced-tables/main.go", "hash": "4321014308995334580"}, {"file": "libs/shared/ui-components/src/components/Badge/Badge.tsx", "hash": "5356344937095701952"}, {"file": "services/product-service/go.sum", "hash": "16525285326504231567"}, {"file": "services/service-catalog/go.sum", "hash": "51958465708738389"}, {"file": "database/cmd/show-enhanced-features/go.mod", "hash": "7232274699366921213"}, {"file": "ENHANCED_MICROSERVICES_SUMMARY.md", "hash": "978430878562719112"}, {"file": "database/ENHANCED_SCHEMA_SUMMARY.md", "hash": "16111008030930033995"}, {"file": "services/tax-fee-service/internal/service/tax_fee_service.go", "hash": "11650058853812106785"}, {"file": "services/booking-service/internal/handlers/booking_handler.go", "hash": "15067862772764062091"}, {"file": "services/product-service/internal/config/config.go", "hash": "10886606439734963802"}, {"file": "services/user-service/README.md", "hash": "15982605518905931640"}, {"file": "database/test-manual-connection.go", "hash": "3540564385038418297"}, {"file": "docs/ENHANCED_API_DOCUMENTATION.md", "hash": "11113014112182614027"}, {"file": "services/coupon-service/internal/service/coupon_service.go", "hash": "7470846415405034840"}, {"file": "libs/shared/ui-components/src/components/Avatar/Avatar.tsx", "hash": "17978128378919132945"}, {"file": "libs/shared/constants/src/index.ts", "hash": "8372692330286801310"}, {"file": "services/service-catalog/cmd/main.go", "hash": "3963421619174509516"}, {"file": "database/create-tables-ordered.sql", "hash": "18365364271351381025"}, {"file": "services/booking-service/go.mod", "hash": "5387075379712547971"}, {"file": "database/cmd/show-sample-data/go.mod", "hash": "3910220893582075961"}, {"file": "libs/shared/ui-components/src/components/Modal/Modal.tsx", "hash": "9955660126984467847"}, {"file": "database/sample-data.sql", "hash": "6616027839550181009"}, {"file": "database/setup-with-psql.ps1", "hash": "16156565129154979267"}, {"file": "database/pgadmin-setup-guide.md", "hash": "6993014804900042632"}, {"file": "package-lock.json", "hash": "16232239202108223990"}, {"file": "README.md", "hash": "16073280826438291839"}, {"file": "services/notification-service/go.mod", "hash": "4880725429907094558"}, {"file": "go.mod", "hash": "58273605373484584"}, {"file": "services/tax-fee-service/internal/models/tax_fee.go", "hash": "15383941992586465212"}, {"file": "services/service-catalog/internal/repository/category_repository.go", "hash": "15870467123197431667"}, {"file": "database/cmd/check-category-structure/main.go", "hash": "9331942832559621440"}, {"file": "database/test-auth.ps1", "hash": "5113667904167181887"}, {"file": "services/booking-service/internal/models/booking.go", "hash": "11659346108113022650"}, {"file": "final-verification.ps1", "hash": "7894573037248819678"}, {"file": "database/cmd/add-enhanced-sample-data/main.go", "hash": "18374247461493692847"}, {"file": "database/cmd/verify-tables/go.mod", "hash": "5273674957283445276"}, {"file": "TAX_FEE_SERVICE_COMPLETION.md", "hash": "6421588215354892716"}, {"file": "services/address-shipping-service/cmd/main.go", "hash": "3524799541661716409"}, {"file": "libs/shared/utils/src/index.ts", "hash": "3895136279849134011"}, {"file": "services/booking-service/internal/middleware/auth.go", "hash": "11309319215430565007"}, {"file": "libs/shared/ui-components/package.json", "hash": "9223956466176375941"}, {"file": "services/scheduling-service/cmd/main.go", "hash": "3840153194154451523"}, {"file": "services/order-service/cmd/main.go", "hash": "11737731927517714024"}], "projectFileMap": {"shared-types": [{"file": "libs/shared/types/package.json", "hash": "2030705768070659394"}, {"file": "libs/shared/types/project.json", "hash": "14570513658430381633"}, {"file": "libs/shared/types/src/index.ts", "hash": "4516684862106513872"}, {"file": "libs/shared/types/tsconfig.lib.json", "hash": "6528012108708162577"}]}}}