{"version": "6.0", "nxVersion": "21.2.1", "pathMappings": {"@shark/shared/ui-components": ["libs/shared/ui-components/src/index.ts"], "@shark/shared/types": ["libs/shared/types/src/index.ts"], "@shark/shared/utils": ["libs/shared/utils/src/index.ts"], "@shark/shared/constants": ["libs/shared/constants/src/index.ts"], "@shark/customer": ["libs/customer/src/index.ts"], "@shark/vendor": ["libs/vendor/src/index.ts"], "@shark/admin": ["libs/admin/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": "services/service-catalog/cmd/main.go", "hash": "4267902363984125663"}, {"file": "services/booking-service/go.sum", "hash": "51958465708738389"}, {"file": "services/service-catalog/internal/handlers/service_handler.go", "hash": "7495246351687506285"}, {"file": "services/booking-service/internal/models/booking.go", "hash": "11659346108113022650"}, {"file": "libs/shared/ui-components/src/index.ts", "hash": "5200880867290526606"}, {"file": "services/service-catalog/internal/models/service.go", "hash": "12374817478598840225"}, {"file": "services/booking-service/internal/middleware/auth.go", "hash": "11309319215430565007"}, {"file": "database/seeds/sample_data.sql", "hash": "13590348411743245328"}, {"file": "services/service-catalog/internal/handlers/category_handler.go", "hash": "9063862334246361637"}, {"file": "package-lock.json", "hash": "16232239202108223990"}, {"file": "services/service-catalog/internal/services/category_service.go", "hash": "14418965604574061749"}, {"file": "libs/shared/ui-components/src/components/Input/Input.tsx", "hash": "4558912545023761021"}, {"file": "libs/shared/utils/src/index.ts", "hash": "3895136279849134011"}, {"file": "services/user-service/cmd/main.go", "hash": "11961781328008569610"}, {"file": "libs/shared/utils/package.json", "hash": "14230491973115361734"}, {"file": "libs/shared/constants/src/index.ts", "hash": "8372692330286801310"}, {"file": "services/user-service/internal/models/user.go", "hash": "17768298651513677714"}, {"file": "services/booking-service/internal/handlers/booking_handler.go", "hash": "15067862772764062091"}, {"file": "services/user-service/README.md", "hash": "15982605518905931640"}, {"file": "libs/shared/ui-components/src/components/Dropdown/Dropdown.tsx", "hash": "8059291971035999431"}, {"file": "README.md", "hash": "16073280826438291839"}, {"file": "services/user-service/.env.example", "hash": "5279662879724511638"}, {"file": ".giti<PERSON>re", "hash": "14507554082285941040"}, {"file": "services/user-service/internal/config/config.go", "hash": "17379564870949559096"}, {"file": "libs/shared/ui-components/package.json", "hash": "9223956466176375941"}, {"file": "services/booking-service/.env.example", "hash": "13446768579205124707"}, {"file": "libs/shared/ui-components/src/components/Spinner/Spinner.tsx", "hash": "9512777642940281091"}, {"file": "libs/shared/ui-components/src/components/Button/Button.tsx", "hash": "14015571202765753770"}, {"file": "services/user-service/internal/middleware/auth.go", "hash": "2881894761414403502"}, {"file": ".editorconfig", "hash": "5443105041930014821"}, {"file": "services/user-service/internal/repository/user_repository.go", "hash": "4635995611179498107"}, {"file": "services/service-catalog/go.mod", "hash": "1898789426254194097"}, {"file": "services/user-service/go.sum", "hash": "1102238564653326899"}, {"file": "services/service-catalog/.env.example", "hash": "5337367040098554402"}, {"file": "services/booking-service/internal/config/config.go", "hash": "18309655228233201354"}, {"file": "services/service-catalog/internal/middleware/auth.go", "hash": "11309319215430565007"}, {"file": "services/service-catalog/internal/repository/service_repository.go", "hash": "17000658170965731723"}, {"file": "nx.json", "hash": "14977852485590650733"}, {"file": "services/booking-service/internal/services/availability_service.go", "hash": "11431452827624762510"}, {"file": "services/user-service/go.mod", "hash": "3365492214139205673"}, {"file": "libs/shared/ui-components/src/components/Card/Card.tsx", "hash": "13379593159199557829"}, {"file": "libs/shared/ui-components/src/components/Avatar/Avatar.tsx", "hash": "17978128378919132945"}, {"file": "libs/shared/ui-components/src/components/Modal/Modal.tsx", "hash": "9955660126984467847"}, {"file": "package.json", "hash": "2896754191538028432"}, {"file": "services/service-catalog/internal/config/config.go", "hash": "508712504963272197"}, {"file": "services/booking-service/internal/services/booking_service.go", "hash": "12472470311862450187"}, {"file": "services/booking-service/internal/handlers/availability_handler.go", "hash": "2686193291485696982"}, {"file": "services/booking-service/go.mod", "hash": "5387075379712547971"}, {"file": "database/schemas/init.sql", "hash": "6437750427317587748"}, {"file": "services/booking-service/cmd/main.go", "hash": "874065588210230385"}, {"file": "tsconfig.base.json", "hash": "1206537870262394623"}, {"file": "docker-compose.yml", "hash": "1925207611585819933"}, {"file": ".vscode/extensions.json", "hash": "3833545590435012078"}, {"file": "services/service-catalog/internal/repository/category_repository.go", "hash": "15870467123197431667"}, {"file": "services/booking-service/internal/repository/availability_repository.go", "hash": "2741759911935136296"}, {"file": "libs/shared/constants/package.json", "hash": "10565926970300603532"}, {"file": "services/user-service/internal/handlers/user_handler.go", "hash": "1754701193631211169"}, {"file": "services/booking-service/internal/repository/booking_repository.go", "hash": "11045740099842553214"}, {"file": "services/user-service/internal/repository/role_repository.go", "hash": "15081286440970397738"}, {"file": "libs/shared/ui-components/src/components/Badge/Badge.tsx", "hash": "5356344937095701952"}, {"file": "services/user-service/internal/services/user_service.go", "hash": "2286274164970018345"}, {"file": "services/user-service/Dockerfile", "hash": "15405630057215672723"}, {"file": "services/user-service/internal/services/auth_service.go", "hash": "6864436261554489907"}, {"file": "services/service-catalog/internal/services/service_service.go", "hash": "1181189360706337768"}, {"file": "services/user-service/internal/handlers/auth_handler.go", "hash": "2516032839410104605"}, {"file": "services/service-catalog/go.sum", "hash": "51958465708738389"}], "projectFileMap": {"shared-types": [{"file": "libs/shared/types/package.json", "hash": "2030705768070659394"}, {"file": "libs/shared/types/project.json", "hash": "14570513658430381633"}, {"file": "libs/shared/types/src/index.ts", "hash": "4516684862106513872"}, {"file": "libs/shared/types/tsconfig.lib.json", "hash": "6528012108708162577"}]}}}