{"version": "6.0", "nxVersion": "21.2.1", "pathMappings": {"@shark/shared/ui-components": ["libs/shared/ui-components/src/index.ts"], "@shark/shared/types": ["libs/shared/types/src/index.ts"], "@shark/shared/utils": ["libs/shared/utils/src/index.ts"], "@shark/shared/constants": ["libs/shared/constants/src/index.ts"], "@shark/customer": ["libs/customer/src/index.ts"], "@shark/vendor": ["libs/vendor/src/index.ts"], "@shark/admin": ["libs/admin/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": "README.md", "hash": "16073280826438291839"}, {"file": "libs/shared/types/package.json", "hash": "2030705768070659394"}, {"file": ".vscode/extensions.json", "hash": "3833545590435012078"}, {"file": "docker-compose.yml", "hash": "1925207611585819933"}, {"file": "database/schemas/init.sql", "hash": "6437750427317587748"}, {"file": "database/seeds/sample_data.sql", "hash": "13590348411743245328"}, {"file": "package-lock.json", "hash": "16232239202108223990"}, {"file": ".giti<PERSON>re", "hash": "14507554082285941040"}, {"file": "libs/shared/constants/src/index.ts", "hash": "8372692330286801310"}, {"file": "libs/shared/utils/src/index.ts", "hash": "3895136279849134011"}, {"file": "nx.json", "hash": "14977852485590650733"}, {"file": "libs/shared/types/src/index.ts", "hash": "4516684862106513872"}, {"file": ".editorconfig", "hash": "5443105041930014821"}, {"file": "libs/shared/constants/package.json", "hash": "10565926970300603532"}, {"file": "libs/shared/utils/package.json", "hash": "14230491973115361734"}, {"file": "package.json", "hash": "2896754191538028432"}, {"file": "libs/shared/ui-components/src/index.ts", "hash": "5200880867290526606"}, {"file": "libs/shared/ui-components/package.json", "hash": "9223956466176375941"}, {"file": "tsconfig.base.json", "hash": "1206537870262394623"}], "projectFileMap": {}}}