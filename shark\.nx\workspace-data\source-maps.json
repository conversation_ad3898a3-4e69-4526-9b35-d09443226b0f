{"libs/shared/types": {"root": ["libs/shared/types/project.json", "nx/core/project-json"], "name": ["libs/shared/types/project.json", "nx/core/project-json"], "tags": ["libs/shared/types/package.json", "nx/core/package-json"], "tags.npm:public": ["libs/shared/types/package.json", "nx/core/package-json"], "metadata.targetGroups": ["libs/shared/types/package.json", "nx/core/package-json"], "metadata.js": ["libs/shared/types/package.json", "nx/core/package-json"], "metadata.js.packageName": ["libs/shared/types/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["libs/shared/types/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["libs/shared/types/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["libs/shared/types/package.json", "nx/core/package-json"], "targets": ["libs/shared/types/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["libs/shared/types/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["libs/shared/types/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["libs/shared/types/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["libs/shared/types/package.json", "nx/core/package-json"], "$schema": ["libs/shared/types/project.json", "nx/core/project-json"], "sourceRoot": ["libs/shared/types/project.json", "nx/core/project-json"], "projectType": ["libs/shared/types/project.json", "nx/core/project-json"], "tags.scope:shared": ["libs/shared/types/project.json", "nx/core/project-json"], "tags.type:types": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.build": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.build.executor": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.build.outputs": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.build.options": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.build.options.main": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.build.options.assets": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.lint": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.lint.executor": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.lint.outputs": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.lint.options": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.lint.options.lintFilePatterns": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.test": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.test.executor": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.test.outputs": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.test.options": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["libs/shared/types/project.json", "nx/core/project-json"], "targets.test.options.passWithNoTests": ["libs/shared/types/project.json", "nx/core/project-json"]}}