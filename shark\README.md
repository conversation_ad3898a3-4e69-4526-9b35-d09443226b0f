# 🏠 Shark - Home Services E-commerce Platform

> A comprehensive platform similar to Urban Company with role reversal capability, built with NX Monorepo, Next.js PWAs, and Go microservices.

## 📋 Project Overview

### Vision
Create a scalable home services marketplace where users can seamlessly switch between customer and vendor roles, offering both services (cleaning, repair, etc.) and products (iron, sand, etc.) in a unified platform.

### Key Features
- **Dual Role System**: Users can be both customers and vendors
- **Service Marketplace**: Book home services with real-time scheduling
- **Product Marketplace**: Purchase physical products with delivery
- **Advanced SEO**: Optimized for local search and discovery
- **PWA Support**: Mobile-first experience with offline capabilities
- **Real-time Communication**: In-app messaging and notifications

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 14+ with TypeScript, Tailwind CSS, PWA
- **Backend**: Go microservices with REST APIs
- **Database**: PostgreSQL with domain-specific schemas
- **Monorepo**: NX for scalable project management
- **Deployment**: Docker + Kubernetes
- **API Gateway**: Centralized routing and authentication

### Project Structure
```
shark/ (NX Monorepo Root)
├── apps/
│   ├── customer-app/          # Next.js PWA for customers
│   ├── vendor-app/            # Next.js PWA for vendors  
│   ├── admin-app/             # Next.js PWA for admin
│   └── landing-page/          # Marketing website
├── libs/
│   ├── shared/
│   │   ├── ui-components/     # Shared React components
│   │   ├── types/             # TypeScript definitions
│   │   ├── utils/             # Utility functions
│   │   └── constants/         # App constants
│   ├── customer/              # Customer-specific features
│   ├── vendor/                # Vendor-specific features
│   └── admin/                 # Admin-specific features
├── services/ (Go Microservices)
│   ├── user-service/          # User management & auth
│   ├── service-catalog/       # Services & categories
│   ├── booking-service/       # Booking management
│   ├── payment-service/       # Payment processing
│   ├── notification-service/  # Notifications
│   ├── review-service/        # Reviews & ratings
│   ├── product-service/       # Product catalog
│   ├── order-service/         # Order management
│   └── analytics-service/     # Analytics & reporting
├── database/
│   ├── migrations/            # Database migrations
│   ├── seeds/                 # Sample data
│   └── schemas/               # Database schemas
└── infrastructure/
    ├── docker/                # Docker configurations
    ├── k8s/                   # Kubernetes manifests
    └── terraform/             # Infrastructure as code
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Go 1.21+
- PostgreSQL 15+
- Docker & Docker Compose
- NX CLI

### Development Setup
```bash
# Install dependencies
npm install

# Setup database
docker-compose up -d postgres

# Run database migrations
npm run db:migrate

# Start development servers
npm run dev
```

## 📱 Applications

### Customer App Features
- **Service Discovery**: Browse, search, and filter services
- **Booking Flow**: Schedule services with preferred vendors
- **Product Shopping**: Browse and purchase products
- **Order Management**: Track bookings and orders
- **Role Switching**: Become a vendor seamlessly

### Vendor App Features
- **Service Management**: List and manage offered services
- **Booking Management**: Accept/decline bookings
- **Availability Calendar**: Manage working hours and slots
- **Analytics Dashboard**: Earnings and performance metrics
- **Product Selling**: List products for sale

### Admin App Features
- **User Management**: Manage customers and vendors
- **Service Administration**: Manage service categories
- **Analytics & Reports**: Platform-wide insights
- **Commission Management**: Set and track commission rates

## 🛠️ Development Commands

```bash
# Build all projects
npm run build

# Run tests
npm run test

# Lint code
npm run lint

# Serve applications
npm run serve

# Run all apps in development
npm run dev

# Database operations
npm run db:migrate
npm run db:seed
```

## 🎯 Core Domains

### 1. User Management Domain
- Multi-role authentication (Customer/Vendor/Admin)
- Profile management with seamless role switching
- KYC verification for vendors

### 2. Service Catalog Domain
- Service categories and subcategories
- Service provider listings and profiles
- Dynamic pricing models

### 3. Booking & Order Domain
- Service booking workflow
- Product ordering system
- Real-time tracking and updates

### 4. Payment Domain
- Multiple payment gateway integration
- Digital wallet system
- Commission and fee management

## 🔒 Security Features

- JWT-based authentication
- Role-based access control (RBAC)
- Data encryption at rest and in transit
- PCI DSS compliance for payments

## 📊 Key Metrics & KPIs

- Monthly Active Users (MAU)
- Gross Merchandise Value (GMV)
- Customer Acquisition Cost (CAC)
- Vendor Retention Rate

## 🤝 Contributing

Please read our contributing guidelines before submitting pull requests.

## 📄 License

This project is licensed under the MIT License.

---

**Built with ❤️ using NX Monorepo, Next.js, and Go**
