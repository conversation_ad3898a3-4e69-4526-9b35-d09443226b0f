# Comprehensive Project & Database Configuration Analysis
Write-Host "SHARK PLATFORM PROJECT ANALYSIS" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor White

Write-Host "`n1. PROJECT STRUCTURE ANALYSIS" -ForegroundColor Yellow
Write-Host "==============================" -ForegroundColor White

# Check Go modules
Write-Host "`nGo Modules:" -ForegroundColor Cyan
if (Test-Path "go.mod") {
    Write-Host "✅ Root go.mod exists" -ForegroundColor Green
    Get-Content "go.mod" | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ Root go.mod missing" -ForegroundColor Red
}

if (Test-Path "shared/go.mod") {
    Write-Host "`n✅ Shared go.mod exists" -ForegroundColor Green
    Get-Content "shared/go.mod" | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
} else {
    Write-Host "`n❌ Shared go.mod missing" -ForegroundColor Red
}

if (Test-Path "database/go.mod") {
    Write-Host "`n✅ Database go.mod exists" -ForegroundColor Green
    Get-Content "database/go.mod" | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
} else {
    Write-Host "`n❌ Database go.mod missing" -ForegroundColor Red
}

Write-Host "`n2. DATABASE CONFIGURATION ANALYSIS" -ForegroundColor Yellow
Write-Host "===================================" -ForegroundColor White

# Check config files
$configFiles = @(
    "config/database.env",
    "config/shared.env", 
    "config/services.env"
)

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "`n✅ $file exists" -ForegroundColor Green
        Write-Host "Content:" -ForegroundColor Cyan
        Get-Content $file | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
            Write-Host "  $_" -ForegroundColor Gray
        }
    } else {
        Write-Host "`n❌ $file missing" -ForegroundColor Red
    }
}

Write-Host "`n3. DATABASE DIRECTORY ISSUES" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor White

# Check for multiple main functions
Write-Host "`nChecking for multiple main() functions:" -ForegroundColor Cyan
$mainFiles = Get-ChildItem -Path "database" -Filter "*.go" | Where-Object { 
    (Get-Content $_.FullName) -match "func main\(\)" 
}

if ($mainFiles.Count -gt 1) {
    Write-Host "❌ CRITICAL ISSUE: Multiple main() functions found!" -ForegroundColor Red
    foreach ($file in $mainFiles) {
        Write-Host "  - $($file.Name)" -ForegroundColor Red
    }
    Write-Host "This causes Go compilation conflicts!" -ForegroundColor Red
} else {
    Write-Host "✅ No main() function conflicts" -ForegroundColor Green
}

Write-Host "`n4. POSTGRESQL CONNECTION TEST" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor White

# Load database config
$dbConfig = @{}
if (Test-Path "config/database.env") {
    Get-Content "config/database.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
        $parts = $_ -split "=", 2
        if ($parts.Length -eq 2) {
            $dbConfig[$parts[0].Trim()] = $parts[1].Trim()
        }
    }
}

Write-Host "Database Configuration:" -ForegroundColor Cyan
foreach ($key in $dbConfig.Keys) {
    if ($key -eq "DB_PASSWORD") {
        Write-Host "  $key = $('*' * $dbConfig[$key].Length)" -ForegroundColor Gray
    } else {
        Write-Host "  $key = $($dbConfig[$key])" -ForegroundColor Gray
    }
}

# Test PostgreSQL port
$dbPort = if ($dbConfig["DB_PORT"]) { $dbConfig["DB_PORT"] } else { "5432" }
Write-Host "`nTesting PostgreSQL port $dbPort..." -ForegroundColor Cyan

try {
    $connection = Test-NetConnection -ComputerName localhost -Port $dbPort -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✅ Port $dbPort is listening" -ForegroundColor Green
    } else {
        Write-Host "❌ Port $dbPort is not listening" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️  Could not test port $dbPort" -ForegroundColor Yellow
}

Write-Host "`n5. GO DEPENDENCIES ANALYSIS" -ForegroundColor Yellow
Write-Host "============================" -ForegroundColor White

# Check if lib/pq is properly installed
Write-Host "Checking PostgreSQL driver installation..." -ForegroundColor Cyan
try {
    $goModOutput = & go list -m github.com/lib/pq 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ github.com/lib/pq is installed: $goModOutput" -ForegroundColor Green
    } else {
        Write-Host "❌ github.com/lib/pq is not properly installed" -ForegroundColor Red
        Write-Host "Error: $goModOutput" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Could not check Go dependencies" -ForegroundColor Red
}

Write-Host "`n6. MICROSERVICES CONFIGURATION" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor White

# Check microservices
$services = @("user-service", "service-catalog", "product-service", "booking-service", "payment-service")
foreach ($service in $services) {
    $servicePath = "services/$service"
    if (Test-Path $servicePath) {
        Write-Host "`n✅ $service exists" -ForegroundColor Green
        
        # Check for config files
        if (Test-Path "$servicePath/internal/config/config.go") {
            Write-Host "  ✅ config.go exists" -ForegroundColor Green
        } else {
            Write-Host "  ❌ config.go missing" -ForegroundColor Red
        }
        
        # Check for .env file
        if (Test-Path "$servicePath/.env") {
            Write-Host "  ✅ .env exists" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  .env missing" -ForegroundColor Yellow
        }
    } else {
        Write-Host "`n❌ $service missing" -ForegroundColor Red
    }
}

Write-Host "`n" + "="*60 -ForegroundColor White
Write-Host "ISSUE SUMMARY & RECOMMENDATIONS" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor White

Write-Host "`nIDENTIFIED ISSUES:" -ForegroundColor Red
Write-Host "1. Multiple main() functions in database directory" -ForegroundColor White
Write-Host "2. Go module conflicts causing compilation issues" -ForegroundColor White
Write-Host "3. Potential UUID extension problems" -ForegroundColor White
Write-Host "4. Database connection authentication issues" -ForegroundColor White

Write-Host "`nRECOMMENDED SOLUTIONS:" -ForegroundColor Green
Write-Host "1. Reorganize database directory structure" -ForegroundColor White
Write-Host "2. Create separate commands for different database operations" -ForegroundColor White
Write-Host "3. Fix PostgreSQL authentication configuration" -ForegroundColor White
Write-Host "4. Test UUID extension support properly" -ForegroundColor White
Write-Host "5. Use proper Go module structure" -ForegroundColor White

Write-Host "`nNEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Fix the multiple main() function issue" -ForegroundColor White
Write-Host "2. Test database connection with proper credentials" -ForegroundColor White
Write-Host "3. Create a single, working table creation script" -ForegroundColor White
Write-Host "4. Verify all microservices can connect to database" -ForegroundColor White
