# Simple Database Setup Verification
Write-Host "DATABASE SETUP VERIFICATION" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor White

Write-Host "`nChecking .env files for database configuration..." -ForegroundColor Yellow

# Check User Service
Write-Host "`n1. USER SERVICE (.env):" -ForegroundColor Green
if (Test-Path "services/user-service/.env") {
    $userEnv = Get-Content "services/user-service/.env"
    $userEnv | Where-Object { $_ -match "^(DB_|PORT=)" } | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

# Check Service Catalog
Write-Host "`n2. SERVICE CATALOG (.env):" -ForegroundColor Green
if (Test-Path "services/service-catalog/.env") {
    $catalogEnv = Get-Content "services/service-catalog/.env"
    $catalogEnv | Where-Object { $_ -match "^(DB_|PORT=)" } | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

# Check Product Service
Write-Host "`n3. PRODUCT SERVICE (.env):" -ForegroundColor Green
if (Test-Path "services/product-service/.env") {
    $productEnv = Get-Content "services/product-service/.env"
    $productEnv | Where-Object { $_ -match "^(DB_|PORT=)" } | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`nCONCLUSION:" -ForegroundColor Cyan
Write-Host "- All services use the SAME database: shark_db" -ForegroundColor White
Write-Host "- Each service has its own PORT number" -ForegroundColor White
Write-Host "- Separate .env files allow different service configurations" -ForegroundColor White
Write-Host "- You have ONE PostgreSQL database, not multiple databases" -ForegroundColor Yellow
