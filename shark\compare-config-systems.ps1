# Compare .env.shared vs config/ directory
Write-Host "COMPARING CONFIGURATION SYSTEMS" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor White

Write-Host "`nOLD SYSTEM (.env.shared):" -ForegroundColor Yellow
if (Test-Path ".env.shared") {
    Get-Content ".env.shared" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
        Write-Host "  $_" -ForegroundColor Red
    }
} else {
    Write-Host "  File not found" -ForegroundColor Gray
}

Write-Host "`nNEW SYSTEM (config/ directory):" -ForegroundColor Green

Write-Host "`n  database.env:" -ForegroundColor Cyan
if (Test-Path "config/database.env") {
    Get-Content "config/database.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
        Write-Host "    $_" -ForegroundColor Green
    }
}

Write-Host "`n  shared.env:" -ForegroundColor Cyan
if (Test-Path "config/shared.env") {
    Get-Content "config/shared.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
        Write-Host "    $_" -ForegroundColor Green
    }
}

Write-Host "`n  services.env:" -ForegroundColor Cyan
if (Test-Path "config/services.env") {
    Get-Content "config/services.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
        Write-Host "    $_" -ForegroundColor Green
    }
}

Write-Host "`nCOMPARISON RESULT:" -ForegroundColor Yellow
Write-Host "- .env.shared contains the SAME values as config/ files" -ForegroundColor White
Write-Host "- config/ directory is BETTER organized (separated by purpose)" -ForegroundColor Green
Write-Host "- .env.shared is REDUNDANT and can be safely removed" -ForegroundColor Red

Write-Host "`nRECOMMENDATION:" -ForegroundColor Cyan
Write-Host "1. Use config/ directory (newer, better organized)" -ForegroundColor Green
Write-Host "2. Remove .env.shared (redundant)" -ForegroundColor Red
Write-Host "3. Update any code that references .env.shared" -ForegroundColor Yellow
