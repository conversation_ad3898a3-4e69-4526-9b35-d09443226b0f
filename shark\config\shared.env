# Shared Configuration
# Configuration shared across ALL microservices

# Environment
ENVIRONMENT=development

# JWT Configuration
JWT_SECRET=shark-super-secret-jwt-key-for-development-only
JWT_EXPIRY=24h

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# External Services
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587

# API Configuration
API_TIMEOUT=30s
API_RATE_LIMIT=1000

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Security
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
BCRYPT_COST=12

# API Gateway (future)
API_GATEWAY_URL=http://localhost:8000
