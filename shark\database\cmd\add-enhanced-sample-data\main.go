package main

import (
	"database/sql"
	"fmt"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🌟 ADDING ENHANCED SAMPLE DATA TO SHARK PLATFORM")
	fmt.Println("===============================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Connecting to: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		fmt.Printf("❌ Failed to connect to database: %v\n", err)
		return
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		fmt.Printf("❌ Failed to ping database: %v\n", err)
		return
	}

	fmt.Println("✅ Database connection successful!")

	// Add enhanced sample data step by step
	fmt.Println("\n🌟 Adding enhanced sample data...")
	
	err = addEnhancedSampleData(db)
	if err != nil {
		fmt.Printf("❌ Failed to add enhanced sample data: %v\n", err)
		return
	}

	// Verify enhanced data
	fmt.Println("\n✅ Verifying enhanced sample data...")
	err = verifyEnhancedData(db)
	if err != nil {
		fmt.Printf("❌ Failed to verify enhanced data: %v\n", err)
		return
	}

	fmt.Println("\n🎉 ENHANCED SAMPLE DATA ADDED SUCCESSFULLY!")
	fmt.Println("==========================================")
	fmt.Println("✅ Your Shark platform now demonstrates:")
	fmt.Println("   • Multi-vendor marketplace")
	fmt.Println("   • Subcategory hierarchies")
	fmt.Println("   • Shopping cart with items")
	fmt.Println("   • Coupon and discount system")
	fmt.Println("   • Dynamic tax rules")
	fmt.Println("   • Schedule time slots")
	fmt.Println("   • Inventory management")
	fmt.Println("   • Wishlist functionality")
	fmt.Println("   • Address management")
	fmt.Println("   • Shipping options")
}

func addEnhancedSampleData(db *sql.DB) error {
	// Get existing user IDs
	userIDs := make(map[string]string)
	rows, err := db.Query("SELECT id, email FROM users")
	if err != nil {
		return fmt.Errorf("failed to get user IDs: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var id, email string
		if err := rows.Scan(&id, &email); err != nil {
			continue
		}
		userIDs[email] = id
	}

	// Get existing category IDs
	serviceCategoryIDs := make(map[string]string)
	rows2, err := db.Query("SELECT id, name FROM service_categories")
	if err != nil {
		return fmt.Errorf("failed to get service category IDs: %w", err)
	}
	defer rows2.Close()

	for rows2.Next() {
		var id, name string
		if err := rows2.Scan(&id, &name); err != nil {
			continue
		}
		serviceCategoryIDs[name] = id
	}

	productCategoryIDs := make(map[string]string)
	rows3, err := db.Query("SELECT id, name FROM product_categories")
	if err != nil {
		return fmt.Errorf("failed to get product category IDs: %w", err)
	}
	defer rows3.Close()

	for rows3.Next() {
		var id, name string
		if err := rows3.Scan(&id, &name); err != nil {
			continue
		}
		productCategoryIDs[name] = id
	}

	// Step 1: Add subcategories
	fmt.Println("  📝 Step 1: Adding subcategories...")
	err = addSubcategories(db, serviceCategoryIDs, productCategoryIDs)
	if err != nil {
		return fmt.Errorf("failed to add subcategories: %w", err)
	}

	// Step 2: Add more products for multi-vendor demo
	fmt.Println("  📝 Step 2: Adding more products...")
	productIDs, err := addMoreProducts(db, userIDs, productCategoryIDs)
	if err != nil {
		return fmt.Errorf("failed to add more products: %w", err)
	}

	// Step 3: Add vendor-product relationships
	fmt.Println("  📝 Step 3: Adding multi-vendor relationships...")
	err = addVendorRelationships(db, userIDs, productIDs)
	if err != nil {
		return fmt.Errorf("failed to add vendor relationships: %w", err)
	}

	// Step 4: Add time slots and schedules
	fmt.Println("  📝 Step 4: Adding time slots and schedules...")
	err = addTimeSlots(db, userIDs)
	if err != nil {
		return fmt.Errorf("failed to add time slots: %w", err)
	}

	// Step 5: Add coupons
	fmt.Println("  📝 Step 5: Adding coupons...")
	err = addCoupons(db, userIDs)
	if err != nil {
		return fmt.Errorf("failed to add coupons: %w", err)
	}

	// Step 6: Add tax rules and fees
	fmt.Println("  📝 Step 6: Adding tax rules and fees...")
	err = addTaxRulesAndFees(db)
	if err != nil {
		return fmt.Errorf("failed to add tax rules and fees: %w", err)
	}

	// Step 7: Add shopping cart items
	fmt.Println("  📝 Step 7: Adding shopping cart items...")
	err = addCartItems(db, userIDs, productIDs)
	if err != nil {
		return fmt.Errorf("failed to add cart items: %w", err)
	}

	// Step 8: Add addresses and shipping
	fmt.Println("  📝 Step 8: Adding addresses and shipping...")
	err = addAddressesAndShipping(db, userIDs)
	if err != nil {
		return fmt.Errorf("failed to add addresses and shipping: %w", err)
	}

	// Step 9: Add wishlist items
	fmt.Println("  📝 Step 9: Adding wishlist items...")
	err = addWishlistItems(db, userIDs, productIDs)
	if err != nil {
		return fmt.Errorf("failed to add wishlist items: %w", err)
	}

	fmt.Println("✅ All enhanced sample data added successfully!")
	return nil
}

func addSubcategories(db *sql.DB, serviceCategoryIDs, productCategoryIDs map[string]string) error {
	// Add service subcategories
	subcategories := []map[string]interface{}{
		{
			"parent": "Plumbing",
			"name": "Emergency Plumbing",
			"description": "24/7 emergency plumbing services",
		},
		{
			"parent": "Plumbing", 
			"name": "Pipe Installation",
			"description": "New pipe installation and replacement",
		},
		{
			"parent": "Electrical",
			"name": "Wiring",
			"description": "Electrical wiring and rewiring services",
		},
		{
			"parent": "Electrical",
			"name": "Smart Home",
			"description": "Smart home automation installation",
		},
	}

	for _, subcat := range subcategories {
		parentID := serviceCategoryIDs[subcat["parent"].(string)]
		if parentID == "" {
			continue
		}

		var subcatID string
		err := db.QueryRow(`
			INSERT INTO service_categories (id, parent_id, name, description, slug, level, path, sort_order) 
			VALUES (uuid_generate_v4(), $1, $2, $3, $4, 1, $5, 0)
			RETURNING id`,
			parentID, 
			subcat["name"], 
			subcat["description"],
			strings.ToLower(strings.ReplaceAll(subcat["name"].(string), " ", "-")),
			"/"+parentID+"/").Scan(&subcatID)
		
		if err != nil {
			return fmt.Errorf("failed to insert service subcategory %s: %w", subcat["name"], err)
		}
		fmt.Printf("    ✅ Added service subcategory: %s\n", subcat["name"])
	}

	// Add product subcategories
	productSubcategories := []map[string]interface{}{
		{
			"parent": "Tools",
			"name": "Power Tools",
			"description": "Electric and battery-powered tools",
		},
		{
			"parent": "Tools",
			"name": "Hand Tools", 
			"description": "Manual tools and equipment",
		},
		{
			"parent": "Hardware",
			"name": "Fasteners",
			"description": "Screws, bolts, nuts, and washers",
		},
		{
			"parent": "Hardware",
			"name": "Hinges & Latches",
			"description": "Door and cabinet hardware",
		},
	}

	for _, subcat := range productSubcategories {
		parentID := productCategoryIDs[subcat["parent"].(string)]
		if parentID == "" {
			continue
		}

		var subcatID string
		err := db.QueryRow(`
			INSERT INTO product_categories (id, parent_id, name, description, slug, level, path, sort_order) 
			VALUES (uuid_generate_v4(), $1, $2, $3, $4, 1, $5, 0)
			RETURNING id`,
			parentID,
			subcat["name"],
			subcat["description"], 
			strings.ToLower(strings.ReplaceAll(subcat["name"].(string), " ", "-")),
			"/"+parentID+"/").Scan(&subcatID)
		
		if err != nil {
			return fmt.Errorf("failed to insert product subcategory %s: %w", subcat["name"], err)
		}
		fmt.Printf("    ✅ Added product subcategory: %s\n", subcat["name"])
	}

	return nil
}

func addMoreProducts(db *sql.DB, userIDs, productCategoryIDs map[string]string) (map[string]string, error) {
	productIDs := make(map[string]string)
	
	products := []map[string]interface{}{
		{
			"vendor": "<EMAIL>",
			"category": "Tools",
			"name": "Cordless Impact Driver",
			"description": "High-torque cordless impact driver with LED light",
			"price": 129.99,
			"sku": "IMPACT-001",
			"stock": 15,
		},
		{
			"vendor": "<EMAIL>",
			"category": "Electrical Supplies",
			"name": "Smart WiFi Switch",
			"description": "WiFi-enabled smart light switch with app control",
			"price": 24.99,
			"sku": "SMART-SW-001",
			"stock": 50,
		},
		{
			"vendor": "<EMAIL>",
			"category": "Plumbing Supplies",
			"name": "PVC Pipe Set",
			"description": "Complete PVC pipe and fitting set for home projects",
			"price": 89.99,
			"sku": "PVC-SET-001",
			"stock": 25,
		},
	}

	for _, product := range products {
		vendorID := userIDs[product["vendor"].(string)]
		categoryID := productCategoryIDs[product["category"].(string)]
		
		if vendorID == "" || categoryID == "" {
			continue
		}

		var productID string
		err := db.QueryRow(`
			INSERT INTO products (id, vendor_id, category_id, name, description, price, sku, stock_quantity) 
			VALUES (uuid_generate_v4(), $1, $2, $3, $4, $5, $6, $7)
			RETURNING id`,
			vendorID, categoryID, product["name"], product["description"], 
			product["price"], product["sku"], product["stock"]).Scan(&productID)
		
		if err != nil {
			return nil, fmt.Errorf("failed to insert product %s: %w", product["name"], err)
		}
		
		productIDs[product["name"].(string)] = productID
		fmt.Printf("    ✅ Added product: %s\n", product["name"])
	}

	return productIDs, nil
}

func addVendorRelationships(db *sql.DB, userIDs, productIDs map[string]string) error {
	// Add vendor-product relationships (multiple vendors for same product)
	relationships := []map[string]interface{}{
		{
			"vendor": "<EMAIL>",
			"product": "Cordless Impact Driver", // Sarah also sells Mike's product
			"price": 134.99, // Different price
			"sku": "SARAH-IMPACT-001",
			"stock": 8,
		},
	}

	for _, rel := range relationships {
		vendorID := userIDs[rel["vendor"].(string)]
		productID := productIDs[rel["product"].(string)]
		
		if vendorID == "" || productID == "" {
			continue
		}

		_, err := db.Exec(`
			INSERT INTO vendor_products (vendor_id, product_id, vendor_sku, vendor_price, stock_quantity) 
			VALUES ($1, $2, $3, $4, $5)`,
			vendorID, productID, rel["sku"], rel["price"], rel["stock"])
		
		if err != nil {
			return fmt.Errorf("failed to insert vendor-product relationship: %w", err)
		}
		
		fmt.Printf("    ✅ Added vendor relationship: %s sells %s\n", rel["vendor"], rel["product"])
	}

	return nil
}

func addTimeSlots(db *sql.DB, userIDs map[string]string) error {
	// Add time slots
	timeSlots := []map[string]interface{}{
		{"name": "Morning", "start": "08:00", "end": "12:00", "duration": 240},
		{"name": "Afternoon", "start": "13:00", "end": "17:00", "duration": 240},
		{"name": "Evening", "start": "18:00", "end": "20:00", "duration": 120},
	}

	slotIDs := make(map[string]string)
	for _, slot := range timeSlots {
		var slotID string
		err := db.QueryRow(`
			INSERT INTO time_slots (id, name, start_time, end_time, duration) 
			VALUES (uuid_generate_v4(), $1, $2, $3, $4)
			RETURNING id`,
			slot["name"], slot["start"], slot["end"], slot["duration"]).Scan(&slotID)
		
		if err != nil {
			return fmt.Errorf("failed to insert time slot %s: %w", slot["name"], err)
		}
		
		slotIDs[slot["name"].(string)] = slotID
		fmt.Printf("    ✅ Added time slot: %s\n", slot["name"])
	}

	// Add vendor schedules
	vendors := []string{"<EMAIL>", "<EMAIL>"}
	for _, vendorEmail := range vendors {
		vendorID := userIDs[vendorEmail]
		if vendorID == "" {
			continue
		}

		// Add schedule for weekdays (Monday-Friday)
		for day := 1; day <= 5; day++ {
			for slotName, slotID := range slotIDs {
				if slotName == "Evening" && day == 5 { // No evening slots on Friday
					continue
				}

				_, err := db.Exec(`
					INSERT INTO vendor_schedules (vendor_id, day_of_week, time_slot_id, is_available, max_bookings) 
					VALUES ($1, $2, $3, true, 2)`,
					vendorID, day, slotID)
				
				if err != nil {
					return fmt.Errorf("failed to insert vendor schedule: %w", err)
				}
			}
		}
		fmt.Printf("    ✅ Added schedule for vendor: %s\n", vendorEmail)
	}

	return nil
}

func addCoupons(db *sql.DB, userIDs map[string]string) error {
	coupons := []map[string]interface{}{
		{
			"code": "WELCOME10",
			"name": "Welcome Discount",
			"description": "10% off for new customers",
			"type": "percentage",
			"value": 10.00,
			"min_amount": 50.00,
			"max_discount": 25.00,
			"usage_limit": 100,
		},
		{
			"code": "FREESHIP",
			"name": "Free Shipping",
			"description": "Free shipping on orders over $75",
			"type": "free_shipping",
			"value": 0.00,
			"min_amount": 75.00,
			"usage_limit": nil,
		},
		{
			"code": "SAVE20",
			"name": "Save $20",
			"description": "$20 off orders over $100",
			"type": "fixed_amount",
			"value": 20.00,
			"min_amount": 100.00,
			"usage_limit": 50,
		},
	}

	for _, coupon := range coupons {
		_, err := db.Exec(`
			INSERT INTO coupons (code, name, description, discount_type, discount_value, minimum_order_amount, maximum_discount_amount, usage_limit, created_by) 
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
			coupon["code"], coupon["name"], coupon["description"], coupon["type"], 
			coupon["value"], coupon["min_amount"], coupon["max_discount"], coupon["usage_limit"],
			userIDs["<EMAIL>"])
		
		if err != nil {
			return fmt.Errorf("failed to insert coupon %s: %w", coupon["code"], err)
		}
		
		fmt.Printf("    ✅ Added coupon: %s\n", coupon["code"])
	}

	return nil
}

func addTaxRulesAndFees(db *sql.DB) error {
	// Add tax rules
	taxRules := []map[string]interface{}{
		{
			"name": "California Sales Tax",
			"type": "sales_tax",
			"rate": 0.0825, // 8.25%
			"country": "US",
			"state": "CA",
		},
		{
			"name": "New York Sales Tax",
			"type": "sales_tax", 
			"rate": 0.08, // 8%
			"country": "US",
			"state": "NY",
		},
	}

	for _, tax := range taxRules {
		_, err := db.Exec(`
			INSERT INTO tax_rules (name, tax_type, rate, country, state) 
			VALUES ($1, $2, $3, $4, $5)`,
			tax["name"], tax["type"], tax["rate"], tax["country"], tax["state"])
		
		if err != nil {
			return fmt.Errorf("failed to insert tax rule %s: %w", tax["name"], err)
		}
		
		fmt.Printf("    ✅ Added tax rule: %s\n", tax["name"])
	}

	// Add fees
	fees := []map[string]interface{}{
		{
			"name": "Processing Fee",
			"type": "processing",
			"calc_type": "percentage",
			"amount": 2.50, // 2.5%
			"min_amount": 1.00,
			"max_amount": 10.00,
		},
		{
			"name": "Service Fee",
			"type": "service",
			"calc_type": "fixed",
			"amount": 5.00,
			"applicable_to": "services",
		},
	}

	for _, fee := range fees {
		_, err := db.Exec(`
			INSERT INTO fees (name, fee_type, calculation_type, amount, minimum_amount, maximum_amount, applicable_to) 
			VALUES ($1, $2, $3, $4, $5, $6, $7)`,
			fee["name"], fee["type"], fee["calc_type"], fee["amount"], 
			fee["min_amount"], fee["max_amount"], fee["applicable_to"])
		
		if err != nil {
			return fmt.Errorf("failed to insert fee %s: %w", fee["name"], err)
		}
		
		fmt.Printf("    ✅ Added fee: %s\n", fee["name"])
	}

	return nil
}

func addCartItems(db *sql.DB, userIDs, productIDs map[string]string) error {
	customerID := userIDs["<EMAIL>"]
	if customerID == "" {
		return fmt.Errorf("customer not found")
	}

	// Create cart for customer
	var cartID string
	err := db.QueryRow(`
		INSERT INTO cart (user_id) VALUES ($1) RETURNING id`,
		customerID).Scan(&cartID)
	
	if err != nil {
		return fmt.Errorf("failed to create cart: %w", err)
	}

	// Add items to cart
	for productName, productID := range productIDs {
		if productName == "Cordless Impact Driver" {
			_, err := db.Exec(`
				INSERT INTO cart_items (cart_id, product_id, vendor_id, quantity, unit_price, total_price) 
				VALUES ($1, $2, $3, 1, 129.99, 129.99)`,
				cartID, productID, userIDs["<EMAIL>"])
			
			if err != nil {
				return fmt.Errorf("failed to add cart item: %w", err)
			}
			
			fmt.Printf("    ✅ Added to cart: %s\n", productName)
			break
		}
	}

	return nil
}

func addAddressesAndShipping(db *sql.DB, userIDs map[string]string) error {
	// Add addresses for customers
	customers := []string{"<EMAIL>", "<EMAIL>"}
	
	for _, customerEmail := range customers {
		customerID := userIDs[customerEmail]
		if customerID == "" {
			continue
		}

		// Add shipping address
		_, err := db.Exec(`
			INSERT INTO user_addresses (user_id, type, is_default, first_name, last_name, address_line1, city, state, postal_code, country) 
			VALUES ($1, 'shipping', true, $2, $3, $4, 'San Francisco', 'CA', '94102', 'US')`,
			customerID, "John", "Doe", "123 Main St")
		
		if err != nil {
			return fmt.Errorf("failed to add address: %w", err)
		}
		
		fmt.Printf("    ✅ Added address for: %s\n", customerEmail)
	}

	// Add shipping methods
	shippingMethods := []map[string]interface{}{
		{
			"name": "Standard Shipping",
			"carrier": "USPS",
			"type": "Ground",
			"calc_type": "fixed",
			"cost": 5.99,
			"days_min": 3,
			"days_max": 7,
		},
		{
			"name": "Express Shipping",
			"carrier": "FedEx",
			"type": "Express",
			"calc_type": "fixed",
			"cost": 12.99,
			"days_min": 1,
			"days_max": 2,
		},
	}

	for _, method := range shippingMethods {
		_, err := db.Exec(`
			INSERT INTO shipping_methods (name, carrier, service_type, calculation_type, base_cost, estimated_days_min, estimated_days_max) 
			VALUES ($1, $2, $3, $4, $5, $6, $7)`,
			method["name"], method["carrier"], method["type"], method["calc_type"],
			method["cost"], method["days_min"], method["days_max"])
		
		if err != nil {
			return fmt.Errorf("failed to add shipping method: %w", err)
		}
		
		fmt.Printf("    ✅ Added shipping method: %s\n", method["name"])
	}

	return nil
}

func addWishlistItems(db *sql.DB, userIDs, productIDs map[string]string) error {
	customerID := userIDs["<EMAIL>"]
	if customerID == "" {
		return fmt.Errorf("customer not found")
	}

	// Create wishlist
	var wishlistID string
	err := db.QueryRow(`
		INSERT INTO wishlist (user_id, name) VALUES ($1, 'My Wishlist') RETURNING id`,
		customerID).Scan(&wishlistID)
	
	if err != nil {
		return fmt.Errorf("failed to create wishlist: %w", err)
	}

	// Add items to wishlist
	for productName, productID := range productIDs {
		if productName == "Smart WiFi Switch" {
			_, err := db.Exec(`
				INSERT INTO wishlist_items (wishlist_id, product_id, vendor_id, notes) 
				VALUES ($1, $2, $3, 'Want this for home automation project')`,
				wishlistID, productID, userIDs["<EMAIL>"])
			
			if err != nil {
				return fmt.Errorf("failed to add wishlist item: %w", err)
			}
			
			fmt.Printf("    ✅ Added to wishlist: %s\n", productName)
			break
		}
	}

	return nil
}

func verifyEnhancedData(db *sql.DB) error {
	// Check enhanced data in key tables
	tables := map[string]string{
		"subcategories (service)": "SELECT COUNT(*) FROM service_categories WHERE parent_id IS NOT NULL",
		"subcategories (product)": "SELECT COUNT(*) FROM product_categories WHERE parent_id IS NOT NULL", 
		"vendor_products": "SELECT COUNT(*) FROM vendor_products",
		"time_slots": "SELECT COUNT(*) FROM time_slots",
		"vendor_schedules": "SELECT COUNT(*) FROM vendor_schedules",
		"coupons": "SELECT COUNT(*) FROM coupons",
		"tax_rules": "SELECT COUNT(*) FROM tax_rules",
		"fees": "SELECT COUNT(*) FROM fees",
		"cart_items": "SELECT COUNT(*) FROM cart_items",
		"user_addresses": "SELECT COUNT(*) FROM user_addresses",
		"shipping_methods": "SELECT COUNT(*) FROM shipping_methods",
		"wishlist_items": "SELECT COUNT(*) FROM wishlist_items",
	}

	fmt.Println("Enhanced data verification:")
	totalRecords := 0
	
	for tableName, query := range tables {
		var count int
		err := db.QueryRow(query).Scan(&count)
		if err != nil {
			fmt.Printf("  ❌ %s: Error - %v\n", tableName, err)
			continue
		}
		fmt.Printf("  ✅ %s: %d records\n", tableName, count)
		totalRecords += count
	}

	if totalRecords == 0 {
		return fmt.Errorf("no enhanced sample data was inserted")
	}

	fmt.Printf("✅ Total enhanced records: %d\n", totalRecords)
	return nil
}

func loadDatabaseConfig() {
	configFile := "../../../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := os.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
