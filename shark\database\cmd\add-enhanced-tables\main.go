package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🚀 ADDING ENHANCED TABLES TO SHARK PLATFORM")
	fmt.Println("===========================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Connecting to: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("❌ Failed to ping database:", err)
	}

	fmt.Println("✅ Database connection successful!")

	// Add enhanced tables
	fmt.Println("\n🔧 Adding enhanced tables...")
	err = addEnhancedTables(db)
	if err != nil {
		log.Fatal("❌ Failed to add enhanced tables:", err)
	}

	// Verify new tables
	fmt.Println("\n✅ Verifying enhanced tables...")
	err = verifyEnhancedTables(db)
	if err != nil {
		log.Fatal("❌ Failed to verify enhanced tables:", err)
	}

	fmt.Println("\n🎉 ENHANCED TABLES ADDED SUCCESSFULLY!")
	fmt.Println("=====================================")
	fmt.Println("✅ Your Shark platform now has:")
	fmt.Println("   • Unlimited subcategories")
	fmt.Println("   • Shopping cart system")
	fmt.Println("   • Wishlist functionality")
	fmt.Println("   • Coupon and discount system")
	fmt.Println("   • Dynamic tax rules")
	fmt.Println("   • Additional fees management")
	fmt.Println("   • Schedule slots system")
	fmt.Println("   • Multi-vendor product/service support")
	fmt.Println("   • Inventory management")
	fmt.Println("   • Address management")
	fmt.Println("   • Shipping system")
}

func addEnhancedTables(db *sql.DB) error {
	// Read enhanced schema SQL file
	sqlFile := "../../enhanced-schema.sql"
	content, err := ioutil.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("failed to read enhanced schema file %s: %w", sqlFile, err)
	}

	sqlContent := string(content)

	// Execute as a single transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Execute the entire SQL content
	_, err = tx.Exec(sqlContent)
	if err != nil {
		return fmt.Errorf("failed to execute enhanced schema SQL: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	fmt.Println("✅ All enhanced tables created successfully!")
	return nil
}

func verifyEnhancedTables(db *sql.DB) error {
	// List of new tables that should be created
	expectedTables := []string{
		// Enhanced existing tables (recreated)
		"service_categories", "product_categories",

		// New vendor relationship tables
		"vendor_products", "vendor_services",

		// Cart system
		"cart", "cart_items", "cart_item_addons", "cart_services", "cart_service_addons",

		// Wishlist
		"wishlist", "wishlist_items",

		// Schedule system
		"time_slots", "vendor_schedules", "vendor_schedule_exceptions",

		// Coupon system
		"coupons", "coupon_usage", "coupon_categories",

		// Tax and fee system
		"tax_rules", "fees", "fee_tiers",

		// Address management
		"user_addresses",

		// Shipping system
		"shipping_methods", "shipping_zones", "shipping_rates",

		// Inventory management
		"inventory_transactions", "low_stock_alerts",
	}

	query := `
		SELECT table_name
		FROM information_schema.tables
		WHERE table_schema = 'public'
		ORDER BY table_name`

	rows, err := db.Query(query)
	if err != nil {
		return fmt.Errorf("failed to query enhanced tables: %w", err)
	}
	defer rows.Close()

	var allTables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}
		allTables = append(allTables, tableName)
	}

	fmt.Printf("📊 Enhanced tables verification:\n")
	fmt.Printf("Expected: %d enhanced tables\n", len(expectedTables))
	fmt.Printf("Total tables found: %d\n", len(allTables))

	// Check which enhanced tables exist
	missingTables := []string{}
	foundCount := 0
	for _, expected := range expectedTables {
		found := false
		for _, actual := range allTables {
			if actual == expected {
				found = true
				foundCount++
				break
			}
		}
		if found {
			fmt.Printf("  ✅ %s\n", expected)
		} else {
			fmt.Printf("  ❌ %s (missing)\n", expected)
			missingTables = append(missingTables, expected)
		}
	}

	fmt.Printf("\nEnhanced tables found: %d/%d\n", foundCount, len(expectedTables))

	if len(missingTables) > 0 {
		return fmt.Errorf("%d enhanced tables are missing: %v", len(missingTables), missingTables)
	}

	// Get total table count
	var totalTables int
	err = db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'").Scan(&totalTables)
	if err != nil {
		return fmt.Errorf("failed to count total tables: %w", err)
	}

	fmt.Printf("\n✅ All enhanced tables verified successfully!\n")
	fmt.Printf("📊 Total tables in database: %d\n", totalTables)

	return nil
}

func loadDatabaseConfig() {
	configFile := "../../../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
