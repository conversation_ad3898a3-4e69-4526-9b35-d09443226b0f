package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🌱 ADDING SAMPLE DATA TO SHARK PLATFORM")
	fmt.Println("=======================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Connecting to: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("❌ Failed to ping database:", err)
	}

	fmt.Println("✅ Database connection successful!")

	// Add sample data step by step
	fmt.Println("\n🌱 Adding sample data step by step...")
	
	err = addSampleDataStepByStep(db)
	if err != nil {
		log.Fatal("❌ Failed to add sample data:", err)
	}

	// Verify data
	fmt.Println("\n✅ Verifying sample data...")
	err = verifySampleData(db)
	if err != nil {
		log.Fatal("❌ Failed to verify sample data:", err)
	}

	fmt.Println("\n🎉 SAMPLE DATA ADDED SUCCESSFULLY!")
	fmt.Println("=================================")
	fmt.Println("✅ Your Shark platform now has test data!")
	fmt.Println("✅ You can start testing your microservices!")
}

func addSampleDataStepByStep(db *sql.DB) error {
	// Step 1: Add Users
	fmt.Println("  📝 Step 1: Adding users...")
	userIDs := make(map[string]string)
	
	users := []map[string]string{
		{"email": "<EMAIL>", "first_name": "Admin", "last_name": "User", "phone": "+1234567890"},
		{"email": "<EMAIL>", "first_name": "John", "last_name": "Doe", "phone": "+1234567891"},
		{"email": "<EMAIL>", "first_name": "Jane", "last_name": "Smith", "phone": "+1234567892"},
		{"email": "<EMAIL>", "first_name": "Mike", "last_name": "Johnson", "phone": "+1234567893"},
		{"email": "<EMAIL>", "first_name": "Sarah", "last_name": "Wilson", "phone": "+1234567894"},
		{"email": "<EMAIL>", "first_name": "Alex", "last_name": "Brown", "phone": "+1234567895"},
	}

	for _, user := range users {
		var userID string
		err := db.QueryRow(`
			INSERT INTO users (id, email, password_hash, first_name, last_name, phone, is_active, is_verified) 
			VALUES (uuid_generate_v4(), $1, '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', $2, $3, $4, true, true)
			RETURNING id`,
			user["email"], user["first_name"], user["last_name"], user["phone"]).Scan(&userID)
		
		if err != nil {
			return fmt.Errorf("failed to insert user %s: %w", user["email"], err)
		}
		userIDs[user["email"]] = userID
		fmt.Printf("    ✅ Added user: %s %s\n", user["first_name"], user["last_name"])
	}

	// Step 2: Add User Roles
	fmt.Println("  📝 Step 2: Adding user roles...")
	roles := map[string][]string{
		"<EMAIL>":        {"admin"},
		"<EMAIL>":     {"customer"},
		"<EMAIL>":   {"customer"},
		"<EMAIL>":  {"vendor", "customer"},
		"<EMAIL>": {"vendor", "customer"},
		"<EMAIL>": {"customer"},
	}

	for email, userRoles := range roles {
		for _, role := range userRoles {
			_, err := db.Exec(`
				INSERT INTO user_roles (user_id, role) VALUES ($1, $2)`,
				userIDs[email], role)
			if err != nil {
				return fmt.Errorf("failed to insert role %s for user %s: %w", role, email, err)
			}
		}
		fmt.Printf("    ✅ Added roles for: %s\n", email)
	}

	// Step 3: Add Service Categories
	fmt.Println("  📝 Step 3: Adding service categories...")
	categoryIDs := make(map[string]string)
	
	categories := []map[string]interface{}{
		{"name": "Home Maintenance", "description": "General home repair and maintenance services", "sort_order": 1},
		{"name": "Plumbing", "description": "Professional plumbing services", "sort_order": 2},
		{"name": "Electrical", "description": "Electrical installation and repair", "sort_order": 3},
		{"name": "Cleaning", "description": "Home and office cleaning services", "sort_order": 4},
		{"name": "Landscaping", "description": "Garden and outdoor maintenance", "sort_order": 5},
	}

	for _, cat := range categories {
		var catID string
		err := db.QueryRow(`
			INSERT INTO service_categories (id, name, description, icon_url, sort_order) 
			VALUES (uuid_generate_v4(), $1, $2, $3, $4)
			RETURNING id`,
			cat["name"], cat["description"], fmt.Sprintf("/icons/%s.svg", strings.ToLower(strings.ReplaceAll(cat["name"].(string), " ", "-"))), cat["sort_order"]).Scan(&catID)
		
		if err != nil {
			return fmt.Errorf("failed to insert service category %s: %w", cat["name"], err)
		}
		categoryIDs[cat["name"].(string)] = catID
		fmt.Printf("    ✅ Added service category: %s\n", cat["name"])
	}

	// Step 4: Add Product Categories
	fmt.Println("  📝 Step 4: Adding product categories...")
	productCategoryIDs := make(map[string]string)
	
	productCategories := []map[string]interface{}{
		{"name": "Tools", "description": "Professional and DIY tools", "sort_order": 1},
		{"name": "Hardware", "description": "Screws, bolts, and hardware supplies", "sort_order": 2},
		{"name": "Electrical Supplies", "description": "Wires, outlets, and electrical components", "sort_order": 3},
		{"name": "Plumbing Supplies", "description": "Pipes, fittings, and plumbing materials", "sort_order": 4},
		{"name": "Safety Equipment", "description": "Safety gear and protective equipment", "sort_order": 5},
	}

	for _, cat := range productCategories {
		var catID string
		err := db.QueryRow(`
			INSERT INTO product_categories (id, name, description, icon_url, sort_order) 
			VALUES (uuid_generate_v4(), $1, $2, $3, $4)
			RETURNING id`,
			cat["name"], cat["description"], fmt.Sprintf("/icons/%s.svg", strings.ToLower(strings.ReplaceAll(cat["name"].(string), " ", "-"))), cat["sort_order"]).Scan(&catID)
		
		if err != nil {
			return fmt.Errorf("failed to insert product category %s: %w", cat["name"], err)
		}
		productCategoryIDs[cat["name"].(string)] = catID
		fmt.Printf("    ✅ Added product category: %s\n", cat["name"])
	}

	// Step 5: Add Services
	fmt.Println("  📝 Step 5: Adding services...")
	serviceIDs := make(map[string]string)
	
	services := []map[string]interface{}{
		{
			"vendor_email": "<EMAIL>",
			"category": "Plumbing",
			"name": "Emergency Plumbing Repair",
			"description": "Fast and reliable emergency plumbing services available 24/7",
			"price": 150.00,
			"duration": 120,
		},
		{
			"vendor_email": "<EMAIL>",
			"category": "Home Maintenance",
			"name": "General Home Repairs",
			"description": "Complete home repair and maintenance services",
			"price": 75.00,
			"duration": 60,
		},
		{
			"vendor_email": "<EMAIL>",
			"category": "Electrical",
			"name": "Electrical Installation",
			"description": "Professional electrical installation and wiring services",
			"price": 120.00,
			"duration": 180,
		},
		{
			"vendor_email": "<EMAIL>",
			"category": "Home Maintenance",
			"name": "Smart Home Setup",
			"description": "Smart home automation and setup services",
			"price": 200.00,
			"duration": 240,
		},
	}

	for _, service := range services {
		var serviceID string
		err := db.QueryRow(`
			INSERT INTO services (id, vendor_id, category_id, name, description, price, price_type, duration, location_type) 
			VALUES (uuid_generate_v4(), $1, $2, $3, $4, $5, 'hourly', $6, 'on_site')
			RETURNING id`,
			userIDs[service["vendor_email"].(string)],
			categoryIDs[service["category"].(string)],
			service["name"],
			service["description"],
			service["price"],
			service["duration"]).Scan(&serviceID)
		
		if err != nil {
			return fmt.Errorf("failed to insert service %s: %w", service["name"], err)
		}
		serviceIDs[service["name"].(string)] = serviceID
		fmt.Printf("    ✅ Added service: %s\n", service["name"])
	}

	// Step 6: Add a Sample Booking
	fmt.Println("  📝 Step 6: Adding sample booking...")
	_, err := db.Exec(`
		INSERT INTO bookings (customer_id, vendor_id, service_id, booking_date, start_time, end_time, status, total_amount, notes)
		VALUES ($1, $2, $3, CURRENT_DATE + INTERVAL '7 days', '10:00'::time, '12:00'::time, 'confirmed', 300.00, 'Sample booking for testing')`,
		userIDs["<EMAIL>"],
		userIDs["<EMAIL>"],
		serviceIDs["Emergency Plumbing Repair"])
	
	if err != nil {
		return fmt.Errorf("failed to insert sample booking: %w", err)
	}
	fmt.Printf("    ✅ Added sample booking\n")

	fmt.Println("✅ All sample data added successfully!")
	return nil
}

func verifySampleData(db *sql.DB) error {
	// Check data in key tables
	tables := map[string]string{
		"users":              "SELECT COUNT(*) FROM users",
		"user_roles":         "SELECT COUNT(*) FROM user_roles",
		"service_categories": "SELECT COUNT(*) FROM service_categories",
		"product_categories": "SELECT COUNT(*) FROM product_categories",
		"services":           "SELECT COUNT(*) FROM services",
		"bookings":           "SELECT COUNT(*) FROM bookings",
	}

	fmt.Println("Data verification:")
	totalRecords := 0
	
	for tableName, query := range tables {
		var count int
		err := db.QueryRow(query).Scan(&count)
		if err != nil {
			fmt.Printf("  ❌ %s: Error - %v\n", tableName, err)
			continue
		}
		fmt.Printf("  ✅ %s: %d records\n", tableName, count)
		totalRecords += count
	}

	if totalRecords == 0 {
		return fmt.Errorf("no sample data was inserted")
	}

	fmt.Printf("✅ Total sample records: %d\n", totalRecords)
	return nil
}

func loadDatabaseConfig() {
	configFile := "../../../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
