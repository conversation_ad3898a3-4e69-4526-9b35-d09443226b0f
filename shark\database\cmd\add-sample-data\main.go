package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🌱 ADDING SAMPLE DATA TO SHARK PLATFORM")
	fmt.Println("=======================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Connecting to: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("❌ Failed to ping database:", err)
	}

	fmt.Println("✅ Database connection successful!")

	// Check if sample data already exists
	fmt.Println("\n📋 Step 1: Checking existing data...")
	if hasExistingData(db) {
		fmt.Println("⚠️  Sample data already exists!")
		fmt.Print("Do you want to clear existing data and add fresh sample data? (y/N): ")
		
		// For automation, we'll skip this and just add data
		fmt.Println("Proceeding to add sample data...")
	}

	// Add sample data
	fmt.Println("\n🌱 Step 2: Adding sample data...")
	err = addSampleData(db)
	if err != nil {
		log.Fatal("❌ Failed to add sample data:", err)
	}

	// Verify data
	fmt.Println("\n✅ Step 3: Verifying sample data...")
	err = verifySampleData(db)
	if err != nil {
		log.Fatal("❌ Failed to verify sample data:", err)
	}

	fmt.Println("\n🎉 SAMPLE DATA ADDED SUCCESSFULLY!")
	fmt.Println("=================================")
	fmt.Println("✅ Your Shark platform now has test data!")
	fmt.Println("✅ You can start testing your microservices!")
}

func hasExistingData(db *sql.DB) bool {
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM users").Scan(&count)
	if err != nil {
		return false
	}
	return count > 0
}

func addSampleData(db *sql.DB) error {
	// Read sample data SQL file
	sqlFile := "../../sample-data.sql"
	content, err := ioutil.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("failed to read sample data file %s: %w", sqlFile, err)
	}

	sqlContent := string(content)
	
	// Execute as a single transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Split SQL into individual statements and execute
	statements := strings.Split(sqlContent, ";")
	successCount := 0
	
	for i, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}

		_, err = tx.Exec(stmt)
		if err != nil {
			fmt.Printf("  ❌ Statement %d failed: %v\n", i+1, err)
			fmt.Printf("     SQL: %s\n", stmt[:min(100, len(stmt))])
			continue
		}
		successCount++
		
		if successCount%10 == 0 {
			fmt.Printf("  ✅ Executed %d statements...\n", successCount)
		}
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	fmt.Printf("✅ Successfully executed %d SQL statements\n", successCount)
	return nil
}

func verifySampleData(db *sql.DB) error {
	// Check data in key tables
	tables := map[string]string{
		"users":              "SELECT COUNT(*) FROM users",
		"service_categories": "SELECT COUNT(*) FROM service_categories",
		"product_categories": "SELECT COUNT(*) FROM product_categories",
		"services":           "SELECT COUNT(*) FROM services",
		"products":           "SELECT COUNT(*) FROM products",
		"bookings":           "SELECT COUNT(*) FROM bookings",
		"orders":             "SELECT COUNT(*) FROM orders",
	}

	fmt.Println("Data verification:")
	totalRecords := 0
	
	for tableName, query := range tables {
		var count int
		err := db.QueryRow(query).Scan(&count)
		if err != nil {
			fmt.Printf("  ❌ %s: Error - %v\n", tableName, err)
			continue
		}
		fmt.Printf("  ✅ %s: %d records\n", tableName, count)
		totalRecords += count
	}

	if totalRecords == 0 {
		return fmt.Errorf("no sample data was inserted")
	}

	fmt.Printf("✅ Total sample records: %d\n", totalRecords)
	return nil
}

func loadDatabaseConfig() {
	configFile := "../../../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
