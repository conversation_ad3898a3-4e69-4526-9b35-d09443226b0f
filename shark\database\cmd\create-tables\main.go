package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🗄️  SHARK PLATFORM - CREATE TABLES")
	fmt.Println("===================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Connecting to: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("❌ Failed to ping database:", err)
	}

	fmt.Println("✅ Database connection successful!")

	// Step 1: Create UUID extension
	fmt.Println("\n📋 Step 1: Creating UUID extension...")
	err = createUUIDExtension(db)
	if err != nil {
		log.Fatal("❌ Failed to create UUID extension:", err)
	}

	// Step 2: Create tables
	fmt.Println("\n📋 Step 2: Creating tables...")
	err = createTables(db)
	if err != nil {
		log.Fatal("❌ Failed to create tables:", err)
	}

	// Step 3: Verify tables
	fmt.Println("\n✅ Step 3: Verifying tables...")
	err = verifyTables(db)
	if err != nil {
		log.Fatal("❌ Failed to verify tables:", err)
	}

	fmt.Println("\n🎉 ALL TABLES CREATED SUCCESSFULLY!")
	fmt.Println("===================================")
	fmt.Println("✅ Your Shark platform database is ready!")
}

func createUUIDExtension(db *sql.DB) error {
	// Try different UUID extension approaches
	extensions := []string{
		"CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"",
		"CREATE EXTENSION IF NOT EXISTS \"pgcrypto\"",
	}

	for _, ext := range extensions {
		_, err := db.Exec(ext)
		if err == nil {
			fmt.Printf("✅ UUID extension created successfully\n")
			
			// Test UUID generation
			var testUUID string
			err = db.QueryRow("SELECT uuid_generate_v4()").Scan(&testUUID)
			if err == nil {
				fmt.Printf("✅ UUID generation test: %s\n", testUUID)
				return nil
			}
			
			// Try alternative
			err = db.QueryRow("SELECT gen_random_uuid()").Scan(&testUUID)
			if err == nil {
				fmt.Printf("✅ UUID generation test (gen_random_uuid): %s\n", testUUID)
				return nil
			}
		}
	}

	return fmt.Errorf("failed to create UUID extension")
}

func createTables(db *sql.DB) error {
	// Read SQL file
	sqlFile := "../../create-tables-ordered.sql"
	content, err := ioutil.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("failed to read SQL file %s: %w", sqlFile, err)
	}

	sqlContent := string(content)
	
	// Execute as a single transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Execute the entire SQL content
	_, err = tx.Exec(sqlContent)
	if err != nil {
		return fmt.Errorf("failed to execute SQL: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	fmt.Println("✅ All tables created successfully!")
	return nil
}

func verifyTables(db *sql.DB) error {
	query := `
		SELECT table_name 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		ORDER BY table_name`

	rows, err := db.Query(query)
	if err != nil {
		return fmt.Errorf("failed to query tables: %w", err)
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}
		tables = append(tables, tableName)
	}

	fmt.Printf("📊 Created %d tables:\n", len(tables))
	for _, table := range tables {
		fmt.Printf("  ✅ %s\n", table)
	}

	// Expected tables for Shark platform
	expectedTables := []string{
		"users", "user_roles", "user_profiles",
		"service_categories", "services", "service_variants", "service_addons",
		"product_categories", "products", "product_variants", "product_addons",
		"bookings", "booking_addons", "vendor_availability",
		"orders", "order_items", "order_item_addons",
		"payments", "reviews", "notifications",
	}

	fmt.Printf("\n🔍 Checking for expected tables:\n")
	missingTables := 0
	for _, expected := range expectedTables {
		found := false
		for _, actual := range tables {
			if actual == expected {
				found = true
				break
			}
		}
		if found {
			fmt.Printf("  ✅ %s\n", expected)
		} else {
			fmt.Printf("  ❌ %s (missing)\n", expected)
			missingTables++
		}
	}

	if missingTables > 0 {
		return fmt.Errorf("%d expected tables are missing", missingTables)
	}

	return nil
}

func loadDatabaseConfig() {
	configFile := "../../../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
