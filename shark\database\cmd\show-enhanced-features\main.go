package main

import (
	"database/sql"
	"fmt"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🌟 SHARK PLATFORM ENHANCED FEATURES OVERVIEW")
	fmt.Println("============================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		fmt.Printf("❌ Failed to connect to database: %v\n", err)
		return
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		fmt.Printf("❌ Failed to ping database: %v\n", err)
		return
	}

	fmt.Println("✅ Connected to database successfully!")

	// Show all enhanced features
	showTimeSlots(db)
	showCoupons(db)
	showTaxRules(db)
	showFees(db)
	showAddresses(db)
	showShippingMethods(db)
	showVendorSchedules(db)
	showDatabaseSummary(db)

	fmt.Println("\n🎯 NEXT STEPS:")
	fmt.Println("=============")
	fmt.Println("1. ✅ Database setup - COMPLETE")
	fmt.Println("2. ✅ Enhanced sample data - COMPLETE")
	fmt.Println("3. 🔄 Test microservices with enhanced data")
	fmt.Println("4. 🔄 Build frontend features:")
	fmt.Println("   • Shopping cart interface")
	fmt.Println("   • Coupon application system")
	fmt.Println("   • Multi-vendor product listings")
	fmt.Println("   • Advanced scheduling interface")
	fmt.Println("   • Tax and fee calculation")
	fmt.Println("   • Address management")
	fmt.Println("   • Shipping options")
}

func showTimeSlots(db *sql.DB) {
	fmt.Println("\n⏰ TIME SLOTS:")
	fmt.Println("=============")
	
	rows, err := db.Query(`
		SELECT name, start_time, end_time, duration
		FROM time_slots 
		ORDER BY start_time`)
	
	if err != nil {
		fmt.Printf("❌ Error querying time slots: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var name, startTime, endTime string
		var duration int
		err := rows.Scan(&name, &startTime, &endTime, &duration)
		if err != nil {
			continue
		}
		fmt.Printf("  🕐 %s: %s - %s (%d minutes)\n", name, startTime, endTime, duration)
	}
}

func showCoupons(db *sql.DB) {
	fmt.Println("\n🎫 COUPONS:")
	fmt.Println("==========")
	
	rows, err := db.Query(`
		SELECT code, name, discount_type, discount_value, minimum_order_amount, usage_limit
		FROM coupons 
		WHERE is_active = true
		ORDER BY code`)
	
	if err != nil {
		fmt.Printf("❌ Error querying coupons: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var code, name, discountType string
		var discountValue, minAmount float64
		var usageLimit sql.NullInt64
		err := rows.Scan(&code, &name, &discountType, &discountValue, &minAmount, &usageLimit)
		if err != nil {
			continue
		}
		
		limitStr := "unlimited"
		if usageLimit.Valid {
			limitStr = fmt.Sprintf("%d uses", usageLimit.Int64)
		}
		
		valueStr := ""
		switch discountType {
		case "percentage":
			valueStr = fmt.Sprintf("%.0f%% off", discountValue)
		case "fixed_amount":
			valueStr = fmt.Sprintf("$%.2f off", discountValue)
		case "free_shipping":
			valueStr = "Free shipping"
		}
		
		fmt.Printf("  🎟️  %s: %s (%s, min $%.2f, %s)\n", code, valueStr, name, minAmount, limitStr)
	}
}

func showTaxRules(db *sql.DB) {
	fmt.Println("\n💰 TAX RULES:")
	fmt.Println("=============")
	
	rows, err := db.Query(`
		SELECT name, tax_type, rate, country, state
		FROM tax_rules 
		WHERE is_active = true
		ORDER BY country, state`)
	
	if err != nil {
		fmt.Printf("❌ Error querying tax rules: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var name, taxType, country string
		var rate float64
		var state sql.NullString
		err := rows.Scan(&name, &taxType, &rate, &country, &state)
		if err != nil {
			continue
		}
		
		location := country
		if state.Valid {
			location = fmt.Sprintf("%s, %s", state.String, country)
		}
		
		fmt.Printf("  💸 %s: %.2f%% (%s) - %s\n", name, rate*100, taxType, location)
	}
}

func showFees(db *sql.DB) {
	fmt.Println("\n💳 FEES:")
	fmt.Println("========")
	
	rows, err := db.Query(`
		SELECT name, fee_type, calculation_type, amount, applicable_to
		FROM fees 
		WHERE is_active = true
		ORDER BY name`)
	
	if err != nil {
		fmt.Printf("❌ Error querying fees: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var name, feeType, calcType, applicableTo string
		var amount float64
		err := rows.Scan(&name, &feeType, &calcType, &amount, &applicableTo)
		if err != nil {
			continue
		}
		
		amountStr := ""
		if calcType == "percentage" {
			amountStr = fmt.Sprintf("%.1f%%", amount)
		} else {
			amountStr = fmt.Sprintf("$%.2f", amount)
		}
		
		fmt.Printf("  💰 %s: %s (%s, applies to %s)\n", name, amountStr, feeType, applicableTo)
	}
}

func showAddresses(db *sql.DB) {
	fmt.Println("\n📍 USER ADDRESSES:")
	fmt.Println("==================")
	
	rows, err := db.Query(`
		SELECT u.first_name || ' ' || u.last_name as user_name,
		       ua.type, ua.address_line1, ua.city, ua.state, ua.postal_code
		FROM user_addresses ua
		JOIN users u ON ua.user_id = u.id
		ORDER BY user_name, ua.type`)
	
	if err != nil {
		fmt.Printf("❌ Error querying addresses: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var userName, addrType, address, city, state, postal string
		err := rows.Scan(&userName, &addrType, &address, &city, &state, &postal)
		if err != nil {
			continue
		}
		
		fmt.Printf("  🏠 %s (%s): %s, %s, %s %s\n", userName, addrType, address, city, state, postal)
	}
}

func showShippingMethods(db *sql.DB) {
	fmt.Println("\n🚚 SHIPPING METHODS:")
	fmt.Println("===================")
	
	rows, err := db.Query(`
		SELECT name, carrier, service_type, base_cost, estimated_days_min, estimated_days_max
		FROM shipping_methods 
		WHERE is_active = true
		ORDER BY base_cost`)
	
	if err != nil {
		fmt.Printf("❌ Error querying shipping methods: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var name, carrier, serviceType string
		var baseCost float64
		var daysMin, daysMax int
		err := rows.Scan(&name, &carrier, &serviceType, &baseCost, &daysMin, &daysMax)
		if err != nil {
			continue
		}
		
		fmt.Printf("  📦 %s (%s %s): $%.2f, %d-%d days\n", name, carrier, serviceType, baseCost, daysMin, daysMax)
	}
}

func showVendorSchedules(db *sql.DB) {
	fmt.Println("\n📅 VENDOR SCHEDULES:")
	fmt.Println("===================")
	
	rows, err := db.Query(`
		SELECT u.first_name || ' ' || u.last_name as vendor_name,
		       vs.day_of_week,
		       ts.name as slot_name,
		       ts.start_time,
		       ts.end_time,
		       vs.max_bookings
		FROM vendor_schedules vs
		JOIN users u ON vs.vendor_id = u.id
		JOIN time_slots ts ON vs.time_slot_id = ts.id
		WHERE vs.is_available = true
		ORDER BY vendor_name, vs.day_of_week, ts.start_time
		LIMIT 10`)
	
	if err != nil {
		fmt.Printf("❌ Error querying vendor schedules: %v\n", err)
		return
	}
	defer rows.Close()

	dayNames := []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}
	
	for rows.Next() {
		var vendorName, slotName, startTime, endTime string
		var dayOfWeek, maxBookings int
		err := rows.Scan(&vendorName, &dayOfWeek, &slotName, &startTime, &endTime, &maxBookings)
		if err != nil {
			continue
		}
		
		dayName := "Unknown"
		if dayOfWeek >= 0 && dayOfWeek < len(dayNames) {
			dayName = dayNames[dayOfWeek]
		}
		
		fmt.Printf("  👨‍🔧 %s - %s %s (%s-%s, max %d bookings)\n", 
			vendorName, dayName, slotName, startTime, endTime, maxBookings)
	}
}

func showDatabaseSummary(db *sql.DB) {
	fmt.Println("\n📊 DATABASE SUMMARY:")
	fmt.Println("===================")
	
	// Get total table count
	var totalTables int
	err := db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'").Scan(&totalTables)
	if err != nil {
		fmt.Printf("❌ Error counting tables: %v\n", err)
		return
	}
	
	// Get record counts for key tables
	tables := map[string]string{
		"Users": "SELECT COUNT(*) FROM users",
		"Products": "SELECT COUNT(*) FROM products",
		"Services": "SELECT COUNT(*) FROM services",
		"Categories (Service)": "SELECT COUNT(*) FROM service_categories",
		"Categories (Product)": "SELECT COUNT(*) FROM product_categories",
		"Time Slots": "SELECT COUNT(*) FROM time_slots",
		"Vendor Schedules": "SELECT COUNT(*) FROM vendor_schedules",
		"Coupons": "SELECT COUNT(*) FROM coupons",
		"Tax Rules": "SELECT COUNT(*) FROM tax_rules",
		"Fees": "SELECT COUNT(*) FROM fees",
		"Addresses": "SELECT COUNT(*) FROM user_addresses",
		"Shipping Methods": "SELECT COUNT(*) FROM shipping_methods",
		"Bookings": "SELECT COUNT(*) FROM bookings",
		"Orders": "SELECT COUNT(*) FROM orders",
	}
	
	fmt.Printf("📋 Total Tables: %d\n", totalTables)
	fmt.Println("\n📈 Record Counts:")
	
	totalRecords := 0
	for tableName, query := range tables {
		var count int
		err := db.QueryRow(query).Scan(&count)
		if err != nil {
			fmt.Printf("  ❌ %s: Error\n", tableName)
			continue
		}
		fmt.Printf("  📊 %s: %d\n", tableName, count)
		totalRecords += count
	}
	
	fmt.Printf("\n🎯 Total Sample Records: %d\n", totalRecords)
	
	fmt.Println("\n✅ ENHANCED FEATURES READY:")
	fmt.Println("  🛒 Shopping Cart System")
	fmt.Println("  🎫 Coupon & Discount System")
	fmt.Println("  💰 Dynamic Tax Calculation")
	fmt.Println("  💳 Flexible Fee Structure")
	fmt.Println("  📅 Advanced Scheduling")
	fmt.Println("  📍 Address Management")
	fmt.Println("  🚚 Shipping Options")
	fmt.Println("  👥 Multi-Vendor Support")
	fmt.Println("  🏷️ Unlimited Subcategories")
	fmt.Println("  ❤️ Wishlist Functionality")
}

func loadDatabaseConfig() {
	configFile := "../../../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		return
	}

	content, err := os.ReadFile(configFile)
	if err != nil {
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
