package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("📊 SHARK PLATFORM SAMPLE DATA OVERVIEW")
	fmt.Println("======================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("❌ Failed to ping database:", err)
	}

	fmt.Println("✅ Connected to database successfully!")

	// Show sample data
	showUsers(db)
	showServiceCategories(db)
	showProductCategories(db)
	showServices(db)
	showBookings(db)

	fmt.Println("\n🎯 NEXT STEPS:")
	fmt.Println("=============")
	fmt.Println("1. ✅ Database setup - COMPLETE")
	fmt.Println("2. ✅ Sample data - COMPLETE")
	fmt.Println("3. 🔄 Test microservices with this data")
	fmt.Println("4. 🔄 Build your application features")
	fmt.Println("5. 🔄 Add more sample data as needed")
}

func showUsers(db *sql.DB) {
	fmt.Println("\n👥 USERS:")
	fmt.Println("========")
	
	rows, err := db.Query(`
		SELECT u.first_name, u.last_name, u.email, 
		       STRING_AGG(ur.role, ', ' ORDER BY ur.role) as roles
		FROM users u
		LEFT JOIN user_roles ur ON u.id = ur.user_id
		GROUP BY u.id, u.first_name, u.last_name, u.email
		ORDER BY u.first_name`)
	
	if err != nil {
		fmt.Printf("❌ Error querying users: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var firstName, lastName, email, roles string
		err := rows.Scan(&firstName, &lastName, &email, &roles)
		if err != nil {
			continue
		}
		fmt.Printf("  👤 %s %s (%s) - Roles: %s\n", firstName, lastName, email, roles)
	}
}

func showServiceCategories(db *sql.DB) {
	fmt.Println("\n🛠️  SERVICE CATEGORIES:")
	fmt.Println("======================")
	
	rows, err := db.Query(`
		SELECT name, description 
		FROM service_categories 
		ORDER BY sort_order`)
	
	if err != nil {
		fmt.Printf("❌ Error querying service categories: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var name, description string
		err := rows.Scan(&name, &description)
		if err != nil {
			continue
		}
		fmt.Printf("  🔧 %s - %s\n", name, description)
	}
}

func showProductCategories(db *sql.DB) {
	fmt.Println("\n📦 PRODUCT CATEGORIES:")
	fmt.Println("=====================")
	
	rows, err := db.Query(`
		SELECT name, description 
		FROM product_categories 
		ORDER BY sort_order`)
	
	if err != nil {
		fmt.Printf("❌ Error querying product categories: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var name, description string
		err := rows.Scan(&name, &description)
		if err != nil {
			continue
		}
		fmt.Printf("  📦 %s - %s\n", name, description)
	}
}

func showServices(db *sql.DB) {
	fmt.Println("\n🛍️  SERVICES:")
	fmt.Println("=============")
	
	rows, err := db.Query(`
		SELECT s.name, s.description, s.price, s.price_type,
		       u.first_name || ' ' || u.last_name as vendor_name,
		       sc.name as category_name
		FROM services s
		JOIN users u ON s.vendor_id = u.id
		JOIN service_categories sc ON s.category_id = sc.id
		ORDER BY s.name`)
	
	if err != nil {
		fmt.Printf("❌ Error querying services: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var name, description, priceType, vendorName, categoryName string
		var price float64
		err := rows.Scan(&name, &description, &price, &priceType, &vendorName, &categoryName)
		if err != nil {
			continue
		}
		fmt.Printf("  🛠️  %s\n", name)
		fmt.Printf("      Vendor: %s | Category: %s | Price: $%.2f/%s\n", vendorName, categoryName, price, priceType)
		fmt.Printf("      Description: %s\n", description)
		fmt.Println()
	}
}

func showBookings(db *sql.DB) {
	fmt.Println("\n📅 BOOKINGS:")
	fmt.Println("============")
	
	rows, err := db.Query(`
		SELECT b.booking_date, b.start_time, b.end_time, b.status, b.total_amount,
		       customer.first_name || ' ' || customer.last_name as customer_name,
		       vendor.first_name || ' ' || vendor.last_name as vendor_name,
		       s.name as service_name
		FROM bookings b
		JOIN users customer ON b.customer_id = customer.id
		JOIN users vendor ON b.vendor_id = vendor.id
		JOIN services s ON b.service_id = s.id
		ORDER BY b.booking_date`)
	
	if err != nil {
		fmt.Printf("❌ Error querying bookings: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var bookingDate, startTime, endTime, status, customerName, vendorName, serviceName string
		var totalAmount float64
		err := rows.Scan(&bookingDate, &startTime, &endTime, &status, &totalAmount, &customerName, &vendorName, &serviceName)
		if err != nil {
			continue
		}
		fmt.Printf("  📅 %s from %s to %s\n", bookingDate, startTime, endTime)
		fmt.Printf("      Customer: %s | Vendor: %s\n", customerName, vendorName)
		fmt.Printf("      Service: %s | Status: %s | Amount: $%.2f\n", serviceName, status, totalAmount)
		fmt.Println()
	}
}

func loadDatabaseConfig() {
	configFile := "../../../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
