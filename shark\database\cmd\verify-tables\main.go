package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🔍 VERIFYING TABLES IN DODO DATABASE")
	fmt.Println("====================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Connecting to: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("❌ Failed to ping database:", err)
	}

	fmt.Println("✅ Database connection successful!")

	// Check current database
	fmt.Println("\n📋 Step 1: Verifying current database...")
	var currentDB string
	err = db.QueryRow("SELECT current_database()").Scan(&currentDB)
	if err != nil {
		log.Fatal("❌ Failed to get current database:", err)
	}
	fmt.Printf("✅ Connected to database: %s\n", currentDB)

	// Check all tables in public schema
	fmt.Println("\n📋 Step 2: Checking tables in public schema...")
	query := `
		SELECT table_name, table_type
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		ORDER BY table_name`

	rows, err := db.Query(query)
	if err != nil {
		log.Fatal("❌ Failed to query tables:", err)
	}
	defer rows.Close()

	var tables []string
	var tableCount int
	for rows.Next() {
		var tableName, tableType string
		if err := rows.Scan(&tableName, &tableType); err != nil {
			continue
		}
		tables = append(tables, tableName)
		tableCount++
		fmt.Printf("  ✅ %s (%s)\n", tableName, tableType)
	}

	if tableCount == 0 {
		fmt.Println("❌ NO TABLES FOUND in public schema!")
	} else {
		fmt.Printf("✅ Found %d tables in public schema\n", tableCount)
	}

	// Check all schemas
	fmt.Println("\n📋 Step 3: Checking all schemas...")
	schemaQuery := `
		SELECT schema_name 
		FROM information_schema.schemata 
		WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
		ORDER BY schema_name`

	rows2, err := db.Query(schemaQuery)
	if err != nil {
		log.Fatal("❌ Failed to query schemas:", err)
	}
	defer rows2.Close()

	fmt.Println("Available schemas:")
	for rows2.Next() {
		var schemaName string
		if err := rows2.Scan(&schemaName); err != nil {
			continue
		}
		fmt.Printf("  - %s\n", schemaName)
	}

	// Check extensions
	fmt.Println("\n📋 Step 4: Checking installed extensions...")
	extQuery := `SELECT extname FROM pg_extension ORDER BY extname`
	rows3, err := db.Query(extQuery)
	if err != nil {
		log.Fatal("❌ Failed to query extensions:", err)
	}
	defer rows3.Close()

	fmt.Println("Installed extensions:")
	for rows3.Next() {
		var extName string
		if err := rows3.Scan(&extName); err != nil {
			continue
		}
		fmt.Printf("  - %s\n", extName)
	}

	// Test UUID generation
	fmt.Println("\n📋 Step 5: Testing UUID generation...")
	var testUUID string
	err = db.QueryRow("SELECT uuid_generate_v4()").Scan(&testUUID)
	if err != nil {
		fmt.Printf("❌ uuid_generate_v4() failed: %v\n", err)
		
		// Try alternative
		err = db.QueryRow("SELECT gen_random_uuid()").Scan(&testUUID)
		if err != nil {
			fmt.Printf("❌ gen_random_uuid() also failed: %v\n", err)
		} else {
			fmt.Printf("✅ gen_random_uuid() works: %s\n", testUUID)
		}
	} else {
		fmt.Printf("✅ uuid_generate_v4() works: %s\n", testUUID)
	}

	// Summary
	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("DIAGNOSIS SUMMARY")
	fmt.Println(strings.Repeat("=", 50))
	
	if tableCount == 0 {
		fmt.Println("❌ ISSUE: No tables found in the database!")
		fmt.Println("\n💡 POSSIBLE CAUSES:")
		fmt.Println("1. Tables were created in wrong database")
		fmt.Println("2. Table creation script failed silently")
		fmt.Println("3. Tables were created in different schema")
		fmt.Println("4. Transaction was rolled back")
		
		fmt.Println("\n🔧 RECOMMENDED ACTIONS:")
		fmt.Println("1. Re-run table creation script with verbose output")
		fmt.Println("2. Check if tables exist in other databases")
		fmt.Println("3. Manually create tables using pgAdmin")
	} else {
		fmt.Printf("✅ SUCCESS: Found %d tables in database\n", tableCount)
	}
}

func loadDatabaseConfig() {
	configFile := "../../../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
