package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🗄️  CREATING SHARK PLATFORM DATABASE TABLES")
	fmt.Println("============================================")

	// Load database configuration from config/database.env
	loadDatabaseConfig()

	// Database connection
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "shark_user")
	dbPassword := getEnv("DB_PASSWORD", "shark_password")
	dbName := getEnv("DB_NAME", "shark_db")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	fmt.Printf("Connecting to database: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("✅ Database connection successful!")

	// Read and execute SQL file
	sqlFile := "create-all-tables.sql"
	fmt.Printf("\n📄 Reading SQL file: %s\n", sqlFile)

	content, err := ioutil.ReadFile(sqlFile)
	if err != nil {
		log.Fatal("Failed to read SQL file:", err)
	}

	sqlContent := string(content)
	fmt.Printf("📊 SQL file size: %d characters\n", len(sqlContent))

	// Split SQL content by statements (rough split by semicolon)
	statements := strings.Split(sqlContent, ";")
	
	fmt.Printf("🔧 Executing %d SQL statements...\n\n", len(statements))

	successCount := 0
	errorCount := 0

	for i, statement := range statements {
		statement = strings.TrimSpace(statement)
		if statement == "" || strings.HasPrefix(statement, "--") {
			continue
		}

		fmt.Printf("Executing statement %d...", i+1)

		_, err := db.Exec(statement)
		if err != nil {
			fmt.Printf(" ❌ FAILED\n")
			fmt.Printf("   Error: %v\n", err)
			fmt.Printf("   Statement: %s\n", statement[:min(100, len(statement))])
			errorCount++
		} else {
			fmt.Printf(" ✅ SUCCESS\n")
			successCount++
		}
	}

	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("DATABASE TABLE CREATION SUMMARY")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Printf("✅ Successful statements: %d\n", successCount)
	fmt.Printf("❌ Failed statements: %d\n", errorCount)

	if errorCount == 0 {
		fmt.Println("\n🎉 ALL TABLES CREATED SUCCESSFULLY!")
		
		// Verify tables were created
		fmt.Println("\n📋 Verifying created tables...")
		verifyTables(db)
	} else {
		fmt.Printf("\n⚠️  %d statements failed. Please check the errors above.\n", errorCount)
	}
}

func verifyTables(db *sql.DB) {
	query := `
		SELECT table_name 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		ORDER BY table_name`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ Failed to verify tables: %v\n", err)
		return
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}
		tables = append(tables, tableName)
	}

	fmt.Printf("📊 Found %d tables:\n", len(tables))
	for _, table := range tables {
		fmt.Printf("  ✅ %s\n", table)
	}
}

func loadDatabaseConfig() {
	configFile := "../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded from config/database.env")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
