# PostgreSQL Connection Diagnostics
Write-Host "POSTGRESQL CONNECTION DIAGNOSTICS" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor White

Write-Host "`n1. CHECKING POSTGRESQL SERVICE STATUS..." -ForegroundColor Yellow

# Check if PostgreSQL service is running
$pgServices = Get-Service | Where-Object { $_.Name -like "*postgres*" -or $_.DisplayName -like "*PostgreSQL*" }

if ($pgServices) {
    Write-Host "Found PostgreSQL services:" -ForegroundColor Green
    foreach ($service in $pgServices) {
        $status = if ($service.Status -eq "Running") { "RUNNING" } else { "STOPPED" }
        $color = if ($service.Status -eq "Running") { "Green" } else { "Red" }
        Write-Host "  $($service.DisplayName): $status" -ForegroundColor $color
    }
} else {
    Write-Host "No PostgreSQL services found!" -ForegroundColor Red
    Write-Host "PostgreSQL might not be installed or installed differently." -ForegroundColor Yellow
}

Write-Host "`n2. CHECKING PORT 5432 (PostgreSQL default)..." -ForegroundColor Yellow

# Check if port 5432 is listening
try {
    $connection = Test-NetConnection -ComputerName localhost -Port 5432 -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "Port 5432 is OPEN and listening" -ForegroundColor Green
    } else {
        Write-Host "Port 5432 is CLOSED or not listening" -ForegroundColor Red
    }
} catch {
    Write-Host "Could not test port 5432" -ForegroundColor Yellow
}

Write-Host "`n3. CHECKING POSTGRESQL INSTALLATION..." -ForegroundColor Yellow

# Check common PostgreSQL installation paths
$commonPaths = @(
    "C:\Program Files\PostgreSQL",
    "C:\Program Files (x86)\PostgreSQL",
    "C:\PostgreSQL"
)

$foundInstallations = @()
foreach ($path in $commonPaths) {
    if (Test-Path $path) {
        $versions = Get-ChildItem $path -Directory | Where-Object { $_.Name -match "^\d+" }
        foreach ($version in $versions) {
            $foundInstallations += "$path\$($version.Name)"
        }
    }
}

if ($foundInstallations) {
    Write-Host "Found PostgreSQL installations:" -ForegroundColor Green
    foreach ($install in $foundInstallations) {
        Write-Host "  $install" -ForegroundColor Green
        
        # Check if pg_ctl exists
        $pgCtl = Join-Path $install "bin\pg_ctl.exe"
        if (Test-Path $pgCtl) {
            Write-Host "    pg_ctl found: $pgCtl" -ForegroundColor Cyan
        }
        
        # Check if psql exists
        $psql = Join-Path $install "bin\psql.exe"
        if (Test-Path $psql) {
            Write-Host "    psql found: $psql" -ForegroundColor Cyan
        }
    }
} else {
    Write-Host "No PostgreSQL installations found in common locations" -ForegroundColor Red
}

Write-Host "`n4. CHECKING PGADMIN INSTALLATION..." -ForegroundColor Yellow

# Check if pgAdmin is installed
$pgAdminPaths = @(
    "C:\Program Files\pgAdmin 4",
    "C:\Program Files (x86)\pgAdmin 4",
    "$env:APPDATA\pgAdmin"
)

$pgAdminFound = $false
foreach ($path in $pgAdminPaths) {
    if (Test-Path $path) {
        Write-Host "pgAdmin found: $path" -ForegroundColor Green
        $pgAdminFound = $true
    }
}

if (-not $pgAdminFound) {
    Write-Host "pgAdmin not found in common locations" -ForegroundColor Yellow
}

Write-Host "`n5. TESTING BASIC CONNECTIVITY..." -ForegroundColor Yellow

# Try to connect using different methods
Write-Host "Testing localhost connectivity..." -ForegroundColor Cyan

try {
    $ping = Test-Connection -ComputerName localhost -Count 1 -Quiet
    if ($ping) {
        Write-Host "localhost is reachable" -ForegroundColor Green
    } else {
        Write-Host "localhost is not reachable" -ForegroundColor Red
    }
} catch {
    Write-Host "Could not test localhost connectivity" -ForegroundColor Yellow
}

Write-Host "`n" + "="*50 -ForegroundColor White
Write-Host "DIAGNOSIS SUMMARY" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor White

Write-Host "`nPOSSIBLE ISSUES:" -ForegroundColor Yellow
Write-Host "1. PostgreSQL service is not running" -ForegroundColor White
Write-Host "2. PostgreSQL is not installed" -ForegroundColor White
Write-Host "3. PostgreSQL is running on a different port" -ForegroundColor White
Write-Host "4. Password authentication is configured incorrectly" -ForegroundColor White
Write-Host "5. PostgreSQL is configured for different authentication method" -ForegroundColor White

Write-Host "`nNEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. If PostgreSQL service is stopped, start it" -ForegroundColor White
Write-Host "2. If PostgreSQL is not installed, install it" -ForegroundColor White
Write-Host "3. Check PostgreSQL configuration files" -ForegroundColor White
Write-Host "4. Try connecting with pgAdmin first" -ForegroundColor White

Write-Host "`nCOMMON SOLUTIONS:" -ForegroundColor Green
Write-Host "- Start PostgreSQL service: Services.msc -> postgresql -> Start" -ForegroundColor White
Write-Host "- Reset postgres user password" -ForegroundColor White
Write-Host "- Check pg_hba.conf for authentication settings" -ForegroundColor White
Write-Host "- Reinstall PostgreSQL if necessary" -ForegroundColor White
