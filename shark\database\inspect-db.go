package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/lib/pq"
)

func main() {
	// Database connection
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "shark_user")
	dbPassword := getEnv("DB_PASSWORD", "shark_password")
	dbName := getEnv("DB_NAME", "shark_db")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("🗄️  SHARK DATABASE INSPECTION")
	fmt.Println("============================")
	fmt.Printf("Connected to: %s@%s:%s/%s\n\n", dbUser, dbHost, dbPort, dbName)

	// List all tables
	fmt.Println("📋 TABLES IN DATABASE:")
	fmt.Println("----------------------")
	
	query := `
		SELECT table_name, table_type 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		ORDER BY table_name`
	
	rows, err := db.Query(query)
	if err != nil {
		log.Fatal("Failed to query tables:", err)
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName, tableType string
		if err := rows.Scan(&tableName, &tableType); err != nil {
			log.Fatal("Failed to scan table:", err)
		}
		fmt.Printf("  ✅ %s (%s)\n", tableName, tableType)
		tables = append(tables, tableName)
	}

	if len(tables) == 0 {
		fmt.Println("  ❌ No tables found!")
		return
	}

	fmt.Printf("\n📊 FOUND %d TABLES\n\n", len(tables))

	// Show table details for each table
	for _, tableName := range tables {
		fmt.Printf("🔍 TABLE: %s\n", tableName)
		fmt.Println("-------------------")
		
		// Get column information
		columnQuery := `
			SELECT column_name, data_type, is_nullable, column_default
			FROM information_schema.columns 
			WHERE table_name = $1 AND table_schema = 'public'
			ORDER BY ordinal_position`
		
		columnRows, err := db.Query(columnQuery, tableName)
		if err != nil {
			fmt.Printf("  ❌ Error getting columns: %v\n\n", err)
			continue
		}
		
		fmt.Println("  COLUMNS:")
		for columnRows.Next() {
			var columnName, dataType, isNullable string
			var columnDefault sql.NullString
			
			if err := columnRows.Scan(&columnName, &dataType, &isNullable, &columnDefault); err != nil {
				fmt.Printf("    ❌ Error scanning column: %v\n", err)
				continue
			}
			
			defaultValue := "NULL"
			if columnDefault.Valid {
				defaultValue = columnDefault.String
			}
			
			fmt.Printf("    - %s: %s (nullable: %s, default: %s)\n", 
				columnName, dataType, isNullable, defaultValue)
		}
		columnRows.Close()
		
		// Get row count
		countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)
		var count int
		if err := db.QueryRow(countQuery).Scan(&count); err != nil {
			fmt.Printf("  ❌ Error getting count: %v\n", err)
		} else {
			fmt.Printf("  📊 ROWS: %d\n", count)
		}
		
		// Show sample data if table has rows
		if count > 0 && count <= 1000 {
			fmt.Println("  📄 SAMPLE DATA (first 3 rows):")
			sampleQuery := fmt.Sprintf("SELECT * FROM %s LIMIT 3", tableName)
			sampleRows, err := db.Query(sampleQuery)
			if err != nil {
				fmt.Printf("    ❌ Error getting sample data: %v\n", err)
			} else {
				// Get column names
				columns, err := sampleRows.Columns()
				if err != nil {
					fmt.Printf("    ❌ Error getting column names: %v\n", err)
				} else {
					// Print header
					fmt.Print("    ")
					for _, col := range columns {
						fmt.Printf("%-20s ", col)
					}
					fmt.Println()
					
					// Print rows
					rowNum := 1
					for sampleRows.Next() && rowNum <= 3 {
						// Create slice to hold values
						values := make([]interface{}, len(columns))
						valuePtrs := make([]interface{}, len(columns))
						for i := range values {
							valuePtrs[i] = &values[i]
						}
						
						if err := sampleRows.Scan(valuePtrs...); err != nil {
							fmt.Printf("    ❌ Error scanning row: %v\n", err)
							continue
						}
						
						fmt.Printf("    ")
						for _, val := range values {
							if val == nil {
								fmt.Printf("%-20s ", "NULL")
							} else {
								str := fmt.Sprintf("%v", val)
								if len(str) > 18 {
									str = str[:15] + "..."
								}
								fmt.Printf("%-20s ", str)
							}
						}
						fmt.Println()
						rowNum++
					}
				}
				sampleRows.Close()
			}
		}
		
		fmt.Println()
	}

	fmt.Println("🎉 Database inspection complete!")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
