-- Migration: Add Service and Product Variants & Add-ons
-- Created: 2025-06-26

-- Service Variants Table
CREATE TABLE IF NOT EXISTS service_variants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_id UUID NOT NULL REFERENCES services(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    price_type VARCHAR(20) NOT NULL CHECK (price_type IN ('fixed', 'hourly', 'custom')),
    duration INTEGER, -- in minutes
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Service Add-ons Table
CREATE TABLE IF NOT EXISTS service_addons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_id UUID NOT NULL REFERENCES services(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    price_type VARCHAR(20) NOT NULL CHECK (price_type IN ('fixed', 'hourly', 'custom')),
    duration INTEGER, -- additional minutes
    is_required BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    max_quantity INTEGER CHECK (max_quantity > 0), -- NULL = unlimited
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product Variants Table (for different sizes, colors, configurations)
CREATE TABLE IF NOT EXISTS product_variants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL, -- e.g., "Small", "Red", "2-Pack"
    description TEXT,
    price_adjustment DECIMAL(10,2) DEFAULT 0, -- price difference from base product
    sku_suffix VARCHAR(20), -- e.g., "-SM", "-RED", "-2PK"
    attributes JSONB, -- flexible attributes like {"size": "small", "color": "red"}
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product Add-ons Table (for accessories, warranties, etc.)
CREATE TABLE IF NOT EXISTS product_addons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL, -- e.g., "Extended Warranty", "Installation Service"
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    is_required BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    max_quantity INTEGER CHECK (max_quantity > 0), -- NULL = unlimited
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_service_variants_service_id ON service_variants(service_id);
CREATE INDEX IF NOT EXISTS idx_service_variants_active ON service_variants(is_active);
CREATE INDEX IF NOT EXISTS idx_service_addons_service_id ON service_addons(service_id);
CREATE INDEX IF NOT EXISTS idx_service_addons_active ON service_addons(is_active);

CREATE INDEX IF NOT EXISTS idx_product_variants_product_id ON product_variants(product_id);
CREATE INDEX IF NOT EXISTS idx_product_variants_active ON product_variants(is_active);
CREATE INDEX IF NOT EXISTS idx_product_addons_product_id ON product_addons(product_id);
CREATE INDEX IF NOT EXISTS idx_product_addons_active ON product_addons(is_active);

-- Sample data for service variants
INSERT INTO service_variants (service_id, name, description, price, price_type, duration, is_default, sort_order) VALUES
-- House Cleaning variants
((SELECT id FROM services WHERE name = 'Regular House Cleaning' LIMIT 1), 'Basic Clean', 'Standard cleaning of main areas', 80.00, 'fixed', 120, true, 1),
((SELECT id FROM services WHERE name = 'Regular House Cleaning' LIMIT 1), 'Deep Clean', 'Thorough cleaning including baseboards, inside appliances', 150.00, 'fixed', 240, false, 2),
((SELECT id FROM services WHERE name = 'Regular House Cleaning' LIMIT 1), 'Move-in/Move-out', 'Complete cleaning for moving', 200.00, 'fixed', 360, false, 3);

-- Sample data for service add-ons
INSERT INTO service_addons (service_id, name, description, price, price_type, is_required, max_quantity, sort_order) VALUES
-- House Cleaning add-ons
((SELECT id FROM services WHERE name = 'Regular House Cleaning' LIMIT 1), 'Inside Oven Cleaning', 'Deep clean inside of oven', 25.00, 'fixed', false, 1, 1),
((SELECT id FROM services WHERE name = 'Regular House Cleaning' LIMIT 1), 'Inside Refrigerator', 'Clean inside of refrigerator', 20.00, 'fixed', false, 1, 2),
((SELECT id FROM services WHERE name = 'Regular House Cleaning' LIMIT 1), 'Extra Cleaner', 'Additional cleaning person', 40.00, 'fixed', false, 3, 3),
((SELECT id FROM services WHERE name = 'Regular House Cleaning' LIMIT 1), 'Eco-Friendly Products', 'Use only eco-friendly cleaning products', 15.00, 'fixed', false, 1, 4);

-- Sample data for product variants (if products exist)
-- This will only insert if the products table has data
INSERT INTO product_variants (product_id, name, description, price_adjustment, sku_suffix, attributes, is_default, sort_order)
SELECT 
    p.id,
    'Standard Size',
    'Regular size option',
    0.00,
    '-STD',
    '{"size": "standard"}',
    true,
    1
FROM products p
WHERE p.name LIKE '%Vacuum%' OR p.name LIKE '%Cleaner%'
LIMIT 3;

-- Sample data for product add-ons
INSERT INTO product_addons (product_id, name, description, price, is_required, max_quantity, sort_order)
SELECT 
    p.id,
    'Extended Warranty',
    '2-year extended warranty coverage',
    49.99,
    false,
    1,
    1
FROM products p
WHERE p.name LIKE '%Vacuum%' OR p.name LIKE '%Cleaner%'
LIMIT 3;
