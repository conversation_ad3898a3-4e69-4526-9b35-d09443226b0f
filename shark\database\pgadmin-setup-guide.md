# PostgreSQL Setup Verification with pgAdmin

## 🎯 **Current Configuration (from your config/database.env)**
```
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=Sharath@1050
DB_NAME=dodo
```

## 📋 **Step-by-Step Verification in pgAdmin**

### **Step 1: Check Server Connection**
1. Open pgAdmin
2. Look for your PostgreSQL server in the left panel
3. If not connected, right-click and select "Connect Server"
4. Enter password: `Sharath@1050`
5. ✅ **Verify**: Server connects successfully

### **Step 2: Check if 'dodo' Database Exists**
1. Expand your PostgreSQL server
2. Expand "Databases"
3. Look for a database named "dodo"

**If 'dodo' database EXISTS:**
- ✅ You're good to proceed
- Skip to Step 4

**If 'dodo' database DOES NOT EXIST:**
- ❌ You need to create it
- Continue to Step 3

### **Step 3: Create 'dodo' Database (if needed)**
1. Right-click on "Databases"
2. Select "Create" → "Database..."
3. Enter Database name: `dodo`
4. Owner: `postgres`
5. Click "Save"
6. ✅ **Verify**: 'dodo' database appears in the list

### **Step 4: Test Database Access**
1. Click on the "dodo" database
2. Right-click → "Query Tool"
3. Run this test query:
   ```sql
   SELECT version();
   ```
4. ✅ **Verify**: Query runs successfully and shows PostgreSQL version

### **Step 5: Check User Permissions**
1. In the Query Tool, run:
   ```sql
   SELECT current_user, current_database();
   ```
2. ✅ **Verify**: Shows `postgres` as user and `dodo` as database

### **Step 6: Test Table Creation (Optional)**
1. In the Query Tool, run:
   ```sql
   CREATE TABLE test_table (
       id SERIAL PRIMARY KEY,
       name VARCHAR(50)
   );
   
   INSERT INTO test_table (name) VALUES ('test');
   
   SELECT * FROM test_table;
   
   DROP TABLE test_table;
   ```
2. ✅ **Verify**: All commands execute successfully

## 🔧 **Common Issues & Solutions**

### **Issue 1: Server Won't Connect**
**Symptoms**: "Connection failed" or "Password authentication failed"

**Solutions**:
1. **Check PostgreSQL Service**:
   - Windows: Services → Look for "postgresql" service → Start if stopped
   
2. **Verify Password**:
   - Make sure password is exactly: `Sharath@1050`
   - Check for extra spaces or special characters
   
3. **Check Port**:
   - Default PostgreSQL port is 5432
   - In pgAdmin, verify server properties show port 5432

### **Issue 2: 'dodo' Database Missing**
**Solution**: Create it using Step 3 above

### **Issue 3: Permission Denied**
**Solution**: Make sure you're using the `postgres` superuser account

## 📊 **What Your Setup Should Look Like**

```
PostgreSQL Server (localhost:5432)
├── Databases
│   ├── postgres (default)
│   ├── dodo (your app database) ← THIS IS WHAT WE NEED
│   └── template0, template1 (system)
├── Login/Group Roles
│   ├── postgres (superuser) ← THIS IS YOUR USER
│   └── ...
└── Tablespaces
```

## ✅ **Verification Checklist**

Before proceeding with table creation, confirm:

- [ ] PostgreSQL server is running
- [ ] Can connect to server with password `Sharath@1050`
- [ ] Database `dodo` exists
- [ ] Can run queries in `dodo` database
- [ ] User `postgres` has full access to `dodo` database

## 🎯 **Next Steps After Verification**

Once you confirm all the above:

1. **If everything is correct**: We can proceed to create tables
2. **If 'dodo' database was missing**: Create it and then proceed
3. **If connection issues**: Fix connection first, then proceed

## 💡 **Pro Tips**

1. **Keep pgAdmin open** while we create tables - you can see them appear in real-time
2. **Refresh the database view** (F5) after creating tables to see them
3. **Use Query Tool** to run our table creation scripts directly in pgAdmin

## 🚨 **Important Notes**

- The database name `dodo` is what your application will connect to
- All Shark platform tables will be created in the `dodo` database
- Make sure you're connected to `dodo` database, not `postgres` database, when creating tables
