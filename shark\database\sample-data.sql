-- Shark Platform Sample Data
-- This file contains realistic test data for all tables

-- Clear existing data (optional - comment out if you want to keep existing data)
-- TRUNCATE TABLE booking_addons, order_item_addons, order_items, reviews, payments, bookings, orders, service_addons, service_variants, product_addons, product_variants, products, services, user_profiles, user_roles, vendor_availability, product_categories, service_categories, users RESTART IDENTITY CASCADE;

-- 1. Insert Users (Customers, Vendors, Admins)
INSERT INTO users (id, email, password_hash, first_name, last_name, phone, is_active, is_verified) VALUES
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'Admin', 'User', '+1234567890', true, true),
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'John', 'Doe', '+1234567891', true, true),
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'Jane', 'Smith', '+1234567892', true, true),
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'Mike', 'Johnson', '+1234567893', true, true),
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'Sarah', 'Wilson', '+1234567894', true, true),
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'Alex', 'Brown', '+1234567895', true, true);

-- 2. Insert User Roles
INSERT INTO user_roles (user_id, role) 
SELECT id, 'admin' FROM users WHERE email = '<EMAIL>';

INSERT INTO user_roles (user_id, role) 
SELECT id, 'customer' FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');

INSERT INTO user_roles (user_id, role) 
SELECT id, 'vendor' FROM users WHERE email IN ('<EMAIL>', '<EMAIL>');

INSERT INTO user_roles (user_id, role) 
SELECT id, 'customer' FROM users WHERE email IN ('<EMAIL>', '<EMAIL>');

-- 3. Insert User Profiles
INSERT INTO user_profiles (user_id, date_of_birth, gender, address_line1, city, state, postal_code, country, bio)
SELECT 
    u.id,
    CASE 
        WHEN u.email = '<EMAIL>' THEN '1990-05-15'::date
        WHEN u.email = '<EMAIL>' THEN '1988-08-22'::date
        WHEN u.email = '<EMAIL>' THEN '1985-03-10'::date
        WHEN u.email = '<EMAIL>' THEN '1992-11-05'::date
        WHEN u.email = '<EMAIL>' THEN '1995-07-18'::date
        ELSE NULL
    END,
    CASE 
        WHEN u.first_name IN ('John', 'Mike', 'Alex') THEN 'Male'
        WHEN u.first_name IN ('Jane', 'Sarah') THEN 'Female'
        ELSE 'Other'
    END,
    CASE 
        WHEN u.email = '<EMAIL>' THEN '123 Main St'
        WHEN u.email = '<EMAIL>' THEN '456 Oak Ave'
        WHEN u.email = '<EMAIL>' THEN '789 Pine Rd'
        WHEN u.email = '<EMAIL>' THEN '321 Elm St'
        WHEN u.email = '<EMAIL>' THEN '654 Maple Dr'
        ELSE '123 Default St'
    END,
    'San Francisco',
    'CA',
    '94102',
    'USA',
    CASE 
        WHEN u.email = '<EMAIL>' THEN 'Professional plumber with 10+ years experience'
        WHEN u.email = '<EMAIL>' THEN 'Certified electrician and home automation specialist'
        ELSE 'Shark platform user'
    END
FROM users u
WHERE u.email != '<EMAIL>';

-- 4. Insert Service Categories
INSERT INTO service_categories (id, name, description, icon_url, sort_order) VALUES
(uuid_generate_v4(), 'Home Maintenance', 'General home repair and maintenance services', '/icons/home-maintenance.svg', 1),
(uuid_generate_v4(), 'Plumbing', 'Professional plumbing services', '/icons/plumbing.svg', 2),
(uuid_generate_v4(), 'Electrical', 'Electrical installation and repair', '/icons/electrical.svg', 3),
(uuid_generate_v4(), 'Cleaning', 'Home and office cleaning services', '/icons/cleaning.svg', 4),
(uuid_generate_v4(), 'Landscaping', 'Garden and outdoor maintenance', '/icons/landscaping.svg', 5);

-- 5. Insert Product Categories
INSERT INTO product_categories (id, name, description, icon_url, sort_order) VALUES
(uuid_generate_v4(), 'Tools', 'Professional and DIY tools', '/icons/tools.svg', 1),
(uuid_generate_v4(), 'Hardware', 'Screws, bolts, and hardware supplies', '/icons/hardware.svg', 2),
(uuid_generate_v4(), 'Electrical Supplies', 'Wires, outlets, and electrical components', '/icons/electrical-supplies.svg', 3),
(uuid_generate_v4(), 'Plumbing Supplies', 'Pipes, fittings, and plumbing materials', '/icons/plumbing-supplies.svg', 4),
(uuid_generate_v4(), 'Safety Equipment', 'Safety gear and protective equipment', '/icons/safety.svg', 5);

-- 6. Insert Services
INSERT INTO services (vendor_id, category_id, name, description, price, price_type, duration, location_type)
SELECT 
    u.id,
    sc.id,
    CASE 
        WHEN u.email = '<EMAIL>' AND sc.name = 'Plumbing' THEN 'Emergency Plumbing Repair'
        WHEN u.email = '<EMAIL>' AND sc.name = 'Home Maintenance' THEN 'General Home Repairs'
        WHEN u.email = '<EMAIL>' AND sc.name = 'Electrical' THEN 'Electrical Installation'
        WHEN u.email = '<EMAIL>' AND sc.name = 'Home Maintenance' THEN 'Smart Home Setup'
    END,
    CASE 
        WHEN u.email = '<EMAIL>' AND sc.name = 'Plumbing' THEN 'Fast and reliable emergency plumbing services available 24/7'
        WHEN u.email = '<EMAIL>' AND sc.name = 'Home Maintenance' THEN 'Complete home repair and maintenance services'
        WHEN u.email = '<EMAIL>' AND sc.name = 'Electrical' THEN 'Professional electrical installation and wiring services'
        WHEN u.email = '<EMAIL>' AND sc.name = 'Home Maintenance' THEN 'Smart home automation and setup services'
    END,
    CASE 
        WHEN u.email = '<EMAIL>' AND sc.name = 'Plumbing' THEN 150.00
        WHEN u.email = '<EMAIL>' AND sc.name = 'Home Maintenance' THEN 75.00
        WHEN u.email = '<EMAIL>' AND sc.name = 'Electrical' THEN 120.00
        WHEN u.email = '<EMAIL>' AND sc.name = 'Home Maintenance' THEN 200.00
    END,
    'hourly',
    CASE 
        WHEN sc.name = 'Plumbing' THEN 120
        WHEN sc.name = 'Electrical' THEN 180
        ELSE 60
    END,
    'on_site'
FROM users u
CROSS JOIN service_categories sc
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
AND (
    (u.email = '<EMAIL>' AND sc.name IN ('Plumbing', 'Home Maintenance')) OR
    (u.email = '<EMAIL>' AND sc.name IN ('Electrical', 'Home Maintenance'))
);

-- 7. Insert Products
INSERT INTO products (vendor_id, category_id, name, description, price, sku, stock_quantity)
SELECT 
    u.id,
    pc.id,
    CASE 
        WHEN pc.name = 'Tools' THEN 'Professional Drill Set'
        WHEN pc.name = 'Hardware' THEN 'Assorted Screws Pack'
        WHEN pc.name = 'Electrical Supplies' THEN 'LED Light Bulbs (4-pack)'
        WHEN pc.name = 'Plumbing Supplies' THEN 'Copper Pipe Fittings'
        WHEN pc.name = 'Safety Equipment' THEN 'Safety Glasses'
    END,
    CASE 
        WHEN pc.name = 'Tools' THEN 'High-quality cordless drill with multiple bits and carrying case'
        WHEN pc.name = 'Hardware' THEN 'Assorted pack of screws for various home projects'
        WHEN pc.name = 'Electrical Supplies' THEN 'Energy-efficient LED bulbs, 60W equivalent'
        WHEN pc.name = 'Plumbing Supplies' THEN 'Professional grade copper pipe fittings set'
        WHEN pc.name = 'Safety Equipment' THEN 'ANSI certified safety glasses with anti-fog coating'
    END,
    CASE 
        WHEN pc.name = 'Tools' THEN 89.99
        WHEN pc.name = 'Hardware' THEN 12.99
        WHEN pc.name = 'Electrical Supplies' THEN 24.99
        WHEN pc.name = 'Plumbing Supplies' THEN 45.99
        WHEN pc.name = 'Safety Equipment' THEN 15.99
    END,
    CASE 
        WHEN pc.name = 'Tools' THEN 'DRILL-001'
        WHEN pc.name = 'Hardware' THEN 'SCREW-PACK-001'
        WHEN pc.name = 'Electrical Supplies' THEN 'LED-4PACK-001'
        WHEN pc.name = 'Plumbing Supplies' THEN 'COPPER-FIT-001'
        WHEN pc.name = 'Safety Equipment' THEN 'SAFETY-GLASS-001'
    END,
    CASE 
        WHEN pc.name = 'Tools' THEN 25
        WHEN pc.name = 'Hardware' THEN 100
        WHEN pc.name = 'Electrical Supplies' THEN 75
        WHEN pc.name = 'Plumbing Supplies' THEN 50
        WHEN pc.name = 'Safety Equipment' THEN 200
    END
FROM users u
CROSS JOIN product_categories pc
WHERE u.email = '<EMAIL>'
LIMIT 5;

-- 8. Insert Service Variants
INSERT INTO service_variants (service_id, name, description, price, price_type, duration, is_default)
SELECT 
    s.id,
    CASE 
        WHEN s.name = 'Emergency Plumbing Repair' THEN 'Standard Repair'
        WHEN s.name = 'Electrical Installation' THEN 'Basic Installation'
        ELSE 'Standard Service'
    END,
    CASE 
        WHEN s.name = 'Emergency Plumbing Repair' THEN 'Standard emergency plumbing repair service'
        WHEN s.name = 'Electrical Installation' THEN 'Basic electrical installation service'
        ELSE 'Standard service offering'
    END,
    s.price,
    s.price_type,
    s.duration,
    true
FROM services s;

-- 9. Insert Service Add-ons
INSERT INTO service_addons (service_id, name, description, price, price_type, duration, is_required)
SELECT 
    s.id,
    CASE 
        WHEN s.name LIKE '%Plumbing%' THEN 'Emergency Call-out Fee'
        WHEN s.name LIKE '%Electrical%' THEN 'Safety Inspection'
        ELSE 'Material Disposal'
    END,
    CASE 
        WHEN s.name LIKE '%Plumbing%' THEN 'Additional fee for emergency after-hours service'
        WHEN s.name LIKE '%Electrical%' THEN 'Comprehensive electrical safety inspection'
        ELSE 'Proper disposal of old materials and debris'
    END,
    CASE 
        WHEN s.name LIKE '%Plumbing%' THEN 50.00
        WHEN s.name LIKE '%Electrical%' THEN 75.00
        ELSE 25.00
    END,
    'fixed',
    30,
    false
FROM services s;

-- 10. Insert Vendor Availability
INSERT INTO vendor_availability (vendor_id, day_of_week, start_time, end_time)
SELECT 
    u.id,
    dow,
    '08:00'::time,
    '18:00'::time
FROM users u
CROSS JOIN generate_series(1, 5) dow  -- Monday to Friday
WHERE u.email IN ('<EMAIL>', '<EMAIL>');

-- 11. Insert Sample Bookings
INSERT INTO bookings (customer_id, vendor_id, service_id, booking_date, start_time, end_time, status, total_amount, notes)
SELECT 
    customer.id,
    vendor.id,
    s.id,
    CURRENT_DATE + INTERVAL '7 days',
    '10:00'::time,
    '12:00'::time,
    'confirmed',
    s.price * 2,  -- 2 hours
    'Sample booking for testing'
FROM users customer
CROSS JOIN users vendor
CROSS JOIN services s
WHERE customer.email = '<EMAIL>'
AND vendor.email = '<EMAIL>'
AND s.name = 'Emergency Plumbing Repair'
LIMIT 1;

-- 12. Insert Sample Orders
INSERT INTO orders (customer_id, order_number, status, subtotal, tax_amount, total_amount, shipping_address, billing_address)
SELECT 
    u.id,
    'ORD-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-001',
    'confirmed',
    89.99,
    7.20,
    97.19,
    '{"line1": "123 Main St", "city": "San Francisco", "state": "CA", "postal_code": "94102", "country": "USA"}'::jsonb,
    '{"line1": "123 Main St", "city": "San Francisco", "state": "CA", "postal_code": "94102", "country": "USA"}'::jsonb
FROM users u
WHERE u.email = '<EMAIL>'
LIMIT 1;

-- 13. Insert Order Items
INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
SELECT 
    o.id,
    p.id,
    1,
    p.price,
    p.price
FROM orders o
CROSS JOIN products p
WHERE o.order_number LIKE 'ORD-%'
AND p.name = 'Professional Drill Set'
LIMIT 1;

-- 14. Insert Sample Payments
INSERT INTO payments (booking_id, customer_id, amount, currency, payment_method, payment_status, transaction_id)
SELECT 
    b.id,
    b.customer_id,
    b.total_amount,
    'USD',
    'credit_card',
    'completed',
    'txn_' || EXTRACT(EPOCH FROM NOW())::text
FROM bookings b
LIMIT 1;

-- 15. Insert Sample Reviews
INSERT INTO reviews (booking_id, reviewer_id, vendor_id, rating, title, comment, is_verified)
SELECT 
    b.id,
    b.customer_id,
    b.vendor_id,
    5,
    'Excellent Service!',
    'Mike did an amazing job fixing our plumbing issue. Very professional and quick!',
    true
FROM bookings b
LIMIT 1;

-- 16. Insert Sample Notifications
INSERT INTO notifications (user_id, type, title, message, data)
SELECT 
    u.id,
    'booking_confirmed',
    'Booking Confirmed',
    'Your booking has been confirmed for ' || TO_CHAR(CURRENT_DATE + INTERVAL '7 days', 'Mon DD, YYYY'),
    '{"booking_id": "sample", "service": "Emergency Plumbing Repair"}'::jsonb
FROM users u
WHERE u.email = '<EMAIL>'
LIMIT 1;
