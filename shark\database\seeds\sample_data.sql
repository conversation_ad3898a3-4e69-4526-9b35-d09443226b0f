-- Sample data for Shark Home Services Platform
-- This file contains sample data for development and testing

-- Insert sample service categories
INSERT INTO service_categories (id, name, description, icon, parent_id, is_active, sort_order) VALUES
('550e8400-e29b-41d4-a716-************', 'Cleaning Services', 'Professional cleaning services for homes and offices', '🧹', NULL, true, 1),
('550e8400-e29b-41d4-a716-************', 'Repair & Maintenance', 'Home repair and maintenance services', '🔧', NULL, true, 2),
('550e8400-e29b-41d4-a716-************', 'Beauty & Wellness', 'Personal care and wellness services', '💄', NULL, true, 3),
('550e8400-e29b-41d4-a716-************', 'Fitness & Health', 'Fitness training and health services', '💪', NULL, true, 4);

-- Insert subcategories for cleaning services
INSERT INTO service_categories (id, name, description, icon, parent_id, is_active, sort_order) VALUES
('550e8400-e29b-41d4-a716-************', 'House Cleaning', 'Regular house cleaning services', '🏠', '550e8400-e29b-41d4-a716-************', true, 1),
('550e8400-e29b-41d4-a716-************', 'Deep Cleaning', 'Thorough deep cleaning services', '🧽', '550e8400-e29b-41d4-a716-************', true, 2),
('550e8400-e29b-41d4-a716-446655440013', 'Office Cleaning', 'Commercial office cleaning', '🏢', '550e8400-e29b-41d4-a716-************', true, 3);

-- Insert subcategories for repair & maintenance
INSERT INTO service_categories (id, name, description, icon, parent_id, is_active, sort_order) VALUES
('550e8400-e29b-41d4-a716-************', 'Plumbing', 'Plumbing repair and installation', '🚰', '550e8400-e29b-41d4-a716-************', true, 1),
('550e8400-e29b-41d4-a716-446655440022', 'Electrical', 'Electrical repair and installation', '⚡', '550e8400-e29b-41d4-a716-************', true, 2),
('550e8400-e29b-41d4-a716-446655440023', 'Carpentry', 'Wood work and furniture repair', '🪚', '550e8400-e29b-41d4-a716-************', true, 3);

-- Insert sample product categories
INSERT INTO product_categories (id, name, description, icon, parent_id, is_active, sort_order) VALUES
('660e8400-e29b-41d4-a716-************', 'Tools & Equipment', 'Professional tools and equipment', '🔨', NULL, true, 1),
('660e8400-e29b-41d4-a716-************', 'Cleaning Supplies', 'Cleaning products and supplies', '🧽', NULL, true, 2),
('660e8400-e29b-41d4-a716-************', 'Construction Materials', 'Building and construction materials', '🧱', NULL, true, 3),
('660e8400-e29b-41d4-a716-************', 'Electronics', 'Electronic devices and accessories', '📱', NULL, true, 4);

-- Insert sample users
INSERT INTO users (id, email, password_hash, first_name, last_name, phone, is_active, is_verified) VALUES
('770e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$example_hash_admin', 'Admin', 'User', '+91-9876543210', true, true),
('770e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$example_hash_john', 'John', 'Doe', '+91-9876543211', true, true),
('770e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$example_hash_jane', 'Jane', 'Smith', '+91-9876543212', true, true),
('770e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$example_hash_mike', 'Mike', 'Johnson', '+91-9876543213', true, true),
('770e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$example_hash_sarah', 'Sarah', 'Wilson', '+91-9876543214', true, true);

-- Insert user roles
INSERT INTO user_roles (user_id, role, is_active) VALUES
('770e8400-e29b-41d4-a716-************', 'admin', true),
('770e8400-e29b-41d4-a716-************', 'customer', true),
('770e8400-e29b-41d4-a716-************', 'vendor', true),
('770e8400-e29b-41d4-a716-************', 'customer', true), -- Jane is both vendor and customer
('770e8400-e29b-41d4-a716-************', 'vendor', true),
('770e8400-e29b-41d4-a716-************', 'customer', true), -- Mike is both vendor and customer
('770e8400-e29b-41d4-a716-************', 'vendor', true);

-- Insert user profiles
INSERT INTO user_profiles (user_id, bio, address, kyc_status) VALUES
('770e8400-e29b-41d4-a716-************', 'Platform administrator', '{"street": "123 Admin St", "city": "Mumbai", "state": "Maharashtra", "country": "India", "zipCode": "400001"}', 'verified'),
('770e8400-e29b-41d4-a716-************', 'Regular customer looking for home services', '{"street": "456 Customer Ave", "city": "Mumbai", "state": "Maharashtra", "country": "India", "zipCode": "400002"}', 'verified'),
('770e8400-e29b-41d4-a716-************', 'Professional service provider with 5 years experience', '{"street": "789 Vendor Rd", "city": "Mumbai", "state": "Maharashtra", "country": "India", "zipCode": "400003"}', 'verified'),
('770e8400-e29b-41d4-a716-************', 'Expert cleaner with eco-friendly solutions', '{"street": "321 Clean St", "city": "Mumbai", "state": "Maharashtra", "country": "India", "zipCode": "400004"}', 'verified'),
('770e8400-e29b-41d4-a716-************', 'Licensed plumber with 10+ years experience', '{"street": "654 Pipe Lane", "city": "Mumbai", "state": "Maharashtra", "country": "India", "zipCode": "400005"}', 'verified');

-- Insert sample services
INSERT INTO services (id, vendor_id, category_id, name, description, price, price_type, duration, tags, is_active) VALUES
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Regular House Cleaning', 'Complete house cleaning including all rooms, kitchen, and bathrooms', 1500.00, 'fixed', 180, ARRAY['cleaning', 'house', 'regular'], true),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Deep Cleaning Service', 'Thorough deep cleaning with specialized equipment', 3000.00, 'fixed', 300, ARRAY['cleaning', 'deep', 'thorough'], true),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Plumbing Repair', 'General plumbing repairs and maintenance', 800.00, 'hourly', 60, ARRAY['plumbing', 'repair', 'maintenance'], true),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Pipe Installation', 'New pipe installation and replacement', 1200.00, 'custom', 120, ARRAY['plumbing', 'installation', 'pipes'], true);

-- Insert sample products
INSERT INTO products (id, vendor_id, category_id, name, description, price, stock, specifications, is_active) VALUES
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Eco-Friendly All-Purpose Cleaner', 'Biodegradable cleaning solution safe for all surfaces', 299.00, 50, '{"volume": "500ml", "type": "liquid", "eco_friendly": true}', true),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Professional Cleaning Kit', 'Complete cleaning kit with brushes, cloths, and solutions', 1299.00, 25, '{"items": 15, "case_included": true, "professional_grade": true}', true),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Pipe Wrench Set', 'Professional pipe wrench set for plumbing work', 899.00, 15, '{"sizes": ["10inch", "12inch", "14inch"], "material": "steel", "adjustable": true}', true);

-- Insert vendor availability (sample for Mike - cleaner)
INSERT INTO vendor_availability (vendor_id, day_of_week, start_time, end_time, is_available) VALUES
('770e8400-e29b-41d4-a716-************', 1, '09:00', '18:00', true), -- Monday
('770e8400-e29b-41d4-a716-************', 2, '09:00', '18:00', true), -- Tuesday
('770e8400-e29b-41d4-a716-************', 3, '09:00', '18:00', true), -- Wednesday
('770e8400-e29b-41d4-a716-************', 4, '09:00', '18:00', true), -- Thursday
('770e8400-e29b-41d4-a716-************', 5, '09:00', '18:00', true), -- Friday
('770e8400-e29b-41d4-a716-************', 6, '10:00', '16:00', true); -- Saturday

-- Insert vendor availability (sample for Sarah - plumber)
INSERT INTO vendor_availability (vendor_id, day_of_week, start_time, end_time, is_available) VALUES
('770e8400-e29b-41d4-a716-************', 1, '08:00', '17:00', true), -- Monday
('770e8400-e29b-41d4-a716-************', 2, '08:00', '17:00', true), -- Tuesday
('770e8400-e29b-41d4-a716-************', 3, '08:00', '17:00', true), -- Wednesday
('770e8400-e29b-41d4-a716-************', 4, '08:00', '17:00', true), -- Thursday
('770e8400-e29b-41d4-a716-************', 5, '08:00', '17:00', true), -- Friday
('770e8400-e29b-41d4-a716-************', 6, '09:00', '15:00', true); -- Saturday

-- Insert sample bookings
INSERT INTO bookings (id, customer_id, vendor_id, service_id, scheduled_at, duration, total_amount, status, address, notes) VALUES
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '2024-01-15 10:00:00', 180, 1500.00, 'confirmed', '{"street": "456 Customer Ave", "city": "Mumbai", "state": "Maharashtra", "country": "India", "zipCode": "400002"}', 'Please bring eco-friendly products'),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '2024-01-16 14:00:00', 60, 800.00, 'pending', '{"street": "789 Vendor Rd", "city": "Mumbai", "state": "Maharashtra", "country": "India", "zipCode": "400003"}', 'Kitchen sink is leaking');

-- Insert sample orders
INSERT INTO orders (id, customer_id, total_amount, status, shipping_address, payment_method) VALUES
('bb0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 598.00, 'confirmed', '{"street": "456 Customer Ave", "city": "Mumbai", "state": "Maharashtra", "country": "India", "zipCode": "400002"}', 'upi');

-- Insert order items
INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) VALUES
('bb0e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 2, 299.00, 598.00);

-- Insert sample payments
INSERT INTO payments (id, user_id, booking_id, amount, currency, method, status, transaction_id) VALUES
('cc0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'aa0e8400-e29b-41d4-a716-************', 1500.00, 'INR', 'upi', 'completed', 'TXN123456789'),
('cc0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', NULL, 598.00, 'INR', 'upi', 'completed', 'TXN123456790');

-- Update the payment with order_id
UPDATE payments SET order_id = 'bb0e8400-e29b-41d4-a716-************' WHERE id = 'cc0e8400-e29b-41d4-a716-************';

-- Insert sample reviews
INSERT INTO reviews (id, user_id, service_id, booking_id, rating, comment) VALUES
('dd0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'aa0e8400-e29b-41d4-a716-************', 5, 'Excellent service! Very thorough and professional cleaning.');

INSERT INTO reviews (id, user_id, product_id, order_id, rating, comment) VALUES
('dd0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 'bb0e8400-e29b-41d4-a716-************', 4, 'Good quality cleaner, works well on all surfaces.');

-- Insert sample notifications
INSERT INTO notifications (user_id, title, message, type, is_read) VALUES
('770e8400-e29b-41d4-a716-************', 'Booking Confirmed', 'Your house cleaning service has been confirmed for Jan 15, 2024 at 10:00 AM', 'booking_confirmed', false),
('770e8400-e29b-41d4-a716-************', 'New Booking Request', 'You have a new booking request for house cleaning service', 'booking_request', false),
('770e8400-e29b-41d4-a716-************', 'Order Shipped', 'Your order #bb0e8400 has been shipped and will arrive in 2-3 days', 'order_shipped', false);
