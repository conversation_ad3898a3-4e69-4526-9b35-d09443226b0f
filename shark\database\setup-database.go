package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🗄️  SHARK PLATFORM DATABASE SETUP")
	fmt.Println("==================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Host: %s:%s\n", dbHost, dbPort)
	fmt.Printf("User: %s\n", dbUser)
	fmt.Printf("Target Database: %s\n", dbName)

	// Step 1: Connect to default 'postgres' database to check/create target database
	fmt.Println("\n📡 Step 1: Connecting to PostgreSQL server...")
	
	defaultDSN := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=postgres sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbSSLMode)

	db, err := sql.Open("postgres", defaultDSN)
	if err != nil {
		log.Fatal("❌ Failed to connect to PostgreSQL server:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("❌ Failed to ping PostgreSQL server:", err)
	}

	fmt.Println("✅ Connected to PostgreSQL server successfully!")

	// Step 2: Check if target database exists
	fmt.Printf("\n🔍 Step 2: Checking if database '%s' exists...\n", dbName)
	
	var exists bool
	err = db.QueryRow("SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = $1)", dbName).Scan(&exists)
	if err != nil {
		log.Fatal("❌ Failed to check database existence:", err)
	}

	if exists {
		fmt.Printf("✅ Database '%s' already exists\n", dbName)
	} else {
		fmt.Printf("📝 Database '%s' does not exist, creating it...\n", dbName)
		
		// Create the database
		_, err = db.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName))
		if err != nil {
			log.Fatal("❌ Failed to create database:", err)
		}
		
		fmt.Printf("✅ Database '%s' created successfully!\n", dbName)
	}

	// Close connection to default database
	db.Close()

	// Step 3: Connect to target database
	fmt.Printf("\n🔌 Step 3: Connecting to database '%s'...\n", dbName)
	
	targetDSN := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	targetDB, err := sql.Open("postgres", targetDSN)
	if err != nil {
		log.Fatal("❌ Failed to connect to target database:", err)
	}
	defer targetDB.Close()

	if err := targetDB.Ping(); err != nil {
		log.Fatal("❌ Failed to ping target database:", err)
	}

	fmt.Printf("✅ Connected to database '%s' successfully!\n", dbName)

	// Step 4: Create tables
	fmt.Println("\n📋 Step 4: Creating tables...")
	
	if err := createTables(targetDB); err != nil {
		log.Fatal("❌ Failed to create tables:", err)
	}

	// Step 5: Verify tables
	fmt.Println("\n✅ Step 5: Verifying created tables...")
	
	if err := verifyTables(targetDB); err != nil {
		log.Fatal("❌ Failed to verify tables:", err)
	}

	fmt.Println("\n🎉 DATABASE SETUP COMPLETE!")
	fmt.Println("==========================================")
	fmt.Printf("✅ Database '%s' is ready for the Shark platform\n", dbName)
	fmt.Println("✅ All tables created successfully")
	fmt.Println("✅ Your microservices can now connect to the database")
}

func createTables(db *sql.DB) error {
	// Read SQL file
	sqlFile := "create-all-tables.sql"
	content, err := ioutil.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("failed to read SQL file %s: %w", sqlFile, err)
	}

	sqlContent := string(content)
	
	// Split by statements and execute
	statements := strings.Split(sqlContent, ";")
	
	successCount := 0
	errorCount := 0

	for i, statement := range statements {
		statement = strings.TrimSpace(statement)
		if statement == "" || strings.HasPrefix(statement, "--") {
			continue
		}

		fmt.Printf("  Executing statement %d...", i+1)

		_, err := db.Exec(statement)
		if err != nil {
			fmt.Printf(" ❌ FAILED\n")
			fmt.Printf("    Error: %v\n", err)
			errorCount++
		} else {
			fmt.Printf(" ✅ SUCCESS\n")
			successCount++
		}
	}

	fmt.Printf("\n📊 Results: %d successful, %d failed\n", successCount, errorCount)

	if errorCount > 0 {
		return fmt.Errorf("%d statements failed", errorCount)
	}

	return nil
}

func verifyTables(db *sql.DB) error {
	query := `
		SELECT table_name 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		ORDER BY table_name`

	rows, err := db.Query(query)
	if err != nil {
		return fmt.Errorf("failed to query tables: %w", err)
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}
		tables = append(tables, tableName)
	}

	fmt.Printf("📊 Created %d tables:\n", len(tables))
	for _, table := range tables {
		fmt.Printf("  ✅ %s\n", table)
	}

	// Expected tables for Shark platform
	expectedTables := []string{
		"users", "user_roles", "user_profiles",
		"service_categories", "services", "service_variants", "service_addons",
		"product_categories", "products", "product_variants", "product_addons",
		"bookings", "booking_addons", "vendor_availability",
		"orders", "order_items", "order_item_addons",
		"payments", "reviews", "notifications",
	}

	fmt.Printf("\n🔍 Checking for expected tables:\n")
	for _, expected := range expectedTables {
		found := false
		for _, actual := range tables {
			if actual == expected {
				found = true
				break
			}
		}
		if found {
			fmt.Printf("  ✅ %s\n", expected)
		} else {
			fmt.Printf("  ❌ %s (missing)\n", expected)
		}
	}

	return nil
}

func loadDatabaseConfig() {
	configFile := "../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
