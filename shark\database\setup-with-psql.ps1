# Shark Platform Database Setup using psql
Write-Host "🗄️  SHARK PLATFORM DATABASE SETUP" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor White

# Load database configuration
Write-Host "`n📋 Loading database configuration..." -ForegroundColor Yellow

$configFile = "..\config\database.env"
if (Test-Path $configFile) {
    Get-Content $configFile | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
        $parts = $_ -split "=", 2
        if ($parts.Length -eq 2) {
            $key = $parts[0].Trim()
            $value = $parts[1].Trim()
            [Environment]::SetEnvironmentVariable($key, $value, "Process")
        }
    }
    Write-Host "✅ Configuration loaded" -ForegroundColor Green
} else {
    Write-Host "❌ Configuration file not found: $configFile" -ForegroundColor Red
    exit 1
}

# Get database settings
$DB_HOST = $env:DB_HOST
$DB_PORT = $env:DB_PORT
$DB_USER = $env:DB_USER
$DB_PASSWORD = $env:DB_PASSWORD
$DB_NAME = $env:DB_NAME

Write-Host "`n📊 Database Settings:" -ForegroundColor Cyan
Write-Host "Host: $DB_HOST" -ForegroundColor White
Write-Host "Port: $DB_PORT" -ForegroundColor White
Write-Host "User: $DB_USER" -ForegroundColor White
Write-Host "Database: $DB_NAME" -ForegroundColor White

# Set PGPASSWORD environment variable for psql
$env:PGPASSWORD = $DB_PASSWORD

Write-Host "`n🔌 Step 1: Testing PostgreSQL connection..." -ForegroundColor Yellow

# Test connection to default postgres database
try {
    $testResult = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "SELECT version();" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ PostgreSQL connection successful!" -ForegroundColor Green
    } else {
        Write-Host "❌ PostgreSQL connection failed!" -ForegroundColor Red
        Write-Host "Error: $testResult" -ForegroundColor Red
        Write-Host "`n💡 Troubleshooting:" -ForegroundColor Yellow
        Write-Host "1. Make sure PostgreSQL is running" -ForegroundColor White
        Write-Host "2. Check if psql is installed and in PATH" -ForegroundColor White
        Write-Host "3. Verify password: $DB_PASSWORD" -ForegroundColor White
        Write-Host "4. Try connecting manually: psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres" -ForegroundColor White
        exit 1
    }
} catch {
    Write-Host "❌ psql command not found!" -ForegroundColor Red
    Write-Host "Please install PostgreSQL client tools or use pgAdmin instead." -ForegroundColor Yellow
    exit 1
}

Write-Host "`n🔍 Step 2: Checking if database '$DB_NAME' exists..." -ForegroundColor Yellow

# Check if database exists
$checkDB = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -t -c "SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = '$DB_NAME');" 2>&1

if ($checkDB -match "t") {
    Write-Host "✅ Database '$DB_NAME' already exists" -ForegroundColor Green
} else {
    Write-Host "📝 Creating database '$DB_NAME'..." -ForegroundColor Yellow
    
    $createResult = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME;" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database '$DB_NAME' created successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create database!" -ForegroundColor Red
        Write-Host "Error: $createResult" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n📋 Step 3: Creating tables..." -ForegroundColor Yellow

# Execute the SQL file
if (Test-Path "create-all-tables.sql") {
    Write-Host "📄 Executing create-all-tables.sql..." -ForegroundColor Cyan
    
    $sqlResult = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f "create-all-tables.sql" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Tables created successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create tables!" -ForegroundColor Red
        Write-Host "Error: $sqlResult" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ SQL file 'create-all-tables.sql' not found!" -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ Step 4: Verifying tables..." -ForegroundColor Yellow

# List created tables
$tables = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;" 2>&1

if ($LASTEXITCODE -eq 0) {
    $tableList = $tables | Where-Object { $_.Trim() -ne "" }
    Write-Host "📊 Created $($tableList.Count) tables:" -ForegroundColor Cyan
    foreach ($table in $tableList) {
        Write-Host "  ✅ $($table.Trim())" -ForegroundColor Green
    }
} else {
    Write-Host "⚠️  Could not verify tables" -ForegroundColor Yellow
}

Write-Host "`n🎉 DATABASE SETUP COMPLETE!" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor White
Write-Host "✅ Database '$DB_NAME' is ready" -ForegroundColor Green
Write-Host "✅ All tables created successfully" -ForegroundColor Green
Write-Host "✅ Your Shark platform is ready to use!" -ForegroundColor Green

Write-Host "`n🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Test your microservices connection" -ForegroundColor White
Write-Host "2. Run your application" -ForegroundColor White
Write-Host "3. Check tables in pgAdmin if needed" -ForegroundColor White

# Clean up password from environment
$env:PGPASSWORD = $null
