# Test PostgreSQL Authentication
Write-Host "TESTING POSTGRESQL AUTHENTICATION" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor White

Write-Host "`nPostgreSQL is running on port 5432 ✅" -ForegroundColor Green
Write-Host "Testing different authentication methods..." -ForegroundColor Yellow

# Test scenarios
$testCases = @(
    @{
        Name = "Your Config (postgres/Sharath@1050)"
        User = "postgres"
        Password = "Sharath@1050"
        Database = "postgres"
    },
    @{
        Name = "Default postgres (no password)"
        User = "postgres"
        Password = ""
        Database = "postgres"
    },
    @{
        Name = "Windows Authentication"
        User = $env:USERNAME
        Password = ""
        Database = "postgres"
    }
)

foreach ($test in $testCases) {
    Write-Host "`nTesting: $($test.Name)" -ForegroundColor Cyan
    Write-Host "User: $($test.User)" -ForegroundColor White
    Write-Host "Password: $(if($test.Password) { '*' * $test.Password.Length } else { '(none)' })" -ForegroundColor White
    
    # Set password environment variable
    if ($test.Password) {
        $env:PGPASSWORD = $test.Password
    } else {
        $env:PGPASSWORD = $null
    }
    
    # Try to connect
    try {
        $result = & psql -h localhost -p 5432 -U $($test.User) -d $($test.Database) -c "SELECT version();" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ SUCCESS! This authentication works." -ForegroundColor Green
            Write-Host "Use these credentials in your config." -ForegroundColor Green
            break
        } else {
            Write-Host "❌ Failed: $result" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ psql command not found or failed" -ForegroundColor Red
    }
}

# Clean up
$env:PGPASSWORD = $null

Write-Host "`n" + "="*50 -ForegroundColor White
Write-Host "AUTHENTICATION TROUBLESHOOTING" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor White

Write-Host "`nIf all tests failed, try these solutions:" -ForegroundColor Yellow
Write-Host "`n1. RESET POSTGRES PASSWORD:" -ForegroundColor Cyan
Write-Host "   - Open Command Prompt as Administrator" -ForegroundColor White
Write-Host "   - Run: net stop postgresql-x64-15 (or your version)" -ForegroundColor White
Write-Host "   - Start PostgreSQL in single-user mode" -ForegroundColor White
Write-Host "   - Reset password" -ForegroundColor White

Write-Host "`n2. CHECK PGADMIN CONNECTION:" -ForegroundColor Cyan
Write-Host "   - Open pgAdmin" -ForegroundColor White
Write-Host "   - Right-click server -> Properties -> Connection" -ForegroundColor White
Write-Host "   - Verify host, port, username" -ForegroundColor White
Write-Host "   - Try 'Save Password' option" -ForegroundColor White

Write-Host "`n3. TRY DIFFERENT AUTHENTICATION:" -ForegroundColor Cyan
Write-Host "   - User: postgres, Password: (empty)" -ForegroundColor White
Write-Host "   - User: postgres, Password: postgres" -ForegroundColor White
Write-Host "   - User: $env:USERNAME, Password: (empty)" -ForegroundColor White

Write-Host "`n4. CHECK POSTGRESQL CONFIG:" -ForegroundColor Cyan
Write-Host "   - Find pg_hba.conf file" -ForegroundColor White
Write-Host "   - Look for 'local all postgres' line" -ForegroundColor White
Write-Host "   - Should be 'trust' or 'md5'" -ForegroundColor White

Write-Host "`nNEXT STEP: Try connecting in pgAdmin first!" -ForegroundColor Green
