package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🔌 TESTING DATABASE CONNECTION")
	fmt.Println("==============================")

	// Load database configuration from config/database.env
	loadDatabaseConfig()

	// Show what we're trying to connect to
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Host: %s\n", dbHost)
	fmt.Printf("Port: %s\n", dbPort)
	fmt.Printf("User: %s\n", dbUser)
	fmt.Printf("Database: %s\n", dbName)
	fmt.Printf("SSL Mode: %s\n", dbSSLMode)
	fmt.Printf("Password: %s\n", maskPassword(dbPassword))

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	fmt.Println("\n🔌 Attempting to connect...")

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("Failed to open database connection:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		fmt.Printf("❌ Connection failed: %v\n", err)
		fmt.Println("\n💡 Troubleshooting tips:")
		fmt.Println("1. Check if PostgreSQL is running")
		fmt.Println("2. Verify the password is correct")
		fmt.Println("3. Ensure the database 'dodo' exists")
		fmt.Println("4. Check if user 'postgres' has access")
		return
	}

	fmt.Println("✅ Database connection successful!")

	// Test a simple query
	var version string
	err = db.QueryRow("SELECT version()").Scan(&version)
	if err != nil {
		fmt.Printf("❌ Failed to query database: %v\n", err)
		return
	}

	fmt.Printf("📊 PostgreSQL Version: %s\n", version[:50]+"...")

	// Check if database exists and is accessible
	var dbExists bool
	err = db.QueryRow("SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = $1)", dbName).Scan(&dbExists)
	if err != nil {
		fmt.Printf("❌ Failed to check database existence: %v\n", err)
		return
	}

	if dbExists {
		fmt.Printf("✅ Database '%s' exists and is accessible\n", dbName)
	} else {
		fmt.Printf("❌ Database '%s' does not exist\n", dbName)
		return
	}

	// List existing tables
	fmt.Println("\n📋 Existing tables:")
	rows, err := db.Query(`
		SELECT table_name 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		ORDER BY table_name`)
	
	if err != nil {
		fmt.Printf("❌ Failed to list tables: %v\n", err)
		return
	}
	defer rows.Close()

	tableCount := 0
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}
		fmt.Printf("  - %s\n", tableName)
		tableCount++
	}

	if tableCount == 0 {
		fmt.Println("  (No tables found - database is empty)")
	} else {
		fmt.Printf("  Found %d existing tables\n", tableCount)
	}

	fmt.Println("\n🎉 Database connection test completed successfully!")
	fmt.Println("Ready to create tables!")
}

func loadDatabaseConfig() {
	configFile := "../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		fmt.Printf("⚠️  Config file not found: %s\n", configFile)
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("⚠️  Failed to read config file: %v\n", err)
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	fmt.Println("✅ Database configuration loaded")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func maskPassword(password string) string {
	if len(password) == 0 {
		return "(empty)"
	}
	if len(password) <= 3 {
		return strings.Repeat("*", len(password))
	}
	return password[:2] + strings.Repeat("*", len(password)-2)
}
