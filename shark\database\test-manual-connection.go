package main

import (
	"database/sql"
	"fmt"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🔌 MANUAL DATABASE CONNECTION TEST")
	fmt.Println("==================================")

	// Test different connection scenarios
	testConfigs := []struct {
		name     string
		host     string
		port     string
		user     string
		password string
		dbname   string
		sslmode  string
	}{
		{
			name:     "Config File Settings",
			host:     "localhost",
			port:     "5432",
			user:     "postgres",
			password: "Sharath@1050",
			dbname:   "dodo",
			sslmode:  "disable",
		},
		{
			name:     "Default PostgreSQL",
			host:     "localhost",
			port:     "5432",
			user:     "postgres",
			password: "Sharath@1050",
			dbname:   "postgres", // Default database
			sslmode:  "disable",
		},
		{
			name:     "Without Password",
			host:     "localhost",
			port:     "5432",
			user:     "postgres",
			password: "",
			dbname:   "postgres",
			sslmode:  "disable",
		},
	}

	for i, config := range testConfigs {
		fmt.Printf("\n%d. Testing: %s\n", i+1, config.name)
		fmt.Printf("   Host: %s:%s\n", config.host, config.port)
		fmt.Printf("   User: %s\n", config.user)
		fmt.Printf("   Database: %s\n", config.dbname)
		fmt.Printf("   Password: %s\n", maskPassword(config.password))

		dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
			config.host, config.port, config.user, config.password, config.dbname, config.sslmode)

		db, err := sql.Open("postgres", dsn)
		if err != nil {
			fmt.Printf("   ❌ Failed to open connection: %v\n", err)
			continue
		}

		err = db.Ping()
		if err != nil {
			fmt.Printf("   ❌ Connection failed: %v\n", err)
			db.Close()
			continue
		}

		fmt.Printf("   ✅ Connection successful!\n")

		// Test a simple query
		var version string
		err = db.QueryRow("SELECT version()").Scan(&version)
		if err != nil {
			fmt.Printf("   ❌ Query failed: %v\n", err)
		} else {
			fmt.Printf("   📊 PostgreSQL: %s\n", version[:30]+"...")
		}

		// Check if 'dodo' database exists
		if config.dbname == "postgres" {
			var exists bool
			err = db.QueryRow("SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = 'dodo')").Scan(&exists)
			if err != nil {
				fmt.Printf("   ❌ Failed to check 'dodo' database: %v\n", err)
			} else if exists {
				fmt.Printf("   ✅ 'dodo' database exists\n")
			} else {
				fmt.Printf("   ⚠️  'dodo' database does not exist\n")
				fmt.Printf("   💡 You may need to create it: CREATE DATABASE dodo;\n")
			}
		}

		db.Close()

		// If this connection worked, we can proceed
		if err == nil {
			fmt.Printf("\n🎉 Found working connection: %s\n", config.name)
			fmt.Println("You can use these settings to create tables.")
			break
		}
	}

	fmt.Println("\n📋 TROUBLESHOOTING GUIDE:")
	fmt.Println("1. Make sure PostgreSQL is running:")
	fmt.Println("   - Windows: Check Services for 'postgresql' service")
	fmt.Println("   - Or check if port 5432 is listening")
	fmt.Println("")
	fmt.Println("2. Verify your PostgreSQL installation:")
	fmt.Println("   - Try connecting with pgAdmin or psql command line")
	fmt.Println("   - psql -h localhost -U postgres -d postgres")
	fmt.Println("")
	fmt.Println("3. Check if 'dodo' database exists:")
	fmt.Println("   - Connect to default 'postgres' database first")
	fmt.Println("   - Then run: CREATE DATABASE dodo;")
	fmt.Println("")
	fmt.Println("4. Password issues:")
	fmt.Println("   - Make sure the password 'Sharath@1050' is correct")
	fmt.Println("   - Try connecting without password (if trust auth is enabled)")
}

func maskPassword(password string) string {
	if len(password) == 0 {
		return "(empty)"
	}
	if len(password) <= 3 {
		return strings.Repeat("*", len(password))
	}
	return password[:2] + strings.Repeat("*", len(password)-2)
}
