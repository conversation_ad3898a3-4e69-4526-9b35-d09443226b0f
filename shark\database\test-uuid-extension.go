package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🧪 TESTING UUID EXTENSION SUPPORT")
	fmt.Println("=================================")

	// Load database configuration
	loadDatabaseConfig()

	// Get connection details
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "dodo")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	fmt.Printf("Connecting to: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// Connect to database
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("❌ Failed to ping database:", err)
	}

	fmt.Println("✅ Database connection successful!")

	// Test 1: Check PostgreSQL version
	fmt.Println("\n📊 Step 1: Checking PostgreSQL version...")
	var version string
	err = db.QueryRow("SELECT version()").Scan(&version)
	if err != nil {
		log.Fatal("❌ Failed to get version:", err)
	}
	fmt.Printf("✅ PostgreSQL Version: %s\n", version[:50]+"...")

	// Test 2: Check available extensions
	fmt.Println("\n📋 Step 2: Checking available extensions...")
	rows, err := db.Query("SELECT name FROM pg_available_extensions WHERE name LIKE '%uuid%' ORDER BY name")
	if err != nil {
		log.Fatal("❌ Failed to query extensions:", err)
	}
	defer rows.Close()

	var availableExtensions []string
	for rows.Next() {
		var extName string
		if err := rows.Scan(&extName); err != nil {
			continue
		}
		availableExtensions = append(availableExtensions, extName)
	}

	if len(availableExtensions) > 0 {
		fmt.Println("✅ Available UUID extensions:")
		for _, ext := range availableExtensions {
			fmt.Printf("  - %s\n", ext)
		}
	} else {
		fmt.Println("❌ No UUID extensions available")
	}

	// Test 3: Check installed extensions
	fmt.Println("\n🔍 Step 3: Checking installed extensions...")
	rows2, err := db.Query("SELECT extname FROM pg_extension ORDER BY extname")
	if err != nil {
		log.Fatal("❌ Failed to query installed extensions:", err)
	}
	defer rows2.Close()

	var installedExtensions []string
	for rows2.Next() {
		var extName string
		if err := rows2.Scan(&extName); err != nil {
			continue
		}
		installedExtensions = append(installedExtensions, extName)
	}

	fmt.Printf("📊 Found %d installed extensions:\n", len(installedExtensions))
	for _, ext := range installedExtensions {
		fmt.Printf("  - %s\n", ext)
	}

	// Test 4: Try to create UUID extension
	fmt.Println("\n🔧 Step 4: Testing UUID extension creation...")
	_, err = db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")
	if err != nil {
		fmt.Printf("❌ Failed to create uuid-ossp extension: %v\n", err)
		
		// Try alternative
		fmt.Println("🔄 Trying alternative uuid extension...")
		_, err = db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-generate\"")
		if err != nil {
			fmt.Printf("❌ Failed to create uuid-generate extension: %v\n", err)
		} else {
			fmt.Println("✅ uuid-generate extension created successfully!")
		}
	} else {
		fmt.Println("✅ uuid-ossp extension created successfully!")
	}

	// Test 5: Test UUID generation
	fmt.Println("\n🧪 Step 5: Testing UUID generation...")
	var testUUID string
	err = db.QueryRow("SELECT uuid_generate_v4()").Scan(&testUUID)
	if err != nil {
		fmt.Printf("❌ Failed to generate UUID: %v\n", err)
		
		// Try alternative UUID generation methods
		fmt.Println("🔄 Trying alternative UUID generation...")
		err = db.QueryRow("SELECT gen_random_uuid()").Scan(&testUUID)
		if err != nil {
			fmt.Printf("❌ gen_random_uuid() also failed: %v\n", err)
		} else {
			fmt.Printf("✅ Generated UUID using gen_random_uuid(): %s\n", testUUID)
		}
	} else {
		fmt.Printf("✅ Generated UUID using uuid_generate_v4(): %s\n", testUUID)
	}

	// Test 6: Test simple table creation
	fmt.Println("\n📋 Step 6: Testing simple table creation...")
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS test_table (
			id SERIAL PRIMARY KEY,
			name VARCHAR(50),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		fmt.Printf("❌ Failed to create test table: %v\n", err)
	} else {
		fmt.Println("✅ Test table created successfully!")
		
		// Clean up
		_, err = db.Exec("DROP TABLE IF EXISTS test_table")
		if err != nil {
			fmt.Printf("⚠️  Failed to clean up test table: %v\n", err)
		} else {
			fmt.Println("✅ Test table cleaned up")
		}
	}

	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("UUID EXTENSION TEST SUMMARY")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Println("✅ Database connection: WORKING")
	fmt.Println("✅ PostgreSQL version: DETECTED")
	
	if len(availableExtensions) > 0 {
		fmt.Println("✅ UUID extensions: AVAILABLE")
	} else {
		fmt.Println("❌ UUID extensions: NOT AVAILABLE")
	}
	
	fmt.Println("\n💡 Recommendations:")
	fmt.Println("1. Use the working UUID generation method in your tables")
	fmt.Println("2. Fix the multiple main() function issue")
	fmt.Println("3. Use proper Go module structure")
}

func loadDatabaseConfig() {
	configFile := "../config/database.env"
	if _, err := os.Stat(configFile); err != nil {
		return
	}

	content, err := ioutil.ReadFile(configFile)
	if err != nil {
		return
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
