-- PostgreSQL Setup Verification Script
-- Run this in pgAdmin Query Tool to verify your setup

-- 1. Check PostgreSQL version and connection
SELECT 
    'PostgreSQL Version' as check_type,
    version() as result;

-- 2. Check current database and user
SELECT 
    'Current Connection' as check_type,
    'Database: ' || current_database() || ', User: ' || current_user as result;

-- 3. Check if we're in the correct database
SELECT 
    'Database Check' as check_type,
    CASE 
        WHEN current_database() = 'dodo' THEN '✅ Connected to dodo database'
        ELSE '❌ Wrong database! Should be connected to dodo, currently in ' || current_database()
    END as result;

-- 4. Check user permissions
SELECT 
    'User Permissions' as check_type,
    CASE 
        WHEN usesuper THEN '✅ User has superuser privileges'
        ELSE '⚠️ User does not have superuser privileges'
    END as result
FROM pg_user 
WHERE usename = current_user;

-- 5. Check if UUID extension can be created (needed for our tables)
SELECT 
    'UUID Extension' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp') 
        THEN '✅ UUID extension already installed'
        ELSE '⚠️ UUID extension not installed (will be created with tables)'
    END as result;

-- 6. List existing tables in the database
SELECT 
    'Existing Tables' as check_type,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ Database is empty (ready for table creation)'
        ELSE '⚠️ Found ' || COUNT(*) || ' existing tables'
    END as result
FROM information_schema.tables 
WHERE table_schema = 'public';

-- 7. Show existing tables if any
SELECT 
    'Table: ' || table_name as existing_tables
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

-- 8. Test table creation permissions
DO $$
BEGIN
    -- Try to create a test table
    CREATE TEMP TABLE test_permissions (id SERIAL);
    RAISE NOTICE '✅ Table creation permissions: OK';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Table creation permissions: FAILED - %', SQLERRM;
END $$;

-- 9. Database size and basic info
SELECT 
    'Database Info' as check_type,
    'Size: ' || pg_size_pretty(pg_database_size(current_database())) || 
    ', Encoding: ' || pg_encoding_to_char(encoding) as result
FROM pg_database 
WHERE datname = current_database();

-- Summary message
SELECT 
    '🎯 SETUP VERIFICATION COMPLETE' as summary,
    'Check the results above. If all checks pass, you can proceed with table creation.' as message;
