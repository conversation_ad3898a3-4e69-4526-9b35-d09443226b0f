version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: shark-postgres
    environment:
      POSTGRES_DB: shark_db
      POSTGRES_USER: shark_user
      POSTGRES_PASSWORD: shark_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schemas:/docker-entrypoint-initdb.d
    networks:
      - shark-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: shark-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - shark-network

  # PostgreSQL Admin (pgAdmin)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: shark-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - shark-network

  # API Gateway (Future)
  # api-gateway:
  #   build:
  #     context: ./services/api-gateway
  #     dockerfile: Dockerfile
  #   container_name: shark-api-gateway
  #   ports:
  #     - "8000:8000"
  #   environment:
  #     - DATABASE_URL=**************************************************/shark_db
  #     - REDIS_URL=redis://redis:6379
  #   depends_on:
  #     - postgres
  #     - redis
  #   networks:
  #     - shark-network

volumes:
  postgres_data:
  redis_data:

networks:
  shark-network:
    driver: bridge
