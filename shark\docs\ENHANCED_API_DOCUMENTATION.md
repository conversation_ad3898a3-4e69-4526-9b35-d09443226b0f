# 🚀 **SHARK PLATFORM ENHANCED API DOCUMENTATION**

## 📋 **OVERVIEW**

This document provides comprehensive API documentation for all enhanced microservices in the Shark Platform, including the new features for cart management, coupons, multi-vendor support, advanced scheduling, tax calculation, and more.

## 🏗️ **MICROSERVICES ARCHITECTURE**

### **Enhanced Microservices:**
- 🛒 **Cart Service** - Shopping cart management
- 🎫 **Coupon Service** - Discount and coupon management  
- 💰 **Tax & Fee Service** - Dynamic tax and fee calculation
- 📅 **Enhanced Scheduling Service** - Advanced time slot management
- 📍 **Address & Shipping Service** - Address and shipping management
- ❤️ **Wishlist Service** - User wishlist management
- 👥 **Multi-Vendor Service** - Vendor relationship management

### **Updated Existing Services:**
- 🛍️ **Product Service** - Enhanced with multi-vendor support
- 🔧 **Service Catalog** - Enhanced with vendor relationships
- 📅 **Booking Service** - Enhanced with time slots

---

## 🛒 **CART SERVICE API**

**Base URL:** `http://localhost:8080/api/v1/cart`

### **Authentication**
All cart endpoints require user authentication via:
- **Header:** `X-User-ID: {user_uuid}` (for testing)
- **JWT Token:** `Authorization: Bearer {token}` (production)

### **📋 Cart Management**

#### **GET /api/v1/cart**
Get user's shopping cart with all items and summary.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cart-uuid",
    "user_id": "user-uuid",
    "items": [
      {
        "id": "item-uuid",
        "product_id": "product-uuid",
        "vendor_id": "vendor-uuid",
        "quantity": 2,
        "unit_price": 129.99,
        "total_price": 259.98,
        "product": {
          "name": "Cordless Impact Driver",
          "sku": "IMPACT-001",
          "images": ["image1.jpg"]
        },
        "vendor": {
          "first_name": "Mike",
          "last_name": "Johnson",
          "email": "<EMAIL>"
        },
        "add_ons": []
      }
    ],
    "services": [],
    "summary": {
      "item_count": 1,
      "service_count": 0,
      "subtotal": 259.98,
      "tax_amount": 21.42,
      "fee_amount": 5.00,
      "shipping_amount": 5.99,
      "discount_amount": 0.00,
      "total": 292.39
    }
  },
  "message": "Cart retrieved successfully"
}
```

#### **POST /api/v1/cart/products**
Add a product to the cart.

**Request Body:**
```json
{
  "product_id": "product-uuid",
  "vendor_id": "vendor-uuid",
  "product_variant_id": "variant-uuid",
  "quantity": 2,
  "add_ons": [
    {
      "addon_id": "addon-uuid",
      "quantity": 1
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cart-item-uuid",
    "product_id": "product-uuid",
    "vendor_id": "vendor-uuid",
    "quantity": 2,
    "unit_price": 129.99,
    "total_price": 259.98
  },
  "message": "Product added to cart successfully"
}
```

#### **POST /api/v1/cart/services**
Add a service to the cart.

**Request Body:**
```json
{
  "service_id": "service-uuid",
  "vendor_id": "vendor-uuid",
  "service_variant_id": "variant-uuid",
  "preferred_date": "2024-01-15",
  "preferred_time_slot": "morning",
  "duration": 120,
  "notes": "Please call before arriving",
  "add_ons": []
}
```

#### **PUT /api/v1/cart/items/{itemId}**
Update cart item quantity.

**Request Body:**
```json
{
  "quantity": 3
}
```

#### **DELETE /api/v1/cart/items/{itemId}**
Remove item from cart.

#### **DELETE /api/v1/cart/services/{serviceId}**
Remove service from cart.

#### **DELETE /api/v1/cart**
Clear entire cart.

### **🎫 Coupon Management**

#### **POST /api/v1/cart/coupons**
Apply coupon to cart.

**Request Body:**
```json
{
  "coupon_code": "WELCOME10"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subtotal": 259.98,
    "discount_amount": 25.00,
    "total": 267.39,
    "applied_coupons": [
      {
        "code": "WELCOME10",
        "name": "Welcome Discount",
        "discount_type": "percentage",
        "discount_amount": 25.00
      }
    ]
  },
  "message": "Coupon applied successfully"
}
```

### **🚚 Shipping Calculation**

#### **POST /api/v1/cart/shipping/calculate**
Calculate shipping costs.

**Request Body:**
```json
{
  "address_id": "address-uuid",
  "shipping_method_id": "method-uuid",
  "address": {
    "address_line1": "123 Main St",
    "city": "San Francisco",
    "state": "CA",
    "postal_code": "94102",
    "country": "US"
  }
}
```

---

## 🎫 **COUPON SERVICE API**

**Base URL:** `http://localhost:8081/api/v1/coupons`

### **📋 Coupon Management**

#### **GET /api/v1/coupons**
Get all coupons with filtering and pagination.

**Query Parameters:**
- `code` - Filter by coupon code
- `discount_type` - Filter by type (percentage, fixed_amount, free_shipping)
- `is_active` - Filter by active status
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)

#### **POST /api/v1/coupons**
Create a new coupon.

**Request Body:**
```json
{
  "code": "SUMMER2024",
  "name": "Summer Sale",
  "description": "20% off summer collection",
  "discount_type": "percentage",
  "discount_value": 20.0,
  "minimum_order_amount": 100.0,
  "maximum_discount_amount": 50.0,
  "usage_limit": 1000,
  "usage_limit_per_user": 1,
  "valid_from": "2024-06-01T00:00:00Z",
  "valid_until": "2024-08-31T23:59:59Z",
  "applicable_to": "categories",
  "category_ids": ["category-uuid"]
}
```

#### **GET /api/v1/coupons/{couponId}**
Get coupon details.

#### **PUT /api/v1/coupons/{couponId}**
Update coupon.

#### **DELETE /api/v1/coupons/{couponId}**
Delete coupon.

### **🔍 Coupon Validation**

#### **POST /api/v1/coupons/validate**
Validate coupon for a specific order.

**Request Body:**
```json
{
  "code": "WELCOME10",
  "user_id": "user-uuid",
  "subtotal": 150.00,
  "order_type": "product"
}
```

**Response:**
```json
{
  "success": true,
  "valid": true,
  "discount_amount": 15.00,
  "message": "Coupon is valid"
}
```

#### **POST /api/v1/coupons/apply**
Apply coupon and record usage.

**Request Body:**
```json
{
  "code": "WELCOME10",
  "user_id": "user-uuid",
  "order_id": "order-uuid",
  "subtotal": 150.00
}
```

### **📊 Coupon Analytics**

#### **GET /api/v1/coupons/stats**
Get coupon usage statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "total_coupons": 25,
    "active_coupons": 18,
    "expired_coupons": 7,
    "total_usages": 1250,
    "total_discount_given": 15750.50,
    "top_coupons": [
      {
        "code": "WELCOME10",
        "name": "Welcome Discount",
        "usage_count": 450,
        "total_saved": 4500.00
      }
    ]
  }
}
```

---

## 💰 **TAX & FEE SERVICE API**

**Base URL:** `http://localhost:8082/api/v1/tax-fee`

### **💸 Tax Calculation**

#### **POST /api/v1/tax-fee/calculate-tax**
Calculate taxes based on location and amount.

**Request Body:**
```json
{
  "subtotal": 100.00,
  "country": "US",
  "state": "CA",
  "city": "San Francisco",
  "postal_code": "94102"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_tax": 8.25,
    "breakdown": [
      {
        "name": "California Sales Tax",
        "rate": 0.0825,
        "amount": 8.25
      }
    ]
  }
}
```

### **💳 Fee Calculation**

#### **POST /api/v1/tax-fee/calculate-fees**
Calculate applicable fees.

**Request Body:**
```json
{
  "subtotal": 100.00,
  "applicable_to": "all"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_fees": 7.50,
    "breakdown": [
      {
        "name": "Processing Fee",
        "type": "processing",
        "amount": 2.50
      },
      {
        "name": "Service Fee",
        "type": "service",
        "amount": 5.00
      }
    ]
  }
}
```

---

## 📅 **ENHANCED SCHEDULING SERVICE API**

**Base URL:** `http://localhost:8083/api/v1/scheduling`

### **⏰ Time Slot Management**

#### **GET /api/v1/scheduling/time-slots**
Get all available time slots.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "slot-uuid",
      "name": "Morning",
      "start_time": "08:00:00",
      "end_time": "12:00:00",
      "duration": 240,
      "is_active": true
    }
  ]
}
```

#### **POST /api/v1/scheduling/time-slots**
Create new time slot.

#### **GET /api/v1/scheduling/vendors/{vendorId}/availability**
Get vendor availability for a date range.

**Query Parameters:**
- `start_date` - Start date (YYYY-MM-DD)
- `end_date` - End date (YYYY-MM-DD)

**Response:**
```json
{
  "success": true,
  "data": {
    "vendor_id": "vendor-uuid",
    "availability": [
      {
        "date": "2024-01-15",
        "day_of_week": 1,
        "available_slots": [
          {
            "time_slot_id": "slot-uuid",
            "name": "Morning",
            "start_time": "08:00:00",
            "end_time": "12:00:00",
            "max_bookings": 2,
            "current_bookings": 0,
            "is_available": true
          }
        ]
      }
    ]
  }
}
```

---

## 📍 **ADDRESS & SHIPPING SERVICE API**

**Base URL:** `http://localhost:8084/api/v1/address-shipping`

### **🏠 Address Management**

#### **GET /api/v1/address-shipping/addresses**
Get user addresses.

#### **POST /api/v1/address-shipping/addresses**
Create new address.

**Request Body:**
```json
{
  "type": "shipping",
  "is_default": true,
  "first_name": "John",
  "last_name": "Doe",
  "address_line1": "123 Main St",
  "city": "San Francisco",
  "state": "CA",
  "postal_code": "94102",
  "country": "US",
  "phone": "******-0123"
}
```

### **🚚 Shipping Methods**

#### **GET /api/v1/address-shipping/shipping-methods**
Get available shipping methods.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "method-uuid",
      "name": "Standard Shipping",
      "carrier": "USPS",
      "service_type": "Ground",
      "base_cost": 5.99,
      "estimated_days_min": 3,
      "estimated_days_max": 7
    }
  ]
}
```

---

## ❤️ **WISHLIST SERVICE API**

**Base URL:** `http://localhost:8085/api/v1/wishlist`

### **💝 Wishlist Management**

#### **GET /api/v1/wishlist**
Get user's wishlists.

#### **POST /api/v1/wishlist**
Create new wishlist.

#### **POST /api/v1/wishlist/{wishlistId}/items**
Add item to wishlist.

**Request Body:**
```json
{
  "product_id": "product-uuid",
  "vendor_id": "vendor-uuid",
  "notes": "Want this for home project"
}
```

---

## 🔧 **ERROR HANDLING**

### **Standard Error Response:**
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    "Detailed error message 1",
    "Detailed error message 2"
  ]
}
```

### **HTTP Status Codes:**
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

---

## 🔐 **AUTHENTICATION & AUTHORIZATION**

### **JWT Token Structure:**
```json
{
  "user_id": "user-uuid",
  "email": "<EMAIL>",
  "roles": ["customer", "vendor"],
  "exp": 1640995200,
  "iat": 1640908800
}
```

### **Required Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
X-User-ID: {user_uuid} // For testing only
```

---

## 📊 **PAGINATION**

### **Standard Pagination Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `sort_by` - Sort field
- `sort_order` - Sort direction (asc/desc)

### **Pagination Response:**
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "total_pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

---

## 🚀 **NEXT STEPS**

1. **Test APIs** using the provided endpoints
2. **Integrate Frontend** with these enhanced APIs
3. **Monitor Performance** and optimize as needed
4. **Add More Features** based on business requirements

**Your Shark Platform now has complete, production-ready APIs for all enhanced e-commerce and service marketplace features!** 🎉
