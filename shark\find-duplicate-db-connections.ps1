# Find Duplicate Database Connection Code
Write-Host "SCANNING FOR DUPLICATE DATABASE CONNECTIONS" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor White

# Function to check if file contains database connection code
function Test-DatabaseConnection {
    param($FilePath)
    
    if (!(Test-Path $FilePath)) { return $false }
    
    $content = Get-Content $FilePath -Raw
    
    # Check for database connection patterns
    $patterns = @(
        "sql\.Open\(",
        "InitDB\(",
        "database/sql",
        "DB_HOST",
        "DB_PASSWORD",
        "postgres://",
        "dsn.*:=.*fmt\.Sprintf"
    )
    
    foreach ($pattern in $patterns) {
        if ($content -match $pattern) {
            return $true
        }
    }
    
    return $false
}

# Function to extract database connection details
function Get-DatabaseConnectionInfo {
    param($FilePath)
    
    if (!(Test-Path $FilePath)) { return @() }
    
    $content = Get-Content $FilePath
    $info = @()
    
    # Look for InitDB function
    $inInitDB = $false
    $initDBLines = @()
    
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        if ($line -match "func.*InitDB") {
            $inInitDB = $true
            $initDBLines += "Line $($i+1): $line"
        } elseif ($inInitDB -and $line -match "^}") {
            $inInitDB = $false
            $info += @{Type="InitDB Function"; Lines=$initDBLines}
            $initDBLines = @()
        } elseif ($inInitDB) {
            $initDBLines += "Line $($i+1): $line"
        }
        
        # Look for sql.Open calls
        if ($line -match "sql\.Open") {
            $info += @{Type="sql.Open Call"; Lines=@("Line $($i+1): $line")}
        }
        
        # Look for DSN construction
        if ($line -match "dsn.*:=.*fmt\.Sprintf") {
            $info += @{Type="DSN Construction"; Lines=@("Line $($i+1): $line")}
        }
    }
    
    return $info
}

# Services to check
$services = @(
    "user-service",
    "service-catalog", 
    "product-service",
    "booking-service",
    "payment-service",
    "notification-service",
    "review-service",
    "order-service",
    "analytics-service"
)

Write-Host "`nSCANNING MICROSERVICES FOR DATABASE CONNECTION CODE..." -ForegroundColor Yellow

$duplicateConnections = @()

foreach ($service in $services) {
    Write-Host "`nChecking $service..." -ForegroundColor Cyan
    
    # Check main.go
    $mainFile = "services/$service/cmd/main.go"
    if (Test-DatabaseConnection $mainFile) {
        Write-Host "  FOUND: Database connection in main.go" -ForegroundColor Red
        $info = Get-DatabaseConnectionInfo $mainFile
        $duplicateConnections += @{Service=$service; File="cmd/main.go"; Info=$info}
    }
    
    # Check config.go
    $configFile = "services/$service/internal/config/config.go"
    if (Test-DatabaseConnection $configFile) {
        Write-Host "  FOUND: Database connection in config.go" -ForegroundColor Red
        $info = Get-DatabaseConnectionInfo $configFile
        $duplicateConnections += @{Service=$service; File="internal/config/config.go"; Info=$info}
    }
    
    # Check for other database files
    $dbFiles = @(
        "services/$service/internal/database/database.go",
        "services/$service/internal/db/db.go",
        "services/$service/pkg/database/database.go"
    )
    
    foreach ($dbFile in $dbFiles) {
        if (Test-DatabaseConnection $dbFile) {
            Write-Host "  FOUND: Database connection in $($dbFile.Split('/')[-1])" -ForegroundColor Red
            $info = Get-DatabaseConnectionInfo $dbFile
            $duplicateConnections += @{Service=$service; File=$dbFile; Info=$info}
        }
    }
}

# Check utility scripts
Write-Host "`nChecking utility scripts..." -ForegroundColor Cyan
$utilityFiles = @(
    "database/migrate.go",
    "database/inspect-db.go"
)

foreach ($file in $utilityFiles) {
    if (Test-DatabaseConnection $file) {
        Write-Host "  FOUND: Database connection in $file" -ForegroundColor Yellow
        $info = Get-DatabaseConnectionInfo $file
        $duplicateConnections += @{Service="Utility"; File=$file; Info=$info}
    }
}

# Report findings
Write-Host "`n" + "="*50 -ForegroundColor White
Write-Host "DUPLICATE DATABASE CONNECTION ANALYSIS" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor White

if ($duplicateConnections.Count -eq 0) {
    Write-Host "No duplicate database connections found!" -ForegroundColor Green
} else {
    Write-Host "Found $($duplicateConnections.Count) files with database connection code:" -ForegroundColor Red
    
    $groupedByService = $duplicateConnections | Group-Object Service
    
    foreach ($group in $groupedByService) {
        Write-Host "`n$($group.Name) Service:" -ForegroundColor Yellow
        foreach ($connection in $group.Group) {
            Write-Host "  - $($connection.File)" -ForegroundColor Red
            foreach ($info in $connection.Info) {
                Write-Host "    $($info.Type)" -ForegroundColor Gray
            }
        }
    }
}

# Recommendations
Write-Host "`nRECOMMENDATIONS:" -ForegroundColor Cyan
Write-Host "1. Create a shared database package" -ForegroundColor White
Write-Host "2. Move all InitDB functions to shared/database/connection.go" -ForegroundColor White
Write-Host "3. Use the shared configuration system" -ForegroundColor White
Write-Host "4. Remove duplicate database connection code" -ForegroundColor White

Write-Host "`nDuplicate database connection scan complete!" -ForegroundColor Green
