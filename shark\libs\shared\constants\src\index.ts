// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

// Application Routes
export const ROUTES = {
  // Public Routes
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  SERVICES: '/services',
  PRODUCTS: '/products',
  ABOUT: '/about',
  CONTACT: '/contact',

  // Customer Routes
  CUSTOMER: {
    DASHBOARD: '/customer/dashboard',
    BOOKINGS: '/customer/bookings',
    ORDERS: '/customer/orders',
    PROFILE: '/customer/profile',
    FAVORITES: '/customer/favorites',
    REVIEWS: '/customer/reviews',
  },

  // Vendor Routes
  VENDOR: {
    DASHBOARD: '/vendor/dashboard',
    SERVICES: '/vendor/services',
    PRODUCTS: '/vendor/products',
    BOOKINGS: '/vendor/bookings',
    ORDERS: '/vendor/orders',
    ANALYTICS: '/vendor/analytics',
    PROFILE: '/vendor/profile',
    CALENDAR: '/vendor/calendar',
  },

  // Admin Routes
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    SERVICES: '/admin/services',
    PRODUCTS: '/admin/products',
    CATEGORIES: '/admin/categories',
    BOOKINGS: '/admin/bookings',
    ORDERS: '/admin/orders',
    ANALYTICS: '/admin/analytics',
    SETTINGS: '/admin/settings',
  },
} as const;

// Service Categories
export const SERVICE_CATEGORIES = [
  {
    id: 'cleaning',
    name: 'Cleaning Services',
    icon: '🧹',
    description: 'Professional cleaning services for homes and offices',
    subcategories: [
      'House Cleaning',
      'Deep Cleaning',
      'Office Cleaning',
      'Carpet Cleaning',
      'Window Cleaning',
    ],
  },
  {
    id: 'repair',
    name: 'Repair & Maintenance',
    icon: '🔧',
    description: 'Home repair and maintenance services',
    subcategories: [
      'Plumbing',
      'Electrical',
      'Carpentry',
      'Painting',
      'AC Repair',
    ],
  },
  {
    id: 'beauty',
    name: 'Beauty & Wellness',
    icon: '💄',
    description: 'Personal care and wellness services',
    subcategories: [
      'Hair Styling',
      'Makeup',
      'Massage',
      'Spa Services',
      'Nail Care',
    ],
  },
  {
    id: 'fitness',
    name: 'Fitness & Health',
    icon: '💪',
    description: 'Fitness training and health services',
    subcategories: [
      'Personal Training',
      'Yoga',
      'Physiotherapy',
      'Nutrition Consulting',
      'Mental Health',
    ],
  },
] as const;

// Product Categories
export const PRODUCT_CATEGORIES = [
  {
    id: 'tools',
    name: 'Tools & Equipment',
    icon: '🔨',
    description: 'Professional tools and equipment',
  },
  {
    id: 'supplies',
    name: 'Cleaning Supplies',
    icon: '🧽',
    description: 'Cleaning products and supplies',
  },
  {
    id: 'materials',
    name: 'Construction Materials',
    icon: '🧱',
    description: 'Building and construction materials',
  },
  {
    id: 'electronics',
    name: 'Electronics',
    icon: '📱',
    description: 'Electronic devices and accessories',
  },
] as const;

// Validation Rules
export const VALIDATION = {
  PASSWORD: {
    MIN_LENGTH: 8,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: true,
  },
  PHONE: {
    PATTERN: /^[+]?[1-9][\d\s\-\(\)]{7,15}$/,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
  },
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

// File Upload
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  MAX_FILES: 5,
} as const;

// Time Slots
export const TIME_SLOTS = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
  '15:00', '15:30', '16:00', '16:30', '17:00', '17:30',
  '18:00', '18:30', '19:00', '19:30', '20:00',
] as const;

// Status Colors
export const STATUS_COLORS = {
  PENDING: '#FFA500',
  CONFIRMED: '#4CAF50',
  IN_PROGRESS: '#2196F3',
  COMPLETED: '#8BC34A',
  CANCELLED: '#F44336',
  REFUNDED: '#9C27B0',
  PROCESSING: '#FF9800',
  SHIPPED: '#3F51B5',
  DELIVERED: '#4CAF50',
  FAILED: '#F44336',
} as const;

// Currency
export const CURRENCY = {
  SYMBOL: '₹',
  CODE: 'INR',
  LOCALE: 'en-IN',
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  INPUT: 'yyyy-MM-dd',
  TIME: 'HH:mm',
  DATETIME: 'MMM dd, yyyy HH:mm',
  ISO: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'Something went wrong. Please try again later.',
  SESSION_EXPIRED: 'Your session has expired. Please login again.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Welcome back!',
  REGISTER_SUCCESS: 'Account created successfully!',
  BOOKING_SUCCESS: 'Booking confirmed successfully!',
  ORDER_SUCCESS: 'Order placed successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'shark_auth_token',
  USER_DATA: 'shark_user_data',
  CART: 'shark_cart',
  PREFERENCES: 'shark_preferences',
  THEME: 'shark_theme',
} as const;

// Theme Configuration
export const THEME = {
  COLORS: {
    PRIMARY: '#1976D2',
    SECONDARY: '#DC004E',
    SUCCESS: '#4CAF50',
    WARNING: '#FF9800',
    ERROR: '#F44336',
    INFO: '#2196F3',
  },
  BREAKPOINTS: {
    XS: '0px',
    SM: '600px',
    MD: '960px',
    LG: '1280px',
    XL: '1920px',
  },
} as const;
