{"name": "shared-types", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/types/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/types", "main": "libs/shared/types/src/index.ts", "tsConfig": "libs/shared/types/tsconfig.lib.json", "assets": []}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared/types/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/types/jest.config.ts", "passWithNoTests": true}}}, "tags": ["scope:shared", "type:types"]}