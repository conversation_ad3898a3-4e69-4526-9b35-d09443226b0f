import React from 'react';

export interface AvatarProps {
  src?: string;
  alt?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fallback?: string;
  className?: string;
  'data-testid'?: string;
}

export const Avatar: React.FC<AvatarProps> = ({
  src,
  alt = '',
  size = 'md',
  fallback,
  className = '',
  'data-testid': testId,
}) => {
  const sizeClasses = {
    sm: 'h-8 w-8 text-sm',
    md: 'h-10 w-10 text-base',
    lg: 'h-12 w-12 text-lg',
    xl: 'h-16 w-16 text-xl',
  };

  const classes = [
    'inline-flex items-center justify-center rounded-full bg-gray-300 text-gray-700 font-medium',
    sizeClasses[size],
    className,
  ].filter(Boolean).join(' ');

  if (src) {
    return (
      <img
        src={src}
        alt={alt}
        className={`${classes} object-cover`}
        data-testid={testId}
      />
    );
  }

  return (
    <div className={classes} data-testid={testId}>
      {fallback || alt.charAt(0).toUpperCase() || '?'}
    </div>
  );
};
