{"name": "@modern-js/node-bundle-require", "version": "2.67.6", "description": "A Progressive React Framework for modern web development.", "homepage": "https://modernjs.dev", "bugs": "https://github.com/web-infra-dev/modern.js/issues", "repository": {"type": "git", "url": "https://github.com/web-infra-dev/modern.js", "directory": "packages/toolkit/node-bundle-require"}, "license": "MIT", "keywords": ["react", "framework", "modern", "modern.js"], "jsnext:source": "./src/index.ts", "types": "./dist/types/index.d.ts", "main": "./dist/cjs/index.js", "exports": {".": {"types": "./dist/types/index.d.ts", "node": {"jsnext:source": "./src/index.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "default": "./dist/cjs/index.js"}, "./bundle": {"types": "./dist/types/bundle.d.ts", "default": "./dist/cjs/bundle.js"}}, "typesVersions": {"*": {".": ["./dist/types/index.d.ts"], "bundle": ["./dist/types/bundle.d.ts"]}}, "dependencies": {"@swc/helpers": "^0.5.17", "esbuild": "0.17.19", "@modern-js/utils": "2.67.6"}, "devDependencies": {"@types/jest": "^29", "@types/node": "^14", "jest": "^29", "typescript": "^5", "@scripts/jest-config": "2.66.0", "@scripts/build": "2.66.0"}, "sideEffects": false, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "scripts": {"new": "modern-lib new", "build": "modern-lib build", "test": "jest --passWithNoTests"}}