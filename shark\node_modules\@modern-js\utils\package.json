{"name": "@modern-js/utils", "description": "A Progressive React Framework for modern web development.", "homepage": "https://modernjs.dev", "bugs": "https://github.com/web-infra-dev/modern.js/issues", "repository": {"type": "git", "url": "https://github.com/web-infra-dev/modern.js", "directory": "packages/toolkit/utils"}, "license": "MIT", "keywords": ["react", "framework", "modern", "modern.js"], "version": "2.67.6", "jsnext:source": "./src/index.ts", "types": "./dist/types/index.d.ts", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "_comment": "Provide ESM and CJS exports, ESM is used by runtime package, for treeshaking", "exports": {".": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.js"}, "./logger": {"types": "./dist/types/cli/logger.d.ts", "default": "./dist/cjs/cli/logger.js"}, "./chain-id": {"types": "./dist/types/cli/constants/chainId.d.ts", "default": "./dist/cjs/cli/constants/chainId.js"}, "./universal": {"types": "./dist/types/universal/index.d.ts", "import": "./dist/esm/universal/index.js", "default": "./dist/cjs/universal/index.js"}, "./universal/constants": {"types": "./dist/types/universal/constants.d.ts", "import": "./dist/esm/universal/constants.js", "default": "./dist/cjs/universal/constants.js"}, "./universal/format-webpack": {"types": "./dist/types/universal/formatWebpack.d.ts", "import": "./dist/esm/universal/formatWebpack.js", "default": "./dist/cjs/universal/formatWebpack.js"}, "./universal/plugin-dag-sort": {"types": "./dist/types/universal/pluginDagSort.d.ts", "import": "./dist/esm/universal/pluginDagSort.js", "default": "./dist/cjs/universal/pluginDagSort.js"}, "./commander": "./dist/compiled/commander/index.js", "./ora": "./dist/compiled/ora/index.js", "./glob": "./dist/compiled/glob/index.js", "./chalk": "./dist/compiled/chalk/index.js", "./execa": "./dist/compiled/execa/index.js", "./json5": "./dist/compiled/json5/index.js", "./semver": "./dist/compiled/semver/index.js", "./lodash": "./dist/compiled/lodash/index.js", "./globby": "./dist/compiled/globby/index.js", "./fs-extra": "./dist/compiled/fs-extra/index.js", "./fast-glob": "./dist/compiled/fast-glob/index.js", "./gzip-size": "./dist/compiled/gzip-size/index.js", "./mime-types": "./dist/compiled/mime-types/index.js", "./strip-ansi": "./dist/compiled/strip-ansi/index.js", "./browserslist": "./dist/compiled/browserslist/index.js", "./webpack-chain": "./dist/compiled/webpack-chain/index.js", "./tsconfig-paths": "./dist/compiled/tsconfig-paths/index.js", "./react-server-dom-webpack": "./dist/compiled/react-server-dom-webpack/index.js", "./react-server-dom-webpack/client": {"workerd": "./dist/compiled/react-server-dom-webpack/client.edge.js", "deno": "./dist/compiled/react-server-dom-webpack/client.edge.js", "worker": "./dist/compiled/react-server-dom-webpack/client.edge.js", "node": {"webpack": "./dist/compiled/react-server-dom-webpack/client.node.js", "default": "./dist/compiled/react-server-dom-webpack/client.node.unbundled.js"}, "edge-light": "./dist/compiled/react-server-dom-webpack/client.edge.js", "browser": "./dist/compiled/react-server-dom-webpack/client.browser.js", "default": "./dist/compiled/react-server-dom-webpack/client.browser.js"}, "./react-server-dom-webpack/client.browser": "./dist/compiled/react-server-dom-webpack/client.browser.js", "./react-server-dom-webpack/client.edge": "./dist/compiled/react-server-dom-webpack/client.edge.js", "./react-server-dom-webpack/server": {"react-server": {"workerd": "./dist/compiled/react-server-dom-webpack/server.edge.js", "deno": "./dist/compiled/react-server-dom-webpack/server.browser.js", "node": {"webpack": "./dist/compiled/react-server-dom-webpack/server.node.js", "default": "./dist/compiled/react-server-dom-webpack/server.node.unbundled.js"}, "edge-light": "./dist/compiled/react-server-dom-webpack/server.edge.js", "browser": "./dist/compiled/react-server-dom-webpack/server.browser.js"}, "default": "./dist/compiled/react-server-dom-webpack/server.js"}, "./react-server-dom-webpack/server.edge": "./dist/compiled/react-server-dom-webpack/server.edge.js"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "typesVersions": {"*": {"logger": ["./dist/types/cli/logger.d.ts"], "chain-id": ["./dist/types/cli/constants/chainId.d.ts"], "universal": ["./dist/types/universal/index.d.ts"], "universal/constants": ["./dist/types/universal/constants.d.ts"], "universal/format-webpack": ["./dist/types/universal/formatWebpack.d.ts"], "universal/plugin-dag-sort": ["./dist/types/universal/pluginDagSort.d.ts"], "execa": ["./dist/compiled/execa/index.d.ts"], "glob": ["./dist/compiled/glob/index.d.ts"], "chalk": ["./dist/compiled/chalk/index.d.ts"], "json5": ["./dist/compiled/json5/index.d.ts"], "semver": ["./dist/compiled/semver/index.d.ts"], "lodash": ["./dist/compiled/lodash/index.d.ts"], "globby": ["./dist/compiled/globby/index.d.ts"], "fs-extra": ["./dist/compiled/fs-extra/index.d.ts"], "fast-glob": ["./dist/compiled/fast-glob/index.d.ts"], "gzip-size": ["./dist/compiled/gzip-size/index.d.ts"], "mime-types": ["./dist/compiled/mime-types/index.d.ts"], "strip-ansi": ["./dist/compiled/strip-ansi/index.d.ts"], "browserslist": ["./dist/compiled/browserslist/index.d.ts"], "webpack-chain": ["./dist/compiled/webpack-chain/types/index.d.ts"], "tsconfig-paths": ["./dist/compiled/tsconfig-paths/lib/index.d.ts"], "commander": ["./dist/compiled/commander/typings/index.d.ts"], "ora": ["./dist/compiled/ora/index.d.ts"]}}, "dependencies": {"@swc/helpers": "^0.5.17", "caniuse-lite": "^1.0.30001520", "lodash": "^4.17.21", "rslog": "^1.1.0"}, "devDependencies": {"@types/jest": "^29", "@types/node": "^14", "jest": "^29", "typescript": "^5", "webpack": "^5.99.8", "@modern-js/types": "2.67.6", "@scripts/build": "2.66.0", "@scripts/jest-config": "2.66.0"}, "sideEffects": false, "scripts": {"new": "modern-lib new", "dev": "modern-lib build --watch", "build": "modern-lib build", "test": "jest --passWithNoTests"}}