# @module-federation/bridge-react-webpack-plugin

## 0.9.1

### Patch Changes

- Updated dependencies [35d925b]
- Updated dependencies [35d925b]
- Updated dependencies [8acd217]
  - @module-federation/sdk@0.9.1

## 0.9.0

### Patch Changes

- @module-federation/sdk@0.9.0

## 0.8.12

### Patch Changes

- @module-federation/sdk@0.8.12

## 0.8.11

### Patch Changes

- @module-federation/sdk@0.8.11

## 0.8.10

### Patch Changes

- @module-federation/sdk@0.8.10

## 0.8.9

### Patch Changes

- @module-federation/sdk@0.8.9

## 0.8.8

### Patch Changes

- @module-federation/sdk@0.8.8

## 0.8.7

### Patch Changes

- Updated dependencies [835b09c]
- Updated dependencies [336f3d8]
- Updated dependencies [4fd33fb]
  - @module-federation/sdk@0.8.7

## 0.8.6

### Patch Changes

- @module-federation/sdk@0.8.6

## 0.8.5

### Patch Changes

- @module-federation/sdk@0.8.5

## 0.8.4

### Patch Changes

- @module-federation/sdk@0.8.4

## 0.8.3

### Patch Changes

- Updated dependencies [8e172c8]
  - @module-federation/sdk@0.8.3

## 0.8.2

### Patch Changes

- @module-federation/sdk@0.8.2

## 0.8.1

### Patch Changes

- @module-federation/sdk@0.8.1

## 0.8.0

### Patch Changes

- @module-federation/sdk@0.8.0

## 0.7.7

### Patch Changes

- @module-federation/sdk@0.7.7

## 0.7.6

### Patch Changes

- @module-federation/sdk@0.7.6

## 0.7.5

### Patch Changes

- 0309fb5: fix: wrap try catch with react-router-dom path resolve
  - @module-federation/sdk@0.7.5

## 0.7.4

### Patch Changes

- @module-federation/sdk@0.7.4

## 0.7.3

### Patch Changes

- Updated dependencies [4ab9295]
  - @module-federation/sdk@0.7.3

## 0.7.2

### Patch Changes

- @module-federation/sdk@0.7.2

## 0.7.1

### Patch Changes

- Updated dependencies [6db4c5f]
  - @module-federation/sdk@0.7.1

## 0.7.0

### Minor Changes

- 3942740: add license information
- Updated dependencies [879ad87]
- Updated dependencies [4eb09e7]
- Updated dependencies [206b56d]
  - @module-federation/sdk@0.7.0

## 0.6.16

### Patch Changes

- Updated dependencies [f779188]
- Updated dependencies [024df60]
  - @module-federation/sdk@0.6.16

## 0.6.15

### Patch Changes

- @module-federation/sdk@0.6.15

## 0.6.14

### Patch Changes

- Updated dependencies [ad605d2]
  - @module-federation/sdk@0.6.14

## 0.6.13

### Patch Changes

- @module-federation/sdk@0.6.13

## 0.6.12

### Patch Changes

- @module-federation/sdk@0.6.12

## 0.6.11

### Patch Changes

- Updated dependencies [d5a3072]
  - @module-federation/sdk@0.6.11

## 0.6.10

### Patch Changes

- Updated dependencies [22a3b83]
  - @module-federation/sdk@0.6.10

## 0.6.9

### Patch Changes

- @module-federation/sdk@0.6.9

## 0.6.8

### Patch Changes

- Updated dependencies [32db0ac]
  - @module-federation/sdk@0.6.8

## 0.6.7

### Patch Changes

- Updated dependencies [9e32644]
  - @module-federation/sdk@0.6.7

## 0.6.6

### Patch Changes

- @module-federation/sdk@0.6.6

## 0.6.5

### Patch Changes

- @module-federation/sdk@0.6.5

## 0.6.4

### Patch Changes

- d90295b: chore: adjust bridge router alias strategy to alias to router-v6 when not found react-router-dom in package.json
  - @module-federation/sdk@0.6.4

## 0.6.3

### Patch Changes

- @module-federation/sdk@0.6.3

## 0.6.2

### Patch Changes

- @module-federation/sdk@0.6.2

## 0.6.1

### Patch Changes

- Updated dependencies [2855583]
- Updated dependencies [813680f]
  - @module-federation/sdk@0.6.1

## 0.6.0

### Patch Changes

- Updated dependencies [1d9bb77]
  - @module-federation/sdk@0.6.0

## 0.5.2

### Patch Changes

- Updated dependencies [b90fa7d]
  - @module-federation/sdk@0.5.2

## 0.5.1

### Patch Changes

- 472e2cc: feat: enchance react-bridge react-router-dom alias according to origin react-router-dom version
  - @module-federation/sdk@0.5.1

## 0.5.0

### Patch Changes

- 49d6135: feat(@module-federation/bridge): enhance Bridge capabilities and fix some issues
- Updated dependencies [8378a77]
  - @module-federation/sdk@0.5.0

## 0.4.0

### Patch Changes

- Updated dependencies [a6e2bed]
- Updated dependencies [a6e2bed]
  - @module-federation/sdk@0.4.0

## 0.3.5

### Patch Changes

- @module-federation/sdk@0.3.5

## 0.3.4

### Patch Changes

- @module-federation/sdk@0.3.4

## 0.3.3

### Patch Changes

- @module-federation/sdk@0.3.3

## 0.3.2

### Patch Changes

- @module-federation/sdk@0.3.2

## 0.3.1

### Patch Changes

- @module-federation/sdk@0.3.1

## 0.3.0

### Patch Changes

- Updated dependencies [fa37cc4]
  - @module-federation/sdk@0.3.0

## 0.2.8

### Patch Changes

- @module-federation/sdk@0.2.8

## 0.2.7

### Patch Changes

- Updated dependencies [b00ef13]
  - @module-federation/sdk@0.2.7

## 0.2.6

### Patch Changes

- Updated dependencies [91bf689]
  - @module-federation/sdk@0.2.6

## 0.2.5

### Patch Changes

- Updated dependencies [8cce571]
  - @module-federation/sdk@0.2.5

## 0.2.4

### Patch Changes

- Updated dependencies [09b792d]
- Updated dependencies [09b792d]
  - @module-federation/sdk@0.2.4

## 0.2.3

### Patch Changes

- Updated dependencies [32f26af]
- Updated dependencies [32f26af]
  - @module-federation/sdk@0.2.3

## 0.2.2

### Patch Changes

- @module-federation/sdk@0.2.2

## 0.2.1

### Patch Changes

- Updated dependencies [88445e7]
  - @module-federation/sdk@0.2.1

## 0.2.0

### Minor Changes

- d2ab821: feat(bridge): Supports exporting and loading of application-level modules (with routing), currently supports react and vue3

### Patch Changes

- @module-federation/sdk@0.2.0
