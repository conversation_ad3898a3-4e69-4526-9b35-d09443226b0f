{"name": "@module-federation/bridge-react-webpack-plugin", "version": "0.9.1", "publishConfig": {"access": "public"}, "author": "z<PERSON><PERSON>o <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/module-federation/core", "directory": "packages/bridge-react-webpack-plugin"}, "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "types": "./dist/index.cjs.d.ts", "exports": {".": {"types": "./dist/index.cjs.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}, "./*": "./*"}, "dependencies": {"semver": "7.6.3", "@types/semver": "7.5.8", "@module-federation/sdk": "0.9.1"}, "devDependencies": {"typescript": "^5.2.2", "vite": "^5.2.14", "vite-plugin-dts": "^4.3.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}}