{"name": "bridge-react-webpack-plugin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/bridge/bridge-react-webpack-plugin/src", "projectType": "library", "tags": ["type:pkg"], "targets": {"build": {"executor": "nx:run-commands", "options": {"commands": ["npm run build --prefix packages/bridge/bridge-react-webpack-plugin"]}}, "test": {"executor": "nx:run-commands", "options": {"parallel": false, "commands": [{"command": "vitest run -c packages/bridge/bridge-react-webpack-plugin/vitest.config.ts", "forwardAllArgs": false}]}}}}