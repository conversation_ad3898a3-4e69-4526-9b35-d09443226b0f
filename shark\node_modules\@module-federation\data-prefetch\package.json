{"name": "@module-federation/data-prefetch", "description": "Module Federation Data Prefetch", "version": "0.9.1", "author": "ni<PERSON>an <<EMAIL>>", "homepage": "https://github.com/module-federation/core", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/module-federation/core/", "directory": "packages/data-prefetch"}, "publishConfig": {"access": "public"}, "files": ["dist", "README.md", "LICENSE"], "exports": {".": {"types": "./dist/index.cjs.d.ts", "import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}, "./react": {"types": "./dist/react.cjs.d.ts", "import": "./dist/react.esm.mjs", "require": "./dist/react.cjs.js"}, "./cli": {"types": "./dist/cli.cjs.d.ts", "import": "./dist/cli.esm.mjs", "require": "./dist/cli.cjs.js"}, "./babel-plugin": {"types": "./dist/babel.cjs.d.ts", "import": "./dist/babel.esm.mjs", "require": "./dist/babel.cjs.js"}, "./universal": {"types": "./dist/universal.cjs.d.ts", "import": "./dist/universal.esm.mjs", "require": "./dist/universal.cjs.js"}}, "typesVersions": {"*": {".": ["./dist/index.cjs.d.ts"], "react": ["./dist/react.cjs.d.ts"], "cli": ["./dist/cli.cjs.d.ts"], "universal": ["./dist/universal.cjs.d.ts"], "babel-plugin": ["./dist/babel.cjs.d.ts"]}}, "main": "dist/index.cjs.js", "module": "dist/index.esm.mjs", "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "devDependencies": {"@testing-library/react-hooks": "^8.0.1", "@types/fs-extra": "9.0.6", "@types/jest": "^29.5.11", "@types/node": "^17.0.45", "@types/react": "~18.0.38", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "minimist": "^1.2.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^6.21.3", "react-test-renderer": "^18.2.0", "ts-jest": "29.0.1", "webpack": "5.75.0"}, "dependencies": {"fs-extra": "9.1.0", "@module-federation/runtime": "0.9.1", "@module-federation/sdk": "0.9.1"}, "scripts": {"test": "jest"}}