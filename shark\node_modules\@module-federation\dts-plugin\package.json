{"name": "@module-federation/dts-plugin", "version": "0.9.1", "author": "hanric <<EMAIL>>", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/module-federation/core/", "directory": "packages/dts-plugin"}, "publishConfig": {"access": "public"}, "files": ["dist/", "README.md"], "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./core": {"import": "./dist/core.js", "require": "./dist/core.js"}, "./dynamic-remote-type-hints-plugin": {"types": "./dist/dynamic-remote-type-hints-plugin.d.ts", "import": "./dist/esm/dynamic-remote-type-hints-plugin.js", "require": "./dist/dynamic-remote-type-hints-plugin.js"}, "./*": "./*"}, "typesVersions": {"*": {".": ["./dist/index.d.ts"], "core": ["./dist/core.d.ts"], "dynamic-remote-type-hints-plugin": ["./dist/dynamic-remote-type-hints-plugin.d.ts"]}}, "dependencies": {"adm-zip": "^0.5.10", "ansi-colors": "^4.1.3", "axios": "^1.7.4", "rambda": "^9.1.0", "lodash.clonedeepwith": "4.5.0", "chalk": "3.0.0", "fs-extra": "9.1.0", "isomorphic-ws": "5.0.0", "koa": "2.15.4", "log4js": "6.9.1", "node-schedule": "2.1.1", "ws": "8.18.0", "@module-federation/sdk": "0.9.1", "@module-federation/managers": "0.9.1", "@module-federation/third-party-dts-extractor": "0.9.1", "@module-federation/error-codes": "0.9.1"}, "devDependencies": {"@types/ws": "8.5.12", "@types/koa": "2.15.0", "@types/node-schedule": "2.1.7", "vue": "^3.4.29", "@vue/tsconfig": "^0.5.1", "vue-tsc": "^2.0.26", "rimraf": "~3.0.2", "@module-federation/runtime": "0.9.1"}, "peerDependencies": {"typescript": "^4.9.0 || ^5.0.0", "vue-tsc": ">=1.0.24"}, "peerDependenciesMeta": {"vue-tsc": {"optional": true}}}