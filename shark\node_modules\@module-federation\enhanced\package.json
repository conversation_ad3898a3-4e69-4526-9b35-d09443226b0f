{"name": "@module-federation/enhanced", "version": "0.9.1", "main": "./dist/src/index.js", "types": "./dist/src/index.d.ts", "repository": {"type": "git", "url": "https://github.com/module-federation/core/", "directory": "packages/enhanced"}, "files": ["dist/", "README.md"], "license": "MIT", "publishConfig": {"access": "public"}, "peerDependencies": {"typescript": "^4.9.0 || ^5.0.0", "vue-tsc": ">=1.0.24", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}, "typescript": {"optional": true}, "vue-tsc": {"optional": true}}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./dist/src/index.js", "require": "./dist/src/index.js"}, "./webpack": {"types": "./dist/src/webpack.d.ts", "import": "./dist/src/webpack.js", "require": "./dist/src/webpack.js"}, "./rspack": {"types": "./dist/src/rspack.d.ts", "import": "./dist/src/rspack.js", "require": "./dist/src/rspack.js"}, "./runtime": {"types": "./dist/src/runtime.d.ts", "import": "./dist/src/runtime.js", "require": "./dist/src/runtime.js"}, "./prefetch": {"types": "./dist/src/prefetch.d.ts", "import": "./dist/src/prefetch.js", "require": "./dist/src/prefetch.js"}}, "typesVersions": {"*": {".": ["./dist/src/index.d.ts"], "webpack": ["./dist/src/webpack.d.ts"], "rspack": ["./dist/src/rspack.d.ts"], "runtime": ["./dist/src/runtime.d.ts"], "prefetch": ["./dist/src/prefetch.d.ts"]}}, "devDependencies": {"@types/btoa": "^1.2.5", "ajv": "^8.17.1", "enhanced-resolve": "^5.0.0", "terser": "^5.37.0", "@module-federation/webpack-bundler-runtime": "0.9.1"}, "dependencies": {"btoa": "^1.2.1", "upath": "2.0.1", "@module-federation/bridge-react-webpack-plugin": "0.9.1", "@module-federation/data-prefetch": "0.9.1", "@module-federation/dts-plugin": "0.9.1", "@module-federation/error-codes": "0.9.1", "@module-federation/inject-external-runtime-core-plugin": "0.9.1", "@module-federation/managers": "0.9.1", "@module-federation/manifest": "0.9.1", "@module-federation/rspack": "0.9.1", "@module-federation/runtime-tools": "0.9.1", "@module-federation/sdk": "0.9.1"}}