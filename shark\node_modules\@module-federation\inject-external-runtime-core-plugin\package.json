{"name": "@module-federation/inject-external-runtime-core-plugin", "version": "0.9.1", "license": "MIT", "description": "A sdk for support module federation", "keywords": ["Module Federation", "sdk"], "files": ["dist/", "README.md"], "publishConfig": {"access": "public"}, "author": "z<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "main": "./dist/index.cjs.js", "module": "./dist/index.esm.mjs", "types": "./dist/index.cjs.d.ts", "exports": {".": {"types": "./dist/index.cjs.d.ts", "import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}}, "typesVersions": {"*": {".": ["./dist/index.cjs.d.ts"]}}, "peerDependencies": {"@module-federation/runtime-tools": "0.9.1"}, "devDependencies": {"@module-federation/runtime-tools": "0.9.1"}}