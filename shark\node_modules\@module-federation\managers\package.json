{"name": "@module-federation/managers", "version": "0.9.1", "license": "MIT", "description": "Provide managers for helping handle mf data .", "keywords": ["Module Federation"], "files": ["dist/", "README.md"], "publishConfig": {"access": "public"}, "author": "hanric <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/module-federation/core/", "directory": "packages/managers"}, "sideEffects": false, "main": "./dist/index.cjs.js", "module": "./dist/index.esm.js", "types": "./dist/index.cjs.d.ts", "dependencies": {"find-pkg": "2.0.0", "fs-extra": "9.1.0", "@module-federation/sdk": "0.9.1"}, "devDependencies": {"webpack": "5.93.0"}, "exports": {".": {"types": "./dist/index.cjs.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.cjs.js"}}, "typesVersions": {"*": {".": ["./dist/index.cjs.d.ts"]}}}