{"version": 3, "sources": ["../../../src/lib/typescript/getTypeScriptIntent.ts"], "sourcesContent": ["import { existsSync, promises as fs } from 'fs'\nimport path from 'path'\nimport { recursiveReadDir } from '../recursive-readdir'\n\nexport type TypeScriptIntent = { firstTimeSetup: boolean }\n\nexport async function getTypeScriptIntent(\n  baseDir: string,\n  intentDirs: string[],\n  tsconfigPath: string\n): Promise<TypeScriptIntent | false> {\n  const resolvedTsConfigPath = path.join(baseDir, tsconfigPath)\n\n  // The integration turns on if we find a `tsconfig.json` in the user's\n  // project.\n  const hasTypeScriptConfiguration = existsSync(resolvedTsConfigPath)\n  if (hasTypeScriptConfiguration) {\n    const content = await fs\n      .readFile(resolvedTsConfigPath, { encoding: 'utf8' })\n      .then(\n        (txt) => txt.trim(),\n        () => null\n      )\n    return { firstTimeSetup: content === '' || content === '{}' }\n  }\n\n  // Next.js also offers a friendly setup mode that bootstraps a TypeScript\n  // project for the user when we detect TypeScript files. So, we need to check\n  // the `pages/` directory for a TypeScript file.\n  // Checking all directories is too slow, so this is a happy medium.\n  const tsFilesRegex = /.*\\.(ts|tsx)$/\n  const excludedRegex = /(node_modules|.*\\.d\\.ts$)/\n  for (const dir of intentDirs) {\n    const typescriptFiles = await recursiveReadDir(dir, {\n      pathnameFilter: (name) => tsFilesRegex.test(name),\n      ignoreFilter: (name) => excludedRegex.test(name),\n    })\n    if (typescriptFiles.length) {\n      return { firstTimeSetup: true }\n    }\n  }\n\n  return false\n}\n"], "names": ["getTypeScriptIntent", "baseDir", "intentDirs", "tsconfigPath", "resolvedTsConfigPath", "path", "join", "hasTypeScriptConfiguration", "existsSync", "content", "fs", "readFile", "encoding", "then", "txt", "trim", "firstTimeSetup", "tsFilesRegex", "excludedRegex", "dir", "typescriptFiles", "recursiveReadDir", "pathnameFilter", "name", "test", "ignoreFilter", "length"], "mappings": ";;;;+BAMsBA;;;eAAAA;;;oBANqB;6DAC1B;kCACgB;;;;;;AAI1B,eAAeA,oBACpBC,OAAe,EACfC,UAAoB,EACpBC,YAAoB;IAEpB,MAAMC,uBAAuBC,aAAI,CAACC,IAAI,CAACL,SAASE;IAEhD,sEAAsE;IACtE,WAAW;IACX,MAAMI,6BAA6BC,IAAAA,cAAU,EAACJ;IAC9C,IAAIG,4BAA4B;QAC9B,MAAME,UAAU,MAAMC,YAAE,CACrBC,QAAQ,CAACP,sBAAsB;YAAEQ,UAAU;QAAO,GAClDC,IAAI,CACH,CAACC,MAAQA,IAAIC,IAAI,IACjB,IAAM;QAEV,OAAO;YAAEC,gBAAgBP,YAAY,MAAMA,YAAY;QAAK;IAC9D;IAEA,yEAAyE;IACzE,6EAA6E;IAC7E,gDAAgD;IAChD,mEAAmE;IACnE,MAAMQ,eAAe;IACrB,MAAMC,gBAAgB;IACtB,KAAK,MAAMC,OAAOjB,WAAY;QAC5B,MAAMkB,kBAAkB,MAAMC,IAAAA,kCAAgB,EAACF,KAAK;YAClDG,gBAAgB,CAACC,OAASN,aAAaO,IAAI,CAACD;YAC5CE,cAAc,CAACF,OAASL,cAAcM,IAAI,CAACD;QAC7C;QACA,IAAIH,gBAAgBM,MAAM,EAAE;YAC1B,OAAO;gBAAEV,gBAAgB;YAAK;QAChC;IACF;IAEA,OAAO;AACT"}