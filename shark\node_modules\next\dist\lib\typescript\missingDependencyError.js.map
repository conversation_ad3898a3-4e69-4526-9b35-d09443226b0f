{"version": 3, "sources": ["../../../src/lib/typescript/missingDependencyError.ts"], "sourcesContent": ["import { bold, cyan, red } from '../picocolors'\n\nimport { getOxfordCommaList } from '../oxford-comma-list'\nimport type { MissingDependency } from '../has-necessary-dependencies'\nimport { FatalError } from '../fatal-error'\nimport { getPkgManager } from '../helpers/get-pkg-manager'\n\nexport function missingDepsError(\n  dir: string,\n  missingPackages: MissingDependency[]\n): never {\n  const packagesHuman = getOxfordCommaList(missingPackages.map((p) => p.pkg))\n  const packagesCli = missingPackages.map((p) => p.pkg).join(' ')\n  const packageManager = getPkgManager(dir)\n\n  const removalMsg =\n    '\\n\\n' +\n    bold(\n      'If you are not trying to use TypeScript, please remove the ' +\n        cyan('tsconfig.json') +\n        ' file from your package root (and any TypeScript files in your app and pages directories).'\n    )\n\n  throw new FatalError(\n    bold(\n      red(\n        `It looks like you're trying to use TypeScript but do not have the required package(s) installed.`\n      )\n    ) +\n      '\\n\\n' +\n      bold(`Please install ${bold(packagesHuman)} by running:`) +\n      '\\n\\n' +\n      `\\t${bold(\n        cyan(\n          (packageManager === 'yarn'\n            ? 'yarn add --dev'\n            : packageManager === 'pnpm'\n              ? 'pnpm install --save-dev'\n              : 'npm install --save-dev') +\n            ' ' +\n            packagesCli\n        )\n      )}` +\n      removalMsg +\n      '\\n'\n  )\n}\n"], "names": ["missingDepsError", "dir", "missingPackages", "packagesHuman", "getOxfordCommaList", "map", "p", "pkg", "packagesCli", "join", "packageManager", "getPkgManager", "removalMsg", "bold", "cyan", "FatalE<PERSON>r", "red"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;4BAPgB;iCAEG;4BAER;+BACG;AAEvB,SAASA,iBACdC,GAAW,EACXC,eAAoC;IAEpC,MAAMC,gBAAgBC,IAAAA,mCAAkB,EAACF,gBAAgBG,GAAG,CAAC,CAACC,IAAMA,EAAEC,GAAG;IACzE,MAAMC,cAAcN,gBAAgBG,GAAG,CAAC,CAACC,IAAMA,EAAEC,GAAG,EAAEE,IAAI,CAAC;IAC3D,MAAMC,iBAAiBC,IAAAA,4BAAa,EAACV;IAErC,MAAMW,aACJ,SACAC,IAAAA,gBAAI,EACF,gEACEC,IAAAA,gBAAI,EAAC,mBACL;IAGN,MAAM,qBAsBL,CAtBK,IAAIC,sBAAU,CAClBF,IAAAA,gBAAI,EACFG,IAAAA,eAAG,EACD,CAAC,gGAAgG,CAAC,KAGpG,SACAH,IAAAA,gBAAI,EAAC,CAAC,eAAe,EAAEA,IAAAA,gBAAI,EAACV,eAAe,YAAY,CAAC,IACxD,SACA,CAAC,EAAE,EAAEU,IAAAA,gBAAI,EACPC,IAAAA,gBAAI,EACF,AAACJ,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACjB,4BACA,wBAAuB,IAC3B,MACAF,eAEH,GACHI,aACA,OArBE,qBAAA;eAAA;oBAAA;sBAAA;IAsBN;AACF"}