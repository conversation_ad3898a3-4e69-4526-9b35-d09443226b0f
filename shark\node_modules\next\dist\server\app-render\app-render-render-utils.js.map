{"version": 3, "sources": ["../../../src/server/app-render/app-render-render-utils.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\n\n/**\n * This is a utility function to make scheduling sequential tasks that run back to back easier.\n * We schedule on the same queue (setImmediate) at the same time to ensure no other events can sneak in between.\n */\nexport function scheduleInSequentialTasks<R>(\n  render: () => R | Promise<R>,\n  followup: () => void\n): Promise<R> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`scheduleInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let pendingResult: R | Promise<R>\n      setImmediate(() => {\n        try {\n          pendingResult = render()\n        } catch (err) {\n          reject(err)\n        }\n      })\n      setImmediate(() => {\n        followup()\n        resolve(pendingResult)\n      })\n    })\n  }\n}\n"], "names": ["scheduleInSequentialTasks", "render", "followup", "process", "env", "NEXT_RUNTIME", "InvariantError", "Promise", "resolve", "reject", "pendingResult", "setImmediate", "err"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;gCANe;AAMxB,SAASA,0BACdC,MAA4B,EAC5BC,QAAoB;IAEpB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,sEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIC,QAAQ,CAACC,SAASC;YAC3B,IAAIC;YACJC,aAAa;gBACX,IAAI;oBACFD,gBAAgBT;gBAClB,EAAE,OAAOW,KAAK;oBACZH,OAAOG;gBACT;YACF;YACAD,aAAa;gBACXT;gBACAM,QAAQE;YACV;QACF;IACF;AACF"}