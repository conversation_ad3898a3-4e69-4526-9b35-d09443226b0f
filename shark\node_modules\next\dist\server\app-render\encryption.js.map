{"version": 3, "sources": ["../../../src/server/app-render/encryption.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nimport 'server-only'\n\n/* eslint-disable import/no-extraneous-dependencies */\nimport { renderToReadableStream } from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport { createFromReadableStream } from 'react-server-dom-webpack/client.edge'\n\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport {\n  arrayBufferToString,\n  decrypt,\n  encrypt,\n  getActionEncryptionKey,\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n  stringToUint8Array,\n} from './encryption-utils'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { createHangingInputAbortSignal } from './dynamic-rendering'\nimport React from 'react'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst textEncoder = new TextEncoder()\nconst textDecoder = new TextDecoder()\n\n/**\n * Decrypt the serialized string with the action id as the salt.\n */\nasync function decodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (typeof key === 'undefined') {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get the iv (16 bytes) and the payload from the arg.\n  const originalPayload = atob(arg)\n  const ivValue = originalPayload.slice(0, 16)\n  const payload = originalPayload.slice(16)\n\n  const decrypted = textDecoder.decode(\n    await decrypt(key, stringToUint8Array(ivValue), stringToUint8Array(payload))\n  )\n\n  if (!decrypted.startsWith(actionId)) {\n    throw new Error('Invalid Server Action payload: failed to decrypt.')\n  }\n\n  return decrypted.slice(actionId.length)\n}\n\n/**\n * Encrypt the serialized string with the action id as the salt. Add a prefix to\n * later ensure that the payload is correctly decrypted, similar to a checksum.\n */\nasync function encodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (key === undefined) {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get 16 random bytes as iv.\n  const randomBytes = new Uint8Array(16)\n  workUnitAsyncStorage.exit(() => crypto.getRandomValues(randomBytes))\n  const ivValue = arrayBufferToString(randomBytes.buffer)\n\n  const encrypted = await encrypt(\n    key,\n    randomBytes,\n    textEncoder.encode(actionId + arg)\n  )\n\n  return btoa(ivValue + arrayBufferToString(encrypted))\n}\n\n// Encrypts the action's bound args into a string. For the same combination of\n// actionId and args the same cached promise is returned. This ensures reference\n// equality for returned objects from \"use cache\" functions when they're invoked\n// multiple times within one render pass using the same bound args.\nexport const encryptActionBoundArgs = React.cache(\n  async function encryptActionBoundArgs(actionId: string, ...args: any[]) {\n    const { clientModules } = getClientReferenceManifestForRsc()\n\n    // Create an error before any asynchronous calls, to capture the original\n    // call stack in case we need it when the serialization errors.\n    const error = new Error()\n    Error.captureStackTrace(error, encryptActionBoundArgs)\n\n    let didCatchError = false\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    const hangingInputAbortSignal =\n      workUnitStore?.type === 'prerender'\n        ? createHangingInputAbortSignal(workUnitStore)\n        : undefined\n\n    // Using Flight to serialize the args into a string.\n    const serialized = await streamToString(\n      renderToReadableStream(args, clientModules, {\n        signal: hangingInputAbortSignal,\n        onError(err) {\n          if (hangingInputAbortSignal?.aborted) {\n            return\n          }\n\n          // We're only reporting one error at a time, starting with the first.\n          if (didCatchError) {\n            return\n          }\n\n          didCatchError = true\n\n          // Use the original error message together with the previously created\n          // stack, because err.stack is a useless Flight Server call stack.\n          error.message = err instanceof Error ? err.message : String(err)\n        },\n      }),\n      // We pass the abort signal to `streamToString` so that no chunks are\n      // included that are emitted after the signal was already aborted. This\n      // ensures that we can encode hanging promises.\n      hangingInputAbortSignal\n    )\n\n    if (didCatchError) {\n      if (process.env.NODE_ENV === 'development') {\n        // Logging the error is needed for server functions that are passed to the\n        // client where the decryption is not done during rendering. Console\n        // replaying allows us to still show the error dev overlay in this case.\n        console.error(error)\n      }\n\n      throw error\n    }\n\n    if (!workUnitStore) {\n      return encodeActionBoundArg(actionId, serialized)\n    }\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n    const cacheKey = actionId + serialized\n\n    const cachedEncrypted =\n      prerenderResumeDataCache?.encryptedBoundArgs.get(cacheKey) ??\n      renderResumeDataCache?.encryptedBoundArgs.get(cacheKey)\n\n    if (cachedEncrypted) {\n      return cachedEncrypted\n    }\n\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    cacheSignal?.beginRead()\n\n    const encrypted = await encodeActionBoundArg(actionId, serialized)\n\n    cacheSignal?.endRead()\n    prerenderResumeDataCache?.encryptedBoundArgs.set(cacheKey, encrypted)\n\n    return encrypted\n  }\n)\n\n// Decrypts the action's bound args from the encrypted string.\nexport async function decryptActionBoundArgs(\n  actionId: string,\n  encryptedPromise: Promise<string>\n) {\n  const encrypted = await encryptedPromise\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  let decrypted: string | undefined\n\n  if (workUnitStore) {\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n\n    decrypted =\n      prerenderResumeDataCache?.decryptedBoundArgs.get(encrypted) ??\n      renderResumeDataCache?.decryptedBoundArgs.get(encrypted)\n\n    if (!decrypted) {\n      cacheSignal?.beginRead()\n      decrypted = await decodeActionBoundArg(actionId, encrypted)\n      cacheSignal?.endRead()\n      prerenderResumeDataCache?.decryptedBoundArgs.set(encrypted, decrypted)\n    }\n  } else {\n    decrypted = await decodeActionBoundArg(actionId, encrypted)\n  }\n\n  const { edgeRscModuleMapping, rscModuleMapping } =\n    getClientReferenceManifestForRsc()\n\n  // Using Flight to deserialize the args from the string.\n  const deserialized = await createFromReadableStream(\n    new ReadableStream({\n      start(controller) {\n        controller.enqueue(textEncoder.encode(decrypted))\n\n        if (workUnitStore?.type === 'prerender') {\n          // Explicitly don't close the stream here (until prerendering is\n          // complete) so that hanging promises are not rejected.\n          if (workUnitStore.renderSignal.aborted) {\n            controller.close()\n          } else {\n            workUnitStore.renderSignal.addEventListener(\n              'abort',\n              () => controller.close(),\n              { once: true }\n            )\n          }\n        } else {\n          controller.close()\n        }\n      },\n    }),\n    {\n      serverConsumerManifest: {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the current execution. Instead, we'll wait for any ClientReference\n        // to be emitted which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime ? edgeRscModuleMapping : rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      },\n    }\n  )\n\n  return deserialized\n}\n"], "names": ["decryptActionBoundArgs", "encryptActionBoundArgs", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "textEncoder", "TextEncoder", "textDecoder", "TextDecoder", "decodeActionBoundArg", "actionId", "arg", "key", "getActionEncryptionKey", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "decrypted", "decode", "decrypt", "stringToUint8Array", "startsWith", "length", "encodeActionBoundArg", "undefined", "randomBytes", "Uint8Array", "workUnitAsyncStorage", "exit", "crypto", "getRandomValues", "arrayBufferToString", "buffer", "encrypted", "encrypt", "encode", "btoa", "React", "cache", "args", "clientModules", "getClientReferenceManifestForRsc", "error", "captureStackTrace", "didCatchError", "workUnitStore", "getStore", "hangingInputAbortSignal", "type", "createHangingInputAbortSignal", "serialized", "streamToString", "renderToReadableStream", "signal", "onError", "err", "aborted", "message", "String", "NODE_ENV", "console", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "renderResumeDataCache", "getRenderResumeDataCache", "cache<PERSON>ey", "cachedEncrypted", "encryptedBoundArgs", "get", "cacheSignal", "beginRead", "endRead", "set", "encryptedPromise", "decryptedBoundArgs", "edgeRscModuleMapping", "rscModuleMapping", "deserialized", "createFromReadableStream", "ReadableStream", "start", "controller", "enqueue", "renderSignal", "close", "addEventListener", "once", "serverConsumerManifest", "moduleLoading", "moduleMap", "serverModuleMap", "getServerModuleMap"], "mappings": "AAAA,oDAAoD;;;;;;;;;;;;;;;IA+K9BA,sBAAsB;eAAtBA;;IAvFTC,sBAAsB;eAAtBA;;;QAvFN;4BAGgC;4BAEE;sCAEV;iCASxB;8CAKA;kCACuC;8DAC5B;;;;;;AAElB,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,cAAc,IAAIC;AACxB,MAAMC,cAAc,IAAIC;AAExB;;CAEC,GACD,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,IAAAA,uCAAsB;IACxC,IAAI,OAAOD,QAAQ,aAAa;QAC9B,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKL;IAC7B,MAAMM,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IAEtC,MAAME,YAAYb,YAAYc,MAAM,CAClC,MAAMC,IAAAA,wBAAO,EAACV,KAAKW,IAAAA,mCAAkB,EAACN,UAAUM,IAAAA,mCAAkB,EAACJ;IAGrE,IAAI,CAACC,UAAUI,UAAU,CAACd,WAAW;QACnC,MAAM,qBAA8D,CAA9D,IAAII,MAAM,sDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;IACrE;IAEA,OAAOM,UAAUF,KAAK,CAACR,SAASe,MAAM;AACxC;AAEA;;;CAGC,GACD,eAAeC,qBAAqBhB,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,IAAAA,uCAAsB;IACxC,IAAID,QAAQe,WAAW;QACrB,MAAM,qBAEL,CAFK,IAAIb,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,6BAA6B;IAC7B,MAAMc,cAAc,IAAIC,WAAW;IACnCC,kDAAoB,CAACC,IAAI,CAAC,IAAMC,OAAOC,eAAe,CAACL;IACvD,MAAMX,UAAUiB,IAAAA,oCAAmB,EAACN,YAAYO,MAAM;IAEtD,MAAMC,YAAY,MAAMC,IAAAA,wBAAO,EAC7BzB,KACAgB,aACAvB,YAAYiC,MAAM,CAAC5B,WAAWC;IAGhC,OAAO4B,KAAKtB,UAAUiB,IAAAA,oCAAmB,EAACE;AAC5C;AAMO,MAAMpC,yBAAyBwC,cAAK,CAACC,KAAK,CAC/C,eAAezC,uBAAuBU,QAAgB,EAAE,GAAGgC,IAAW;IACpE,MAAM,EAAEC,aAAa,EAAE,GAAGC,IAAAA,iDAAgC;IAE1D,yEAAyE;IACzE,+DAA+D;IAC/D,MAAMC,QAAQ,IAAI/B;IAClBA,MAAMgC,iBAAiB,CAACD,OAAO7C;IAE/B,IAAI+C,gBAAgB;IAEpB,MAAMC,gBAAgBlB,kDAAoB,CAACmB,QAAQ;IAEnD,MAAMC,0BACJF,CAAAA,iCAAAA,cAAeG,IAAI,MAAK,cACpBC,IAAAA,+CAA6B,EAACJ,iBAC9BrB;IAEN,oDAAoD;IACpD,MAAM0B,aAAa,MAAMC,IAAAA,oCAAc,EACrCC,IAAAA,kCAAsB,EAACb,MAAMC,eAAe;QAC1Ca,QAAQN;QACRO,SAAQC,GAAG;YACT,IAAIR,2CAAAA,wBAAyBS,OAAO,EAAE;gBACpC;YACF;YAEA,qEAAqE;YACrE,IAAIZ,eAAe;gBACjB;YACF;YAEAA,gBAAgB;YAEhB,sEAAsE;YACtE,kEAAkE;YAClEF,MAAMe,OAAO,GAAGF,eAAe5C,QAAQ4C,IAAIE,OAAO,GAAGC,OAAOH;QAC9D;IACF,IACA,qEAAqE;IACrE,uEAAuE;IACvE,+CAA+C;IAC/CR;IAGF,IAAIH,eAAe;QACjB,IAAI7C,QAAQC,GAAG,CAAC2D,QAAQ,KAAK,eAAe;YAC1C,0EAA0E;YAC1E,oEAAoE;YACpE,wEAAwE;YACxEC,QAAQlB,KAAK,CAACA;QAChB;QAEA,MAAMA;IACR;IAEA,IAAI,CAACG,eAAe;QAClB,OAAOtB,qBAAqBhB,UAAU2C;IACxC;IAEA,MAAMW,2BAA2BC,IAAAA,yDAA2B,EAACjB;IAC7D,MAAMkB,wBAAwBC,IAAAA,sDAAwB,EAACnB;IACvD,MAAMoB,WAAW1D,WAAW2C;IAE5B,MAAMgB,kBACJL,CAAAA,4CAAAA,yBAA0BM,kBAAkB,CAACC,GAAG,CAACH,eACjDF,yCAAAA,sBAAuBI,kBAAkB,CAACC,GAAG,CAACH;IAEhD,IAAIC,iBAAiB;QACnB,OAAOA;IACT;IAEA,MAAMG,cACJxB,cAAcG,IAAI,KAAK,cAAcH,cAAcwB,WAAW,GAAG7C;IAEnE6C,+BAAAA,YAAaC,SAAS;IAEtB,MAAMrC,YAAY,MAAMV,qBAAqBhB,UAAU2C;IAEvDmB,+BAAAA,YAAaE,OAAO;IACpBV,4CAAAA,yBAA0BM,kBAAkB,CAACK,GAAG,CAACP,UAAUhC;IAE3D,OAAOA;AACT;AAIK,eAAerC,uBACpBW,QAAgB,EAChBkE,gBAAiC;IAEjC,MAAMxC,YAAY,MAAMwC;IACxB,MAAM5B,gBAAgBlB,kDAAoB,CAACmB,QAAQ;IAEnD,IAAI7B;IAEJ,IAAI4B,eAAe;QACjB,MAAMwB,cACJxB,cAAcG,IAAI,KAAK,cAAcH,cAAcwB,WAAW,GAAG7C;QAEnE,MAAMqC,2BAA2BC,IAAAA,yDAA2B,EAACjB;QAC7D,MAAMkB,wBAAwBC,IAAAA,sDAAwB,EAACnB;QAEvD5B,YACE4C,CAAAA,4CAAAA,yBAA0Ba,kBAAkB,CAACN,GAAG,CAACnC,gBACjD8B,yCAAAA,sBAAuBW,kBAAkB,CAACN,GAAG,CAACnC;QAEhD,IAAI,CAAChB,WAAW;YACdoD,+BAAAA,YAAaC,SAAS;YACtBrD,YAAY,MAAMX,qBAAqBC,UAAU0B;YACjDoC,+BAAAA,YAAaE,OAAO;YACpBV,4CAAAA,yBAA0Ba,kBAAkB,CAACF,GAAG,CAACvC,WAAWhB;QAC9D;IACF,OAAO;QACLA,YAAY,MAAMX,qBAAqBC,UAAU0B;IACnD;IAEA,MAAM,EAAE0C,oBAAoB,EAAEC,gBAAgB,EAAE,GAC9CnC,IAAAA,iDAAgC;IAElC,wDAAwD;IACxD,MAAMoC,eAAe,MAAMC,IAAAA,oCAAwB,EACjD,IAAIC,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAAChF,YAAYiC,MAAM,CAAClB;YAEtC,IAAI4B,CAAAA,iCAAAA,cAAeG,IAAI,MAAK,aAAa;gBACvC,gEAAgE;gBAChE,uDAAuD;gBACvD,IAAIH,cAAcsC,YAAY,CAAC3B,OAAO,EAAE;oBACtCyB,WAAWG,KAAK;gBAClB,OAAO;oBACLvC,cAAcsC,YAAY,CAACE,gBAAgB,CACzC,SACA,IAAMJ,WAAWG,KAAK,IACtB;wBAAEE,MAAM;oBAAK;gBAEjB;YACF,OAAO;gBACLL,WAAWG,KAAK;YAClB;QACF;IACF,IACA;QACEG,wBAAwB;YACtB,2FAA2F;YAC3F,oFAAoF;YACpF,6DAA6D;YAC7DC,eAAe;YACfC,WAAW3F,gBAAgB6E,uBAAuBC;YAClDc,iBAAiBC,IAAAA,mCAAkB;QACrC;IACF;IAGF,OAAOd;AACT"}