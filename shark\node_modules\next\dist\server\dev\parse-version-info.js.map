{"version": 3, "sources": ["../../../src/server/dev/parse-version-info.ts"], "sourcesContent": ["import * as semver from 'next/dist/compiled/semver'\n\nexport interface VersionInfo {\n  installed: string\n  staleness:\n    | 'fresh'\n    | 'stale-patch'\n    | 'stale-minor'\n    | 'stale-major'\n    | 'stale-prerelease'\n    | 'newer-than-npm'\n    | 'unknown'\n  expected?: string\n}\n\nexport function parseVersionInfo(o: {\n  installed: string\n  latest: string\n  canary: string\n}): VersionInfo {\n  const latest = semver.parse(o.latest)\n  const canary = semver.parse(o.canary)\n  const installedParsed = semver.parse(o.installed)\n  const installed = o.installed\n  if (installedParsed && latest && canary) {\n    if (installedParsed.major < latest.major) {\n      // Old major version\n      return { staleness: 'stale-major', expected: latest.raw, installed }\n    } else if (\n      installedParsed.prerelease[0] === 'canary' &&\n      semver.lt(installedParsed, canary)\n    ) {\n      // Matching major, but old canary\n      return {\n        staleness: 'stale-prerelease',\n        expected: canary.raw,\n        installed,\n      }\n    } else if (\n      !installedParsed.prerelease.length &&\n      semver.lt(installedParsed, latest)\n    ) {\n      // Stable, but not the latest\n      if (installedParsed.minor === latest.minor) {\n        // Same major and minor, but not the latest patch\n        return {\n          staleness: 'stale-patch',\n          expected: latest.raw,\n          installed,\n        }\n      }\n      return { staleness: 'stale-minor', expected: latest.raw, installed }\n    } else if (\n      semver.gt(installedParsed, latest) &&\n      installedParsed.version !== canary.version\n    ) {\n      // Newer major version\n      return { staleness: 'newer-than-npm', installed }\n    } else {\n      // Latest and greatest\n      return { staleness: 'fresh', installed }\n    }\n  }\n\n  return {\n    installed: installedParsed?.raw ?? '0.0.0',\n    staleness: 'unknown',\n  }\n}\n"], "names": ["parseVersionInfo", "o", "latest", "semver", "parse", "canary", "installedParsed", "installed", "major", "staleness", "expected", "raw", "prerelease", "lt", "length", "minor", "gt", "version"], "mappings": ";;;;+BAegBA;;;eAAAA;;;gEAfQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAejB,SAASA,iBAAiBC,CAIhC;IACC,MAAMC,SAASC,QAAOC,KAAK,CAACH,EAAEC,MAAM;IACpC,MAAMG,SAASF,QAAOC,KAAK,CAACH,EAAEI,MAAM;IACpC,MAAMC,kBAAkBH,QAAOC,KAAK,CAACH,EAAEM,SAAS;IAChD,MAAMA,YAAYN,EAAEM,SAAS;IAC7B,IAAID,mBAAmBJ,UAAUG,QAAQ;QACvC,IAAIC,gBAAgBE,KAAK,GAAGN,OAAOM,KAAK,EAAE;YACxC,oBAAoB;YACpB,OAAO;gBAAEC,WAAW;gBAAeC,UAAUR,OAAOS,GAAG;gBAAEJ;YAAU;QACrE,OAAO,IACLD,gBAAgBM,UAAU,CAAC,EAAE,KAAK,YAClCT,QAAOU,EAAE,CAACP,iBAAiBD,SAC3B;YACA,iCAAiC;YACjC,OAAO;gBACLI,WAAW;gBACXC,UAAUL,OAAOM,GAAG;gBACpBJ;YACF;QACF,OAAO,IACL,CAACD,gBAAgBM,UAAU,CAACE,MAAM,IAClCX,QAAOU,EAAE,CAACP,iBAAiBJ,SAC3B;YACA,6BAA6B;YAC7B,IAAII,gBAAgBS,KAAK,KAAKb,OAAOa,KAAK,EAAE;gBAC1C,iDAAiD;gBACjD,OAAO;oBACLN,WAAW;oBACXC,UAAUR,OAAOS,GAAG;oBACpBJ;gBACF;YACF;YACA,OAAO;gBAAEE,WAAW;gBAAeC,UAAUR,OAAOS,GAAG;gBAAEJ;YAAU;QACrE,OAAO,IACLJ,QAAOa,EAAE,CAACV,iBAAiBJ,WAC3BI,gBAAgBW,OAAO,KAAKZ,OAAOY,OAAO,EAC1C;YACA,sBAAsB;YACtB,OAAO;gBAAER,WAAW;gBAAkBF;YAAU;QAClD,OAAO;YACL,sBAAsB;YACtB,OAAO;gBAAEE,WAAW;gBAASF;YAAU;QACzC;IACF;IAEA,OAAO;QACLA,WAAWD,CAAAA,mCAAAA,gBAAiBK,GAAG,KAAI;QACnCF,WAAW;IACb;AACF"}