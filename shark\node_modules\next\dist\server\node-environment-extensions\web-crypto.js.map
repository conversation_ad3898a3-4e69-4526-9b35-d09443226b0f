{"version": 3, "sources": ["../../../src/server/node-environment-extensions/web-crypto.tsx"], "sourcesContent": ["/**\n * We extend Web Crypto APIs during builds and revalidates to ensure that prerenders don't observe random bytes\n * When dynamicIO is enabled. Random bytes are a form of IO even if they resolve synchronously. When dyanmicIO is\n * enabled we need to ensure that random bytes are excluded from prerenders unless they are cached.\n *\n *\n * The extensions here never error nor alter the underlying return values and thus should be transparent to callers.\n */\n\nimport { io } from './utils'\n\nlet webCrypto: typeof crypto\nif (process.env.NEXT_RUNTIME === 'edge') {\n  webCrypto = crypto\n} else {\n  if (typeof crypto === 'undefined') {\n    webCrypto = require('node:crypto').webcrypto\n  } else {\n    webCrypto = crypto\n  }\n}\n\nconst getRandomValuesExpression = '`crypto.getRandomValues()`'\ntry {\n  const _getRandomValues = webCrypto.getRandomValues\n  webCrypto.getRandomValues = function getRandomValues() {\n    io(getRandomValuesExpression, 'crypto')\n    return _getRandomValues.apply(webCrypto, arguments as any)\n  }\n} catch {\n  console.error(\n    `Failed to install ${getRandomValuesExpression} extension. When using \\`experimental.dynamicIO\\` calling this function will not correctly trigger dynamic behavior.`\n  )\n}\n\nconst randomUUIDExpression = '`crypto.randomUUID()`'\ntry {\n  const _randomUUID = webCrypto.randomUUID\n  webCrypto.randomUUID = function randomUUID() {\n    io(randomUUIDExpression, 'crypto')\n    return _randomUUID.apply(webCrypto, arguments as any)\n  } as typeof _randomUUID\n} catch {\n  console.error(\n    `Failed to install ${getRandomValuesExpression} extension. When using \\`experimental.dynamicIO\\` calling this function will not correctly trigger dynamic behavior.`\n  )\n}\n"], "names": ["webCrypto", "process", "env", "NEXT_RUNTIME", "crypto", "require", "webcrypto", "getRandomValuesExpression", "_getRandom<PERSON><PERSON><PERSON>", "getRandomValues", "io", "apply", "arguments", "console", "error", "randomUUIDExpression", "_randomUUID", "randomUUID"], "mappings": "AAAA;;;;;;;CAOC;;;;uBAEkB;AAEnB,IAAIA;AACJ,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,YAAYI;AACd,OAAO;IACL,IAAI,OAAOA,WAAW,aAAa;QACjCJ,YAAYK,QAAQ,eAAeC,SAAS;IAC9C,OAAO;QACLN,YAAYI;IACd;AACF;AAEA,MAAMG,4BAA4B;AAClC,IAAI;IACF,MAAMC,mBAAmBR,UAAUS,eAAe;IAClDT,UAAUS,eAAe,GAAG,SAASA;QACnCC,IAAAA,SAAE,EAACH,2BAA2B;QAC9B,OAAOC,iBAAiBG,KAAK,CAACX,WAAWY;IAC3C;AACF,EAAE,OAAM;IACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEP,0BAA0B,oHAAoH,CAAC;AAExK;AAEA,MAAMQ,uBAAuB;AAC7B,IAAI;IACF,MAAMC,cAAchB,UAAUiB,UAAU;IACxCjB,UAAUiB,UAAU,GAAG,SAASA;QAC9BP,IAAAA,SAAE,EAACK,sBAAsB;QACzB,OAAOC,YAAYL,KAAK,CAACX,WAAWY;IACtC;AACF,EAAE,OAAM;IACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEP,0BAA0B,oHAAoH,CAAC;AAExK"}