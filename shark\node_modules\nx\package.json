{"name": "nx", "version": "21.2.1", "private": false, "description": "The core Nx plugin contains the core functionality of Nx like the project graph, nx commands and task orchestration.", "repository": {"type": "git", "url": "https://github.com/nrwl/nx.git", "directory": "packages/nx"}, "keywords": ["Monore<PERSON>", "Angular", "React", "Web", "Node", "Nest", "Jest", "Cypress", "CLI", "Testing", "Front-end", "Backend", "Mobile"], "bin": {"nx": "./bin/nx.js", "nx-cloud": "./bin/nx-cloud.js"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nrwl/nx/issues"}, "homepage": "https://nx.dev", "dependencies": {"@napi-rs/wasm-runtime": "0.2.4", "@yarnpkg/lockfile": "^1.1.0", "@yarnpkg/parsers": "3.0.2", "@zkochan/js-yaml": "0.0.7", "axios": "^1.8.3", "chalk": "^4.1.0", "cli-cursor": "3.1.0", "cli-spinners": "2.6.1", "cliui": "^8.0.1", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "enquirer": "~2.3.6", "figures": "3.2.0", "flat": "^5.0.2", "front-matter": "^4.0.2", "ignore": "^5.0.4", "jest-diff": "^29.4.1", "jsonc-parser": "3.2.0", "lines-and-columns": "2.0.3", "minimatch": "9.0.3", "node-machine-id": "1.1.12", "npm-run-path": "^4.0.1", "open": "^8.4.0", "ora": "5.3.0", "resolve.exports": "2.0.3", "semver": "^7.5.3", "string-width": "^4.2.3", "tar-stream": "~2.2.0", "tmp": "~0.2.1", "tree-kill": "^1.2.2", "tsconfig-paths": "^4.1.2", "tslib": "^2.3.0", "yaml": "^2.6.0", "yargs": "^17.6.2", "yargs-parser": "21.1.1"}, "peerDependencies": {"@swc-node/register": "^1.8.0", "@swc/core": "^1.3.85"}, "peerDependenciesMeta": {"@swc-node/register": {"optional": true}, "@swc/core": {"optional": true}}, "optionalDependencies": {"@nx/nx-darwin-arm64": "21.2.1", "@nx/nx-darwin-x64": "21.2.1", "@nx/nx-freebsd-x64": "21.2.1", "@nx/nx-linux-arm-gnueabihf": "21.2.1", "@nx/nx-linux-arm64-gnu": "21.2.1", "@nx/nx-linux-arm64-musl": "21.2.1", "@nx/nx-linux-x64-gnu": "21.2.1", "@nx/nx-linux-x64-musl": "21.2.1", "@nx/nx-win32-arm64-msvc": "21.2.1", "@nx/nx-win32-x64-msvc": "21.2.1"}, "nx-migrations": {"migrations": "./migrations.json", "packageGroup": ["@nx/js", "@nx/jest", "@nx/linter", "@nx/eslint", "@nx/workspace", "@nx/angular", "@nx/cypress", "@nx/detox", "@nx/devkit", "@nx/esbuild", "@nx/eslint-plugin", "@nx/expo", "@nx/express", "@nx/gradle", "@nx/module-federation", "@nx/nest", "@nx/next", "@nx/node", "@nx/nuxt", "@nx/playwright", "@nx/plugin", "@nx/react", "@nx/react-native", "@nx/rollup", "@nx/remix", "@nx/rspack", "@nx/rsbuild", "@nx/storybook", "@nx/vite", "@nx/vue", "@nx/web", "@nx/webpack", {"package": "nx-cloud", "version": "latest"}, {"package": "@nrwl/nx-cloud", "version": "latest"}]}, "generators": "./generators.json", "executors": "./executors.json", "builders": "./executors.json", "publishConfig": {"access": "public"}, "napi": {"binaryName": "nx", "packageName": "@nx/nx", "wasm": {"initialMemory": 1024, "maximumMemory": 32768}, "targets": ["x86_64-unknown-linux-gnu", "x86_64-pc-windows-msvc", "x86_64-apple-darwin", "aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "aarch64-pc-windows-msvc", "armv7-unknown-linux-gnueabihf", "x86_64-unknown-linux-musl", "x86_64-unknown-freebsd"]}, "types": "./bin/nx.d.ts", "main": "./bin/nx.js", "type": "commonjs", "scripts": {"postinstall": "node ./bin/post-install"}}