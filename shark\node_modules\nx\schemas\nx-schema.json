{"$schema": "http://json-schema.org/draft-07/schema", "$id": "https://nx.dev/reference/nx-json", "title": "JSON schema for Nx configuration", "type": "object", "properties": {"implicitDependencies": {"type": "object", "description": "Map of files to projects that implicitly depend on them.", "deprecated": "Use named inputs instead.  See https://nx.dev/deprecated/global-implicit-dependencies"}, "affected": {"type": "object", "description": "Default options for `nx affected`.", "properties": {"defaultBase": {"type": "string", "description": "Default based branch used by affected commands."}}, "additionalProperties": false, "deprecated": "Use `defaultBase` instead. Support for setting `defaultBase` in `affected` will be removed in Nx 20."}, "extends": {"type": "string", "description": "Specifies the base config to extend."}, "defaultBase": {"type": "string", "description": "Default --base used by affected logic."}, "tasksRunnerOptions": {"type": "object", "additionalProperties": {"$ref": "#/definitions/tasksRunnerOptions"}}, "namedInputs": {"type": "object", "description": "Named inputs used by inputs defined in targets", "additionalProperties": {"$ref": "#/definitions/inputs"}}, "targetDefaults": {"type": "object", "description": "Target defaults", "additionalProperties": {"$ref": "#/definitions/targetDefaultsConfig"}}, "workspaceLayout": {"type": "object", "description": "Where new apps + libs should be placed.", "properties": {"libsDir": {"type": "string", "description": "Default folder name for libs."}, "appsDir": {"type": "string", "description": "Default folder name for apps."}}, "additionalProperties": false}, "cli": {"$ref": "#/definitions/cliOptions"}, "generators": {"$ref": "#/definitions/generatorOptions"}, "plugins": {"type": "array", "description": "Plugins for extending the project graph.", "items": {"$ref": "#/definitions/plugins"}}, "tui": {"type": "object", "description": "Settings for the Nx Terminal User Interface (TUI)", "properties": {"enabled": {"type": "boolean", "description": "Whether to enable the Terminal UI whenever possible (based on the current environment and terminal).", "default": true}, "autoExit": {"oneOf": [{"type": "boolean"}, {"type": "number"}], "description": "Whether to exit the TUI automatically after all tasks finish. If set to `true`, the TUI will exit immediately. If set to `false` the TUI will not automatically exit. If set to a number, an interruptible countdown popup will be shown for that many seconds before the TUI exits.", "default": 3}}, "additionalProperties": false}, "defaultProject": {"type": "string", "description": "Default project. When project isn't provided, the default project will be used."}, "nxCloudAccessToken": {"type": "string", "description": "The access token to use for nx-cloud. If set, the default tasks runner will be nx-cloud."}, "nxCloudUrl": {"type": "string", "description": "Specifies the url pointing to an instance of nx cloud. Used for remote caching and displaying run links."}, "nxCloudEncryptionKey": {"type": "string", "description": "Specifies the encryption key used to encrypt artifacts data before sending it to nx cloud."}, "neverConnectToCloud": {"type": "boolean", "description": "Set this to true to disable all connections to Nx Cloud."}, "parallel": {"type": "number", "description": "Specifies how many tasks are ran in parallel by Nx for the default tasks runner."}, "cacheDirectory": {"type": "string", "description": "Specifies the default location of the cache directory."}, "useDaemonProcess": {"type": "boolean", "description": "Specifies whether the daemon should be used for the default tasks runner."}, "useInferencePlugins": {"type": "boolean", "description": "Specifies whether to add inference plugins when generating new projects."}, "release": {"type": "object", "description": "Configuration for the nx release commands.", "additionalProperties": false, "properties": {"projects": {"oneOf": [{"type": "string", "description": "A project name"}, {"type": "array", "description": "An array of project names", "minItems": 1, "items": {"type": "string"}}]}, "groups": {"type": "object", "additionalProperties": {"type": "object", "properties": {"projectsRelationship": {"type": "string", "enum": ["fixed", "independent"]}, "projects": {"oneOf": [{"type": "string", "description": "A project name"}, {"type": "array", "description": "An array of project names", "minItems": 1, "items": {"type": "string"}}]}, "version": {"$ref": "#/definitions/NxReleaseGroupVersionConfiguration"}, "changelog": {"oneOf": [{"$ref": "#/definitions/NxReleaseChangelogConfiguration"}, {"type": "boolean"}]}, "releaseTagPattern": {"type": "string", "description": "Optionally override the git/release tag pattern to use for this group."}, "releaseTagPatternCheckAllBranchesWhen": {"oneOf": [{"type": "boolean"}, {"type": "array", "items": {"type": "string"}}], "description": "By default, we will try and resolve the latest match for the releaseTagPattern from the current branch, falling back to all branches if no match is found on the current branch. Setting this to true will cause us to ALWAYS check all branches for the latest match. Setting it to false will cause us to ONLY check the current branch for the latest match. Setting it to an array of strings will cause us to check all branches WHEN the current branch is one of the strings in the array. Glob patterns are supported."}, "versionPlans": {"oneOf": [{"$ref": "#/definitions/NxReleaseVersionPlansConfiguration"}, {"type": "boolean", "description": "Enables using version plans as a specifier source for versioning and to determine changes for changelog generation."}]}}, "required": ["projects"]}}, "changelog": {"type": "object", "properties": {"workspaceChangelog": {"oneOf": [{"$ref": "#/definitions/NxReleaseChangelogConfiguration"}, {"type": "boolean"}]}, "projectChangelogs": {"oneOf": [{"$ref": "#/definitions/NxReleaseChangelogConfiguration"}, {"type": "boolean"}]}, "automaticFromRef": {"type": "boolean", "description": "Whether or not to automatically look up the first commit for the workspace (or package, if versioning independently) and use that as the starting point for changelog generation. If this is not enabled, changelog generation will fail if there is no previous matching git tag to use as a starting point."}, "git": {"$ref": "#/definitions/NxReleaseGitConfiguration"}}}, "conventionalCommits": {"$ref": "#/definitions/NxReleaseConventionalCommitsConfiguration"}, "projectsRelationship": {"type": "string", "enum": ["fixed", "independent"]}, "git": {"$ref": "#/definitions/NxReleaseGitConfiguration"}, "version": {"$ref": "#/definitions/NxReleaseVersionConfiguration"}, "versionPlans": {"oneOf": [{"$ref": "#/definitions/NxReleaseVersionPlansConfiguration"}, {"type": "boolean", "description": "Enables using version plans as a specifier source for versioning and to determine changes for changelog generation."}]}, "releaseTagPattern": {"type": "string", "description": "Optionally override the git/release tag pattern to use. This field is the source of truth for changelog generation and release tagging, as well as for conventional commits parsing. It supports interpolating the version as {version} and (if releasing independently or forcing project level version control system releases) the project name as {projectName} within the string. The default releaseTagPattern for fixed/unified releases is: \"v{version}\". The default releaseTagPattern for independent releases at the project level is: \"{projectName}@{version}\""}, "releaseTagPatternCheckAllBranchesWhen": {"oneOf": [{"type": "boolean"}, {"type": "array", "items": {"type": "string"}}], "description": "By default, we will try and resolve the latest match for the releaseTagPattern from the current branch, falling back to all branches if no match is found on the current branch. Setting this to true will cause us to ALWAYS check all branches for the latest match. Setting it to false will cause us to ONLY check the current branch for the latest match. Setting it to an array of strings will cause us to check all branches WHEN the current branch is one of the strings in the array. Glob patterns are supported."}}}, "sync": {"type": "object", "description": "Configuration for the `nx sync` command", "properties": {"globalGenerators": {"type": "array", "items": {"type": "string"}, "description": "List of workspace-wide sync generators to be run (not attached to targets)"}, "generatorOptions": {"type": "object", "description": "Options for the sync generators.", "additionalProperties": {"type": "object"}}, "applyChanges": {"type": "boolean", "description": "Whether to automatically apply sync generator changes when running tasks. If not set, the user will be prompted. If set to `true`, the user will not be prompted and the changes will be applied. If set to `false`, the user will not be prompted and the changes will not be applied."}, "disabledTaskSyncGenerators": {"type": "array", "items": {"type": "string"}, "description": "List of registered task sync generators to disable."}}, "additionalProperties": false}}, "definitions": {"inputs": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"fileset": {"type": "string", "description": "A glob"}}, "additionalProperties": false}, {"type": "object", "properties": {"projects": {"oneOf": [{"type": "string", "description": "The project that the targets belong to."}, {"type": "array", "description": "The projects that the targets belong to.", "items": {"type": "string"}}]}, "dependencies": {"type": "boolean", "description": "Include files belonging to the input for all the project dependencies of this target."}, "input": {"type": "string", "description": "The name of the input."}}, "oneOf": [{"required": ["projects", "input"]}, {"required": ["dependencies", "input"]}, {"required": ["input"], "not": {"anyOf": [{"required": ["projects"]}, {"required": ["dependencies"]}]}}], "additionalProperties": false}, {"type": "object", "properties": {"runtime": {"type": "string", "description": "The command that will be executed and the results of which is added to the hash"}}, "additionalProperties": false}, {"type": "object", "properties": {"env": {"type": "string", "description": "The name of the env var which value is added to the hash"}}, "additionalProperties": false}, {"type": "object", "properties": {"externalDependencies": {"type": "array", "items": {"type": "string"}, "description": "The list of external dependencies that our target depends on for `nx:run-commands` and community plugins."}}, "additionalProperties": false}, {"type": "object", "properties": {"dependentTasksOutputFiles": {"type": "string", "description": "The glob list of output files that project depends on."}, "transitive": {"type": "boolean", "description": "Whether the check for outputs should be recursive or stop at the first level of dependencies."}}, "required": ["dependentTasksOutputFiles"], "additionalProperties": false}]}}, "cliOptions": {"type": "object", "description": "Default generator collection.", "properties": {"packageManager": {"type": "string", "description": "The default package manager to use.", "enum": ["yarn", "pnpm", "npm", "bun"]}}}, "generatorOptions": {"type": "object", "description": "List of default values used by generators."}, "tasksRunnerOptions": {"type": "object", "description": "Available Task Runners.", "properties": {"runner": {"type": "string", "description": "Path to resolve the runner."}, "options": {"type": "object", "description": "Default options for the runner.", "properties": {"accessToken": {"type": "string"}, "nxCloudId": {"type": "string"}, "captureStderr": {"type": "boolean", "description": "Defines whether the cache captures stderr or just stdout."}, "parallel": {"type": "number", "description": "Defines the max number of targets ran in parallel."}, "cacheableOperations": {"type": "array", "description": "Defines the list of targets/operations that are cached by Nx.", "items": {"type": "string"}}, "cacheDirectory": {"type": "string", "description": "Defines where the local cache is stored."}, "skipNxCache": {"type": "boolean", "description": "Defines whether the Nx Cache should be skipped."}, "encryptionKey": {"type": "string", "description": "Defines an encryption key to support end-to-end encryption of your cloud cache. You may also provide an environment variable with the key NX_CLOUD_ENCRYPTION_KEY that contains an encryption key as its value. The Nx Cloud task runner normalizes the key length, so any length of key is acceptable."}}}}, "additionalProperties": false}, "targetDefaultsConfig": {"type": "object", "description": "Target defaults", "properties": {"executor": {"description": "The function that Nx will invoke when you run this target", "type": "string"}, "options": {"type": "object"}, "outputs": {"type": "array", "items": {"type": "string"}}, "defaultConfiguration": {"type": "string", "description": "The name of a configuration to use as the default if a configuration is not provided"}, "configurations": {"type": "object", "description": "provides extra sets of values that will be merged into the options map", "additionalProperties": {"type": "object"}}, "continuous": {"type": "boolean", "default": false, "description": "Whether this target runs continuously until stopped"}, "parallelism": {"type": "boolean", "default": true, "description": "Whether this target can be run in parallel with other tasks"}, "inputs": {"$ref": "#/definitions/inputs"}, "dependsOn": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"projects": {"oneOf": [{"type": "string", "description": "A project name"}, {"type": "array", "description": "An array of project names", "minItems": 1, "items": {"type": "string"}}]}, "dependencies": {"type": "boolean"}, "target": {"type": "string", "description": "The name of the target."}, "params": {"type": "string", "description": "Configuration for params handling.", "enum": ["ignore", "forward"], "default": "ignore"}}, "oneOf": [{"required": ["projects", "target"]}, {"required": ["dependencies", "target"]}, {"required": ["target"], "not": {"anyOf": [{"required": ["projects"]}, {"required": ["dependencies"]}]}}], "additionalProperties": false}]}}, "cache": {"type": "boolean", "description": "Specifies if the given target should be cacheable"}, "syncGenerators": {"type": "array", "items": {"type": "string"}, "description": "List of generators to run before the target to ensure the workspace is up to date"}}, "additionalProperties": false}, "plugins": {"oneOf": [{"type": "string", "description": "A plugin module to load with default options"}, {"type": "object", "properties": {"plugin": {"type": "string", "description": "The plugin module to load"}, "options": {"type": "object", "description": "The options passed to the plugin when creating nodes and dependencies"}, "include": {"type": "array", "description": "File patterns which are included by the plugin", "items": {"type": "string"}}, "exclude": {"type": "array", "description": "File patterns which are excluded by the plugin", "items": {"type": "string"}}}}]}, "NxReleaseGitConfiguration": {"type": "object", "properties": {"commit": {"type": "boolean", "description": "Whether or not to automatically commit the changes made by current command"}, "commitMessage": {"type": "string", "description": "Custom git commit message to use when committing the changes made by this command"}, "commitArgs": {"type": ["string", "array"], "description": "Additional arguments (added after the --message argument, which may or may not be customized with --git-commit-message) to pass to the `git commit` command invoked behind the scenes"}, "stageChanges": {"type": "boolean", "description": "Whether or not to stage the changes made by this command. Always treated as true if commit is true."}, "tag": {"type": "boolean", "description": "Whether or not to automatically tag the changes made by this command"}, "tagMessage": {"type": "string", "description": "Custom git tag message to use when tagging the changes made by this command. This defaults to be the same value as the tag itself."}, "tagArgs": {"type": ["string", "array"], "description": "Additional arguments to pass to the `git tag` command invoked behind the scenes"}}}, "NxReleaseVersionConfiguration": {"type": "object", "$comment": "The configuration for versioning is dynamic depending on the value of the useLegacyVersioning property. Through trial and error the best in editor DX seems to come from having the if/else at the top level and explicitly include all possible properties and apply additionalProperties false in each case.", "description": "Configuration for the versioning phase of releases.", "if": {"$comment": "When using the latest versioning implementation a lot of configuration has been able to move directly onto the version property.", "properties": {"useLegacyVersioning": {"const": false}}}, "then": {"additionalProperties": false, "properties": {"useLegacyVersioning": {"type": "boolean", "description": "Whether to use the legacy versioning strategy. This value will be true in Nx v20 and false in Nx v21. The legacy versioning implementation will be removed in Nx v22, as will this flag.", "default": false}, "conventionalCommits": {"type": "boolean", "description": "Shorthand for enabling the current version of projects to be resolved from git tags, and the next version to be determined by analyzing commit messages according to the Conventional Commits specification.", "default": false}, "git": {"$ref": "#/definitions/NxReleaseGitConfiguration"}, "preVersionCommand": {"type": "string", "description": "A command to run after validation of nx release configuration, but before versioning begins. Useful for preparing build artifacts. If --dry-run is passed, the command is still executed, but with the NX_DRY_RUN environment variable set to 'true'."}, "specifierSource": {"type": "string", "enum": ["prompt", "conventional-commits", "version-plans"], "default": "prompt", "description": "The source to use for determining the specifier to use when versioning. 'prompt' is the default and will interactively prompt the user for an explicit/imperative specifier. 'conventional-commits' will attempt determine a specifier from commit messages conforming to the Conventional Commits specification. 'version-plans' will determine the specifier from the version plan files available on disk."}, "manifestRootsToUpdate": {"type": "array", "items": {"type": "string"}, "description": "A list of directories containing manifest files (such as package.json) to apply updates to when versioning. By default, only the project root will be used, but you could customize this to only version a manifest in a dist directory, or even version multiple manifests in different directories, such as both source and dist."}, "currentVersionResolver": {"type": "string", "enum": ["registry", "disk", "git-tag", "none"], "description": "The resolver to use for determining the current version of a project during versioning. This is needed for versioning approaches which involve relatively modifying a current version to arrive at a new version, such as semver bumps like 'patch', 'minor' etc. Using 'none' explicitly declares that the current version is not needed to compute the new version, and should only be used with appropriate version actions implementations that support it."}, "currentVersionResolverMetadata": {"type": "object", "additionalProperties": true, "description": "Metadata to provide to the configured currentVersionResolver to help it in determining the current version. What to pass here is specific to each resolver."}, "fallbackCurrentVersionResolver": {"type": "string", "enum": ["disk"], "description": "The fallback version resolver to use when the configured currentVersionResolver fails to resolve the current version."}, "versionPrefix": {"type": "string", "enum": ["auto", "", "~", "^", "="], "default": "auto", "description": "The prefix to use when versioning dependencies. This can be one of the following: auto, '', '~', '^', '=', where auto means the existing prefix will be preserved."}, "deleteVersionPlans": {"type": "boolean", "description": "Whether to delete the processed version plans file after versioning is complete. This is false by default because the version plans are also needed for changelog generation.", "default": false}, "updateDependents": {"type": "string", "enum": ["never", "auto"], "default": "auto", "description": "When versioning independent projects, this controls whether to update their dependents (i.e. the things that depend on them). 'never' means no dependents will be updated (unless they happen to be versioned directly as well). 'auto' is the default and will cause dependents to be updated (a patch version bump) when a dependency is versioned."}, "logUnchangedProjects": {"type": "boolean", "description": "Whether to log projects that have not changed during versioning.", "default": true}, "preserveLocalDependencyProtocols": {"type": "boolean", "description": "Whether to preserve local dependency protocols (e.g. file references, or the `workspace:` protocol in package.json files) of local dependencies when updating them during versioning. This was false by default in legacy versioning, but is true by default now.", "default": true}, "versionActions": {"type": "string", "description": "The path to the version actions implementation to use for releasing all projects by default. This can also be overridden on the release group and project levels.", "default": "@nx/js/src/release"}, "versionActionsOptions": {"type": "object", "description": "The specific options that are defined by each version actions implementation. They will be passed to the version actions implementation when running a release.", "additionalProperties": true}}}, "else": {"properties": {"useLegacyVersioning": {"type": "boolean", "description": "Whether to use the legacy versioning strategy. This value was true in Nx v20 and became false in Nx v21. The legacy versioning implementation will be removed in Nx v22, as will this flag.", "default": false}, "conventionalCommits": {"type": "boolean", "description": "Shorthand for enabling the current version of projects to be resolved from git tags, and the next version to be determined by analyzing commit messages according to the Conventional Commits specification.", "default": false}, "git": {"$ref": "#/definitions/NxReleaseGitConfiguration"}, "preVersionCommand": {"type": "string", "description": "A command to run after validation of nx release configuration, but before versioning begins. Useful for preparing build artifacts. If --dry-run is passed, the command is still executed, but with the NX_DRY_RUN environment variable set to 'true'."}, "generator": {"type": "string", "description": "The generator implementation to use for versioning.", "default": "@nx/js:release-version"}, "generatorOptions": {"type": "object", "description": "These options will be passed to the configured \"release.version.generator\" (which will be \"@nx/js:release-version\" if not set explicitly).", "additionalProperties": true}}, "additionalProperties": false}}, "NxReleaseGroupVersionConfiguration": {"type": "object", "$comment": "We need to improve this configuration definition to be more precise once legacy versioning is removed. Right now it needs to be left open and runtime validation will ensure correct behavior.", "properties": {"groupPreVersionCommand": {"type": "string", "description": "A command to run after validation of nx release configuration AND after the release.version.preVersionCommand (if any), but before versioning begins for this specific group. Useful for preparing build artifacts for the group. If --dry-run is passed, the command is still executed, but with the NX_DRY_RUN environment variable set to 'true'."}}, "additionalProperties": true}, "NxReleaseChangelogConfiguration": {"type": "object", "properties": {"createRelease": {"oneOf": [{"type": "string", "enum": ["github", "gitlab"]}, {"type": "boolean", "enum": [false]}, {"$ref": "#/definitions/CreateReleaseProviderConfiguration"}]}, "entryWhenNoChanges": {"oneOf": [{"type": "string"}, {"type": "boolean", "enum": [false]}]}, "file": {"oneOf": [{"type": "string"}, {"type": "boolean", "enum": [false]}]}, "renderer": {"type": "string"}, "renderOptions": {"$ref": "#/definitions/ChangelogRenderOptions"}}}, "CreateReleaseProviderConfiguration": {"type": "object", "properties": {"provider": {"type": "string", "enum": ["github-enterprise-server", "gitlab"]}, "hostname": {"type": "string", "description": "The hostname of the VCS provider instance, e.g. github.example.com"}, "apiBaseUrl": {"type": "string", "description": "The base URL for the relevant VCS provider API. If not set, this will default to `https://${hostname}/api/v3` (github) or `https://${hostname}/api/v4` (gitlab)."}}, "required": ["provider", "hostname"]}, "NxReleaseVersionPlansConfiguration": {"type": "object", "properties": {"ignorePatternsForPlanCheck": {"type": "array", "items": {"type": "string"}, "description": "Changes to files matching any of these optional patterns will be excluded from the affected project logic within the `nx release plan:check` command. This is useful for ignoring files that are not relevant to the versioning process, such as documentation or configuration files."}}}, "ChangelogRenderOptions": {"type": "object", "additionalProperties": true}, "NxReleaseConventionalCommitsConfiguration": {"type": "object", "properties": {"types": {"type": "object", "description": "A map of commit types to their configuration. If a type is set to 'true', then it will be enabled with the default 'semverBump' of 'patch' and will appear in the changelog. If a type is set to 'false', then it will not trigger a version bump and will be hidden from the changelog.", "additionalProperties": {"oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"semverBump": {"type": "string", "enum": ["major", "minor", "patch", "none"], "description": "The semver bump to apply to the version of the project(s) when a commit of this type is included in the release.", "default": "patch"}, "changelog": {"description": "Configuration for the changelog section for commits of this type. If set to 'true', then commits of this type will be included in the changelog with their default title for the type. If set to 'false', then commits of this type will not be included in the changelog.", "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the section in the changelog for commits of this type"}, "hidden": {"type": "boolean", "description": "Whether or not to include commits of this type in the changelog", "default": false}}}]}}}]}}}}}}