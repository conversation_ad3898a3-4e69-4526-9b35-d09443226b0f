{"$schema": "http://json-schema.org/draft-07/schema", "$id": "https://nx.dev/reference/project-configuration", "title": "JSON schema for Nx projects", "type": "object", "properties": {"name": {"type": "string", "description": "Project's name. Optional if specified in workspace.json"}, "root": {"type": "string", "description": "Project's location relative to the root of the workspace"}, "sourceRoot": {"type": "string", "description": "The location of project's sources relative to the root of the workspace"}, "projectType": {"type": "string", "description": "Type of project supported", "enum": ["library", "application"]}, "generators": {"type": "object", "description": "List of default values used by generators"}, "namedInputs": {"type": "object", "description": "Named inputs used by inputs defined in targets", "additionalProperties": {"$ref": "#/definitions/inputs"}}, "targets": {"type": "object", "description": "Configures all the targets which define what tasks you can run against the project", "additionalProperties": {"type": "object", "properties": {"executor": {"description": "The function that Nx will invoke when you run this target", "type": "string"}, "options": {"type": "object"}, "outputs": {"type": "array", "items": {"type": "string"}}, "defaultConfiguration": {"type": "string", "description": "The name of a configuration to use as the default if a configuration is not provided"}, "configurations": {"type": "object", "description": "provides extra sets of values that will be merged into the options map", "additionalProperties": {"type": "object"}}, "inputs": {"$ref": "#/definitions/inputs"}, "dependsOn": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"projects": {"oneOf": [{"type": "string", "description": "A project name"}, {"type": "array", "description": "An array of project names", "items": {"type": "string"}}]}, "dependencies": {"type": "boolean"}, "target": {"type": "string", "description": "The name of the target."}, "params": {"type": "string", "description": "Configuration for params handling.", "enum": ["ignore", "forward"], "default": "ignore"}}, "oneOf": [{"required": ["projects", "target"]}, {"required": ["dependencies", "target"]}, {"required": ["target"], "not": {"anyOf": [{"required": ["projects"]}, {"required": ["dependencies"]}]}}], "additionalProperties": false}]}}, "command": {"type": "string", "description": "A shorthand for using the nx:run-commands executor"}, "cache": {"type": "boolean", "description": "Specifies if the given target should be cacheable"}, "continuous": {"type": "boolean", "default": false, "description": "Whether this target runs continuously until stopped"}, "parallelism": {"type": "boolean", "default": true, "description": "Whether this target can be run in parallel with other tasks"}, "metadata": {"type": "object", "description": "<PERSON><PERSON><PERSON> about the target", "properties": {"description": {"type": "string", "description": "A description of the target"}}, "additionalProperties": true}, "syncGenerators": {"type": "array", "items": {"type": "string"}, "description": "List of generators to run before the target to ensure the workspace is up to date"}}}}, "tags": {"type": "array", "items": {"type": "string"}}, "implicitDependencies": {"type": "array", "items": {"type": "string"}}, "metadata": {"type": "object", "description": "<PERSON><PERSON><PERSON> about the project.", "properties": {"description": {"type": "string", "description": "A description of the project."}}, "additionalProperties": true}, "release": {"type": "object", "description": "Configuration for the nx release commands.", "properties": {"version": {"type": "object", "description": "Configuration for the nx release version command.", "properties": {"generator": {"type": "string", "description": "The version generator to use. Defaults to @nx/js:release-version."}, "generatorOptions": {"type": "object", "description": "Options for the version generator."}}}}}}, "definitions": {"inputs": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"fileset": {"type": "string", "description": "A glob used to determine a fileset."}}, "additionalProperties": false}, {"type": "object", "properties": {"projects": {"oneOf": [{"type": "string", "description": "The project that the targets belong to."}, {"type": "array", "description": "The projects that the targets belong to.", "items": {"type": "string"}}]}, "dependencies": {"type": "boolean", "description": "Include files belonging to the input for all the project dependencies of this target."}, "input": {"type": "string", "description": "The name of the input."}}, "oneOf": [{"required": ["projects", "input"]}, {"required": ["dependencies", "input"]}, {"required": ["input"], "not": {"anyOf": [{"required": ["projects"]}, {"required": ["dependencies"]}]}}], "additionalProperties": false}, {"type": "object", "properties": {"runtime": {"type": "string", "description": "The command that will be executed and included into the hash."}}, "additionalProperties": false}, {"type": "object", "properties": {"env": {"type": "string", "description": "The env var that will be included into the hash."}}, "additionalProperties": false}, {"type": "object", "properties": {"externalDependencies": {"type": "array", "items": {"type": "string"}, "description": "The list of external dependencies that our target depends on for `nx:run-commands` and community plugins."}}, "additionalProperties": false}, {"type": "object", "properties": {"dependentTasksOutputFiles": {"type": "string", "description": "The glob list of output files that project depends on."}, "transitive": {"type": "boolean", "description": "Whether the check for outputs should be recursive or stop at the first level of dependencies."}}, "required": ["dependentTasksOutputFiles"], "additionalProperties": false}]}}}}