import { CommandModule } from 'yargs';
/**
 * @deprecated 'Use `nx graph --affected`, or` nx affected --graph` instead depending on which best suits your use case. The `affected:graph` command will be removed in Nx 19.'
 */
export declare const yargsAffectedGraphCommand: CommandModule;
/**
 * @deprecated 'Use `nx show --affected`, `nx affected --graph` or `nx graph --affected` depending on which best suits your use case. The `print-affected` command will be removed in Nx 19.'
 */
export declare const yargsPrintAffectedCommand: CommandModule;
