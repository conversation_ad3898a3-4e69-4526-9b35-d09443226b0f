import type { NxReleaseConfiguration } from '../../config/nx-json';
/**
 * @public
 */
export declare class ReleaseClient {
    private overrideReleaseConfig;
    releaseChangelog: (args: import("./command-object").ChangelogOptions) => Promise<import("./changelog").NxReleaseChangelogResult>;
    releasePublish: (args: import("./command-object").PublishOptions) => Promise<import("./publish").PublishProjectsResult>;
    releaseVersion: (args: import("./command-object").VersionOptions) => Promise<import("./version").NxReleaseVersionResult>;
    release: (args: import("./command-object").ReleaseOptions) => Promise<import("./version").NxReleaseVersionResult | number>;
    constructor(overrideReleaseConfig: NxReleaseConfiguration);
}
declare const defaultClient: ReleaseClient;
/**
 * @public
 */
export declare const releaseChangelog: typeof defaultClient.releaseChangelog;
/**
 * @public
 */
export { PublishProjectsResult } from './publish';
/**
 * @public
 */
export declare const releasePublish: typeof defaultClient.releasePublish;
/**
 * @public
 */
export declare const releaseVersion: typeof defaultClient.releaseVersion;
/**
 * @public
 */
export declare const release: typeof defaultClient.release;
/**
 * @public
 */
export { AfterAllProjectsVersioned, VersionActions, } from './version/version-actions';
