/* auto-generated by NAPI-RS */
/* eslint-disable */

export declare class ExternalObject<T> {
  readonly '': {
    readonly '': unique symbol
    [K: symbol]: T
  }
}
export declare class AppLifeCycle {
  constructor(tasks: Array<Task>, initiatingTasks: Array<string>, runMode: RunMode, pinnedTasks: Array<string>, tuiCliArgs: TuiCliArgs, tuiConfig: TuiConfig, titleText: string, workspaceRoot: string)
  startCommand(threadCount?: number | undefined | null): void
  scheduleTask(task: Task): void
  startTasks(tasks: Array<Task>, metadata: object): void
  printTaskTerminalOutput(task: Task, status: string, output: string): void
  endTasks(taskResults: Array<TaskResult>, metadata: object): void
  endCommand(): void
  __init(doneCallback: () => any): void
  registerRunningTask(taskId: string, parserAndWriter: ExternalObject<[ParserArc, WriterArc]>): void
  registerRunningTaskWithEmptyParser(taskId: string): void
  appendTaskOutput(taskId: string, output: string, isPtyOutput: boolean): void
  setTaskStatus(taskId: string, status: TaskStatus): void
  registerForcedShutdownCallback(forcedShutdownCallback: () => any): void
  __setCloudMessage(message: string): Promise<void>
}

export declare class ChildProcess {
  getParserAndWriter(): ExternalObject<[ParserArc, WriterArc]>
  kill(signal?: NodeJS.Signals): void
  onExit(callback: (message: string) => void): void
  onOutput(callback: (message: string) => void): void
  cleanup(): void
}

export declare class FileLock {
  locked: boolean
  constructor(lockFilePath: string)
  unlock(): void
  check(): boolean
  wait(): Promise<void>
  lock(): void
}

export declare class HashPlanInspector {
  constructor(allWorkspaceFiles: ExternalObject<Array<FileData>>, projectGraph: ExternalObject<ProjectGraph>, projectFileMap: ExternalObject<Record<string, Array<FileData>>>)
  inspect(hashPlans: ExternalObject<Record<string, Array<HashInstruction>>>): Record<string, string[]>
}

export declare class HashPlanner {
  constructor(nxJson: NxJson, projectGraph: ExternalObject<ProjectGraph>)
  getPlans(taskIds: Array<string>, taskGraph: TaskGraph): Record<string, string[]>
  getPlansReference(taskIds: Array<string>, taskGraph: TaskGraph): ExternalObject<Record<string, Array<HashInstruction>>>
}

export declare class HttpRemoteCache {
  constructor()
  retrieve(hash: string, cacheDirectory: string): Promise<CachedResult | null>
  store(hash: string, cacheDirectory: string, terminalOutput: string, code: number): Promise<boolean>
}

export declare class ImportResult {
  file: string
  sourceProject: string
  dynamicImportExpressions: Array<string>
  staticImportExpressions: Array<string>
}

export declare class NxCache {
  cacheDirectory: string
  constructor(workspaceRoot: string, cachePath: string, dbConnection: ExternalObject<NxDbConnection>, linkTaskDetails?: boolean | undefined | null, maxCacheSize?: number | undefined | null)
  get(hash: string): CachedResult | null
  put(hash: string, terminalOutput: string, outputs: Array<string>, code: number): void
  applyRemoteCacheResults(hash: string, result: CachedResult, outputs?: Array<string> | undefined | null): void
  getTaskOutputsPath(hash: string): string
  getCacheSize(): number
  copyFilesFromCache(cachedResult: CachedResult, outputs: Array<string>): number
  removeOldCacheRecords(): void
  checkCacheFsInSync(): boolean
}

export declare class NxConsolePreferences {
  constructor(homeDir: string)
  getAutoInstallPreference(): boolean | null
  setAutoInstallPreference(autoInstall: boolean): void
}

export declare class NxTaskHistory {
  constructor(db: ExternalObject<NxDbConnection>)
  recordTaskRuns(taskRuns: Array<TaskRun>): void
  getFlakyTasks(hashes: Array<string>): Array<string>
  getEstimatedTaskTimings(targets: Array<TaskTarget>): Record<string, number>
}

export declare class RunningTasksService {
  constructor(db: ExternalObject<NxDbConnection>)
  getRunningTasks(ids: Array<string>): Array<string>
  addRunningTask(taskId: string): void
  removeRunningTask(taskId: string): void
}

export declare class RustPseudoTerminal {
  constructor()
  runCommand(command: string, commandDir?: string | undefined | null, jsEnv?: Record<string, string> | undefined | null, execArgv?: Array<string> | undefined | null, quiet?: boolean | undefined | null, tty?: boolean | undefined | null, commandLabel?: string | undefined | null): ChildProcess
  /**
   * This allows us to run a pseudoterminal with a fake node ipc channel
   * this makes it possible to be backwards compatible with the old implementation
   */
  fork(id: string, forkScript: string, pseudoIpcPath: string, commandDir: string | undefined | null, jsEnv: Record<string, string> | undefined | null, execArgv: Array<string> | undefined | null, quiet: boolean, commandLabel?: string | undefined | null): ChildProcess
}

export declare class TaskDetails {
  constructor(db: ExternalObject<NxDbConnection>)
  recordTaskDetails(tasks: Array<HashedTask>): void
}

export declare class TaskHasher {
  constructor(workspaceRoot: string, projectGraph: ExternalObject<ProjectGraph>, projectFileMap: ExternalObject<ProjectFiles>, allWorkspaceFiles: ExternalObject<Array<FileData>>, tsConfig: Buffer, tsConfigPaths: Record<string, Array<string>>, options?: HasherOptions | undefined | null)
  hashPlans(hashPlans: ExternalObject<Record<string, Array<HashInstruction>>>, jsEnv: Record<string, string>): NapiDashMap
}

export declare class Watcher {
  origin: string
  /**
   * Creates a new Watcher instance.
   * Will always ignore the following directories:
   * * .git/
   * * node_modules/
   * * .nx/
   */
  constructor(origin: string, additionalGlobs?: Array<string> | undefined | null, useIgnore?: boolean | undefined | null)
  watch(callback: (err: string | null, events: WatchEvent[]) => void): void
  stop(): Promise<void>
}

export declare class WorkspaceContext {
  workspaceRoot: string
  constructor(workspaceRoot: string, cacheDir: string)
  getWorkspaceFiles(projectRootMap: Record<string, string>): NxWorkspaceFiles
  glob(globs: Array<string>, exclude?: Array<string> | undefined | null): Array<string>
  /**
   * Performs multiple glob pattern matches against workspace files in parallel
   * @returns An array of arrays, where each inner array contains the file paths
   * that matched the corresponding glob pattern in the input. The outer array maintains the same order
   * as the input globs.
   */
  multiGlob(globs: Array<string>, exclude?: Array<string> | undefined | null): Array<Array<string>>
  hashFilesMatchingGlobs(globGroups: Array<Array<string>>): Array<string>
  hashFilesMatchingGlob(globs: Array<string>, exclude?: Array<string> | undefined | null): string
  incrementalUpdate(updatedFiles: Array<string>, deletedFiles: Array<string>): Record<string, string>
  updateProjectFiles(projectRootMappings: ProjectRootMappings, projectFiles: ExternalObject<ProjectFiles>, globalFiles: ExternalObject<Array<FileData>>, updatedFiles: Record<string, string>, deletedFiles: Array<string>): UpdatedWorkspaceFiles
  allFileData(): Array<FileData>
  getFilesInDirectory(directory: string): Array<string>
}

export interface CachedResult {
  code: number
  terminalOutput?: string
  outputsPath: string
  size?: number
}

export declare export declare function canInstallNxConsole(): boolean

export declare export declare function closeDbConnection(connection: ExternalObject<NxDbConnection>): void

export declare export declare function connectToNxDb(cacheDir: string, nxVersion: string, dbName?: string | undefined | null): ExternalObject<NxDbConnection>

export declare export declare function copy(src: string, dest: string): number

export interface DepsOutputsInput {
  dependentTasksOutputFiles: string
  transitive?: boolean
}

export interface EnvironmentInput {
  env: string
}

export declare const enum EventType {
  delete = 'delete',
  update = 'update',
  create = 'create'
}

export declare export declare function expandOutputs(directory: string, entries: Array<string>): Array<string>

export interface ExternalDependenciesInput {
  externalDependencies: Array<string>
}

export interface ExternalNode {
  packageName?: string
  version: string
  hash?: string
}

export interface FileData {
  file: string
  hash: string
}

export interface FileMap {
  projectFileMap: ProjectFiles
  nonProjectFiles: Array<FileData>
}

export interface FileSetInput {
  fileset: string
}

export declare export declare function findImports(projectFileMap: Record<string, Array<string>>): Array<ImportResult>

export declare export declare function getBinaryTarget(): string

export declare export declare function getDefaultMaxCacheSize(cachePath: string): number

/**
 * Expands the given outputs into a list of existing files.
 * This is used when hashing outputs
 */
export declare export declare function getFilesForOutputs(directory: string, entries: Array<string>): Array<string>

export declare export declare function getTransformableOutputs(outputs: Array<string>): Array<string>

export declare export declare function hashArray(input: Array<string | undefined | null>): string

export interface HashDetails {
  value: string
  details: Record<string, string>
}

export interface HashedTask {
  hash: string
  project: string
  target: string
  configuration?: string
}

export interface HasherOptions {
  selectivelyHashTsConfig: boolean
}

export declare export declare function hashFile(file: string): string | null

export interface InputsInput {
  input: string
  dependencies?: boolean
  projects?: string | Array<string>
}

export declare export declare function installNxConsole(): void

export const IS_WASM: boolean

export declare export declare function logDebug(message: string): void

/** Stripped version of the NxJson interface for use in rust */
export interface NxJson {
  namedInputs?: Record<string, Array<JsInputs>>
}

export interface NxWorkspaceFiles {
  projectFileMap: ProjectFiles
  globalFiles: Array<FileData>
  externalReferences?: NxWorkspaceFilesExternals
}

export interface NxWorkspaceFilesExternals {
  projectFiles: ExternalObject<ProjectFiles>
  globalFiles: ExternalObject<Array<FileData>>
  allWorkspaceFiles: ExternalObject<Array<FileData>>
}

export declare export declare function parseTaskStatus(stringStatus: string): TaskStatus

export interface Project {
  root: string
  namedInputs?: Record<string, Array<JsInputs>>
  tags?: Array<string>
  targets: Record<string, Target>
}

export interface ProjectGraph {
  nodes: Record<string, Project>
  dependencies: Record<string, Array<string>>
  externalNodes: Record<string, ExternalNode>
}

export declare export declare function remove(src: string): void

export declare export declare function restoreTerminal(): void

export declare const enum RunMode {
  RunOne = 0,
  RunMany = 1
}

export interface RuntimeInput {
  runtime: string
}

export interface Target {
  executor?: string
  inputs?: Array<JsInputs>
  outputs?: Array<string>
  options?: string
  configurations?: string
  parallelism?: boolean
}

export interface Task {
  id: string
  target: TaskTarget
  outputs: Array<string>
  projectRoot?: string
  startTime?: number
  endTime?: number
  continuous?: boolean
}

export interface TaskGraph {
  roots: Array<string>
  tasks: Record<string, Task>
  dependencies: Record<string, Array<string>>
}

export interface TaskResult {
  task: Task
  status: string
  code: number
  terminalOutput?: string
}

export interface TaskRun {
  hash: string
  status: string
  code: number
  start: number
  end: number
}

export declare const enum TaskStatus {
  Success = 0,
  Failure = 1,
  Skipped = 2,
  LocalCacheKeptExisting = 3,
  LocalCache = 4,
  RemoteCache = 5,
  NotStarted = 6,
  InProgress = 7,
  Shared = 8,
  Stopped = 9
}

export interface TaskTarget {
  project: string
  target: string
  configuration?: string
}

export declare export declare function testOnlyTransferFileMap(projectFiles: Record<string, Array<FileData>>, nonProjectFiles: Array<FileData>): NxWorkspaceFilesExternals

/**
 * Transfer the project graph from the JS world to the Rust world, so that we can pass the project graph via memory quicker
 * This wont be needed once the project graph is created in Rust
 */
export declare export declare function transferProjectGraph(projectGraph: ProjectGraph): ExternalObject<ProjectGraph>

export interface TuiCliArgs {
  targets?: string[] | undefined
  tuiAutoExit?: boolean | number | undefined
}

export interface TuiConfig {
  autoExit?: boolean | number | undefined
}

export interface UpdatedWorkspaceFiles {
  fileMap: FileMap
  externalReferences: NxWorkspaceFilesExternals
}

export declare export declare function validateOutputs(outputs: Array<string>): void

export interface WatchEvent {
  path: string
  type: EventType
}

/** Public NAPI error codes that are for Node */
export declare const enum WorkspaceErrors {
  ParseError = 'ParseError',
  Generic = 'Generic'
}

