import { NxJsonConfiguration } from '../config/nx-json';
import { ProjectGraph } from '../config/project-graph';
import { Task, TaskGraph } from '../config/task-graph';
import { DaemonClient } from '../daemon/client/client';
import { TaskHasher } from '../hasher/task-hasher';
import { NxArgs } from '../utils/command-line-utils';
import { DefaultTasksRunnerOptions } from './default-tasks-runner';
import { TaskResult } from './life-cycle';
import { RunningTask } from './running-tasks/running-task';
import { TaskStatus } from './tasks-runner';
import { Batch } from './tasks-schedule';
import { SharedRunningTask } from './running-tasks/shared-running-task';
export declare class TaskOrchestrator {
    private readonly hasher;
    private readonly initiatingProject;
    private readonly initiatingTasks;
    private readonly projectGraph;
    private readonly taskGraph;
    private readonly nxJson;
    private readonly options;
    private readonly bail;
    private readonly daemon;
    private readonly outputStyle;
    private readonly taskGraphForHashing;
    private taskDetails;
    private cache;
    private readonly tuiEnabled;
    private forkedProcessTaskRunner;
    private runningTasksService;
    private tasksSchedule;
    private batchEnv;
    private reverseTaskDeps;
    private initializingTaskIds;
    private processedTasks;
    private processedBatches;
    private completedTasks;
    private waitingForTasks;
    private groups;
    private bailed;
    private runningContinuousTasks;
    private runningRunCommandsTasks;
    constructor(hasher: TaskHasher, initiatingProject: string | undefined, initiatingTasks: Task[], projectGraph: ProjectGraph, taskGraph: TaskGraph, nxJson: NxJsonConfiguration, options: NxArgs & DefaultTasksRunnerOptions, bail: boolean, daemon: DaemonClient, outputStyle: string, taskGraphForHashing?: TaskGraph);
    init(): Promise<void>;
    run(): Promise<{
        [id: string]: TaskStatus;
    }>;
    nextBatch(): Batch;
    private executeNextBatchOfTasksUsingTaskSchedule;
    private processTasks;
    private processTask;
    private processScheduledBatch;
    processAllScheduledTasks(): void;
    private applyCachedResults;
    private applyCachedResult;
    applyFromCacheOrRunBatch(doNotSkipCache: boolean, batch: Batch, groupId: number): Promise<TaskResult[]>;
    private runBatch;
    applyFromCacheOrRunTask(doNotSkipCache: boolean, task: Task, groupId: number): Promise<TaskResult>;
    private runTask;
    private runTaskInForkedProcess;
    startContinuousTask(task: Task, groupId: number): Promise<RunningTask | SharedRunningTask>;
    private preRunSteps;
    private postRunSteps;
    private scheduleNextTasksAndReleaseThreads;
    private complete;
    private pipeOutputCapture;
    private shouldCacheTaskResult;
    private closeGroup;
    private openGroup;
    private shouldCopyOutputsFromCache;
    private recordOutputsHash;
    private cleanup;
    private cleanUpUnneededContinuousTasks;
}
export declare function getThreadCount(options: NxArgs & DefaultTasksRunnerOptions, taskGraph: TaskGraph): number;
