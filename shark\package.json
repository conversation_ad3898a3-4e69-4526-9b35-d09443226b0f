{"name": "@shark/source", "version": "0.0.0", "license": "MIT", "scripts": {"build": "nx build", "test": "nx test", "lint": "nx lint", "serve": "nx serve", "dev": "nx run-many --target=serve --projects=customer-app,vendor-app,admin-app --parallel", "db:migrate": "nx run database:migrate", "db:seed": "nx run database:seed"}, "private": true, "dependencies": {"empty": "0.10.1"}, "devDependencies": {"@nx/workspace": "21.2.1", "nx": "21.2.1"}}