{"name": "@shark/source", "version": "0.0.0", "license": "MIT", "scripts": {"build": "nx build", "test": "nx test", "lint": "nx lint", "serve": "nx serve", "dev": "nx run-many --target=serve --projects=customer-app,vendor-app,admin-app --parallel", "db:migrate": "nx run database:migrate", "db:seed": "nx run database:seed"}, "private": true, "dependencies": {"empty": "0.10.1"}, "devDependencies": {"@nx/eslint": "^21.2.1", "@nx/jest": "^21.2.1", "@nx/js": "^21.2.1", "@nx/next": "^21.2.1", "@nx/react": "^21.2.1", "@nx/workspace": "21.2.1", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "nx": "21.2.1", "typescript": "^5.8.3"}}