# Environment Configuration
ENVIRONMENT=development
PORT=8003

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=shark_user
DB_PASSWORD=shark_password
DB_NAME=shark_db
DB_SSL_MODE=disable

# Alternative: Use DATABASE_URL instead of individual DB settings
# DATABASE_URL=postgres://shark_user:shark_password@localhost:5432/shark_db?sslmode=disable

# JWT Configuration (should match other services)
JWT_SECRET=shark-super-secret-jwt-key-for-development-only

# External Service URLs
USER_SERVICE_URL=http://localhost:8001
SERVICE_CATALOG_SERVICE_URL=http://localhost:8002
PAYMENT_SERVICE_URL=http://localhost:8004
NOTIFICATION_SERVICE_URL=http://localhost:8005

# Business Rules
BOOKING_ADVANCE_HOURS=2
CANCELLATION_HOURS=24
DEFAULT_SLOT_DURATION=60
MAX_BOOKINGS_PER_DAY=10
COMMISSION_RATE=0.15

# Redis Configuration (for caching and real-time features)
REDIS_URL=redis://localhost:6379
