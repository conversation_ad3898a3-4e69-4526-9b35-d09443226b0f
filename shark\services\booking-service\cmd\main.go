package main

import (
	"log"
	"net/http"
	"os"

	"shark/booking-service/internal/config"
	"shark/booking-service/internal/handlers"
	"shark/booking-service/internal/middleware"
	"shark/booking-service/internal/repository"
	"shark/booking-service/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Load configuration
	cfg := config.Load()

	// Initialize database connection
	db, err := config.InitDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize repositories
	bookingRepo := repository.NewBookingRepository(db)
	availabilityRepo := repository.NewAvailabilityRepository(db)

	// Initialize services
	bookingService := services.NewBookingService(bookingRepo, availabilityRepo, cfg)
	availabilityService := services.NewAvailabilityService(availabilityRepo, bookingRepo, cfg)

	// Initialize handlers
	bookingHandler := handlers.NewBookingHandler(bookingService)
	availabilityHandler := handlers.NewAvailabilityHandler(availabilityService)

	// Setup router
	router := setupRouter(bookingHandler, availabilityHandler, cfg)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8003"
	}

	log.Printf("Booking Service starting on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func setupRouter(bookingHandler *handlers.BookingHandler, availabilityHandler *handlers.AvailabilityHandler, cfg *config.Config) http.Handler {
	// Set Gin mode
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// CORS middleware
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "service": "booking-service"})
	})

	// API routes
	api := router.Group("/api/v1")
	{
		// Public routes (no authentication required)
		public := api.Group("/public")
		{
			// Availability (public read access)
			public.GET("/availability/vendor/:vendor_id", availabilityHandler.GetVendorAvailability)
			public.GET("/availability/slots", availabilityHandler.GetAvailableTimeSlots)
		}

		// Protected routes (authentication required)
		protected := api.Group("")
		protected.Use(middleware.AuthMiddleware(cfg.JWTSecret))
		{
			// Customer routes (any authenticated user can book)
			customer := protected.Group("/customer")
			{
				// Booking management
				customer.POST("/bookings", bookingHandler.CreateBooking)
				customer.GET("/bookings", bookingHandler.GetMyBookings)
				customer.GET("/bookings/:id", bookingHandler.GetBooking)
				customer.PUT("/bookings/:id", bookingHandler.UpdateBooking)
				customer.DELETE("/bookings/:id/cancel", bookingHandler.CancelBooking)
			}

			// Vendor routes (vendor or admin access)
			vendor := protected.Group("/vendor")
			vendor.Use(middleware.VendorMiddleware())
			{
				// Booking management for vendors
				vendor.GET("/bookings", bookingHandler.GetMyBookings)
				vendor.GET("/bookings/:id", bookingHandler.GetBooking)
				vendor.PUT("/bookings/:id", bookingHandler.UpdateBooking)
				vendor.GET("/bookings/stats", bookingHandler.GetBookingStats)
				
				// Availability management
				vendor.POST("/availability", availabilityHandler.CreateAvailability)
				vendor.GET("/availability", availabilityHandler.GetMyAvailability)
				vendor.PUT("/availability/:id", availabilityHandler.UpdateAvailability)
				vendor.DELETE("/availability/:id", availabilityHandler.DeleteAvailability)
			}

			// Admin routes (admin only)
			admin := protected.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				// Advanced booking management
				admin.GET("/bookings/search", bookingHandler.SearchBookings)
				admin.GET("/bookings/stats", bookingHandler.GetBookingStats)
				admin.PUT("/bookings/:id", bookingHandler.UpdateBooking)
			}
		}
	}

	return c.Handler(router)
}
