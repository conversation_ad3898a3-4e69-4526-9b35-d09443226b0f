package config

import (
	"database/sql"
	"fmt"
	"os"
	"strconv"

	_ "github.com/lib/pq"
)

type Config struct {
	Environment string
	Port        string
	DatabaseURL string
	JWTSecret   string
	
	// Database config
	DBHost     string
	DBPort     int
	DBUser     string
	DBPassword string
	DBName     string
	DBSSLMode  string
	
	// External service URLs
	UserServiceURL           string
	ServiceCatalogServiceURL string
	PaymentServiceURL        string
	NotificationServiceURL   string
	
	// Business rules
	BookingAdvanceHours    int     // Minimum hours in advance for booking
	CancellationHours      int     // Hours before booking can be cancelled
	DefaultSlotDuration    int     // Default time slot duration in minutes
	MaxBookingsPerDay      int     // Maximum bookings per vendor per day
	CommissionRate         float64 // Platform commission rate
	
	// Redis config (for caching and real-time features)
	RedisURL string
}

func Load() *Config {
	return &Config{
		Environment: getEnv("ENVIRONMENT", "development"),
		Port:        getEnv("PORT", "8003"),
		DatabaseURL: getEnv("DATABASE_URL", ""),
		JWTSecret:   getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		
		DBHost:     getEnv("DB_HOST", "localhost"),
		DBPort:     getEnvAsInt("DB_PORT", 5432),
		DBUser:     getEnv("DB_USER", "shark_user"),
		DBPassword: getEnv("DB_PASSWORD", "shark_password"),
		DBName:     getEnv("DB_NAME", "shark_db"),
		DBSSLMode:  getEnv("DB_SSL_MODE", "disable"),
		
		UserServiceURL:           getEnv("USER_SERVICE_URL", "http://localhost:8001"),
		ServiceCatalogServiceURL: getEnv("SERVICE_CATALOG_SERVICE_URL", "http://localhost:8002"),
		PaymentServiceURL:        getEnv("PAYMENT_SERVICE_URL", "http://localhost:8004"),
		NotificationServiceURL:   getEnv("NOTIFICATION_SERVICE_URL", "http://localhost:8005"),
		
		BookingAdvanceHours: getEnvAsInt("BOOKING_ADVANCE_HOURS", 2),
		CancellationHours:   getEnvAsInt("CANCELLATION_HOURS", 24),
		DefaultSlotDuration: getEnvAsInt("DEFAULT_SLOT_DURATION", 60),
		MaxBookingsPerDay:   getEnvAsInt("MAX_BOOKINGS_PER_DAY", 10),
		CommissionRate:      getEnvAsFloat("COMMISSION_RATE", 0.15), // 15%
		
		RedisURL: getEnv("REDIS_URL", "redis://localhost:6379"),
	}
}

func InitDB(cfg *Config) (*sql.DB, error) {
	var dsn string
	
	if cfg.DatabaseURL != "" {
		dsn = cfg.DatabaseURL
	} else {
		dsn = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			cfg.DBHost, cfg.DBPort, cfg.DBUser, cfg.DBPassword, cfg.DBName, cfg.DBSSLMode)
	}
	
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}
	
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}
	
	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	
	return db, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsFloat(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}
