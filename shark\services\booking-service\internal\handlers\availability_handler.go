package handlers

import (
	"net/http"
	"time"

	"shark/booking-service/internal/middleware"
	"shark/booking-service/internal/models"
	"shark/booking-service/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type AvailabilityHandler struct {
	availabilityService *services.AvailabilityService
	validator           *validator.Validate
}

func NewAvailabilityHandler(availabilityService *services.AvailabilityService) *AvailabilityHandler {
	return &AvailabilityHandler{
		availabilityService: availabilityService,
		validator:           validator.New(),
	}
}

func (h *AvailabilityHandler) CreateAvailability(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	var req models.CreateAvailabilityRequest
	if err := c.<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	availability, err := h.availabilityService.CreateAvailability(userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Data:    availability,
		Message: "Availability created successfully",
	})
}

func (h *AvailabilityHandler) GetMyAvailability(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	availabilities, err := h.availabilityService.GetVendorAvailability(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get availability",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    availabilities,
	})
}

func (h *AvailabilityHandler) GetVendorAvailability(c *gin.Context) {
	vendorIDStr := c.Param("vendor_id")
	vendorID, err := uuid.Parse(vendorIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid vendor ID format",
		})
		return
	}
	
	availabilities, err := h.availabilityService.GetVendorAvailability(vendorID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get vendor availability",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    availabilities,
	})
}

func (h *AvailabilityHandler) UpdateAvailability(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	availabilityIDStr := c.Param("id")
	availabilityID, err := uuid.Parse(availabilityIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid availability ID format",
		})
		return
	}
	
	var req models.UpdateAvailabilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	availability, err := h.availabilityService.UpdateAvailability(userID, availabilityID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    availability,
		Message: "Availability updated successfully",
	})
}

func (h *AvailabilityHandler) DeleteAvailability(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	availabilityIDStr := c.Param("id")
	availabilityID, err := uuid.Parse(availabilityIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid availability ID format",
		})
		return
	}
	
	if err := h.availabilityService.DeleteAvailability(userID, availabilityID); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Availability deleted successfully",
	})
}

func (h *AvailabilityHandler) GetAvailableTimeSlots(c *gin.Context) {
	var req models.AvailabilityRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid query parameters",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	// Parse date from query parameter if provided as string
	dateStr := c.Query("date")
	if dateStr != "" {
		parsedDate, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Message: "Invalid date format, use YYYY-MM-DD",
			})
			return
		}
		req.Date = parsedDate
	}
	
	response, err := h.availabilityService.GetAvailableTimeSlots(req.VendorID, req.ServiceID, req.Date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get available time slots",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    response,
	})
}
