package handlers

import (
	"net/http"

	"shark/booking-service/internal/middleware"
	"shark/booking-service/internal/models"
	"shark/booking-service/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type BookingHandler struct {
	bookingService *services.BookingService
	validator      *validator.Validate
}

func NewBookingHandler(bookingService *services.BookingService) *BookingHandler {
	return &BookingHandler{
		bookingService: bookingService,
		validator:      validator.New(),
	}
}

func (h *BookingHandler) CreateBooking(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	var req models.CreateBookingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	booking, err := h.bookingService.CreateBooking(userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Data:    booking,
		Message: "Booking created successfully",
	})
}

func (h *BookingHandler) GetBooking(c *gin.Context) {
	bookingIDStr := c.Param("id")
	bookingID, err := uuid.Parse(bookingIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid booking ID format",
		})
		return
	}
	
	booking, err := h.bookingService.GetBooking(bookingID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "Booking not found",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    booking,
	})
}

func (h *BookingHandler) UpdateBooking(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	bookingIDStr := c.Param("id")
	bookingID, err := uuid.Parse(bookingIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid booking ID format",
		})
		return
	}
	
	var req models.UpdateBookingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	booking, err := h.bookingService.UpdateBooking(bookingID, userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    booking,
		Message: "Booking updated successfully",
	})
}

func (h *BookingHandler) CancelBooking(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	bookingIDStr := c.Param("id")
	bookingID, err := uuid.Parse(bookingIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid booking ID format",
		})
		return
	}
	
	if err := h.bookingService.CancelBooking(bookingID, userID); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Booking cancelled successfully",
	})
}

func (h *BookingHandler) SearchBookings(c *gin.Context) {
	var query models.BookingSearchQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid query parameters",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	// Validate query
	if err := h.validator.Struct(&query); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	response, err := h.bookingService.SearchBookings(&query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to search bookings",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    response,
	})
}

func (h *BookingHandler) GetMyBookings(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	// Get user roles to determine if they're a customer or vendor
	roles, _ := middleware.GetUserRoles(c)
	
	var query models.BookingSearchQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid query parameters",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	// Set user ID based on role
	hasVendorRole := false
	for _, role := range roles {
		if role == "vendor" {
			hasVendorRole = true
			break
		}
	}
	
	// If user has vendor role and no specific filter is set, show vendor bookings
	// Otherwise, show customer bookings
	if hasVendorRole && query.CustomerID == nil && query.VendorID == nil {
		query.VendorID = &userID
	} else if query.CustomerID == nil && query.VendorID == nil {
		query.CustomerID = &userID
	}
	
	response, err := h.bookingService.SearchBookings(&query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get bookings",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    response,
	})
}

func (h *BookingHandler) GetBookingStats(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}
	
	// Check if user has vendor role
	roles, _ := middleware.GetUserRoles(c)
	hasVendorRole := false
	for _, role := range roles {
		if role == "vendor" {
			hasVendorRole = true
			break
		}
	}
	
	var vendorID *uuid.UUID
	if hasVendorRole {
		vendorID = &userID
	}
	
	stats, err := h.bookingService.GetBookingStats(vendorID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get booking statistics",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    stats,
	})
}
