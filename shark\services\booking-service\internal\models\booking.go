package models

import (
	"time"

	"github.com/google/uuid"
)

// Booking represents a service booking
type Booking struct {
	ID          uuid.UUID `json:"id" db:"id"`
	CustomerID  uuid.UUID `json:"customer_id" db:"customer_id"`
	VendorID    uuid.UUID `json:"vendor_id" db:"vendor_id"`
	ServiceID   uuid.UUID `json:"service_id" db:"service_id"`
	ScheduledAt time.Time `json:"scheduled_at" db:"scheduled_at"`
	Duration    int       `json:"duration" db:"duration"` // in minutes
	TotalAmount float64   `json:"total_amount" db:"total_amount"`
	Status      string    `json:"status" db:"status"`
	Address     Address   `json:"address" db:"address"`
	Notes       *string   `json:"notes,omitempty" db:"notes"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	
	// Related data (populated when needed)
	Customer *UserInfo    `json:"customer,omitempty"`
	Vendor   *UserInfo    `json:"vendor,omitempty"`
	Service  *ServiceInfo `json:"service,omitempty"`
}

// Address represents a physical address
type Address struct {
	Street    string  `json:"street" validate:"required"`
	City      string  `json:"city" validate:"required"`
	State     string  `json:"state" validate:"required"`
	Country   string  `json:"country" validate:"required"`
	ZipCode   string  `json:"zip_code" validate:"required"`
	Latitude  *float64 `json:"latitude,omitempty"`
	Longitude *float64 `json:"longitude,omitempty"`
}

// UserInfo represents basic user information
type UserInfo struct {
	ID        uuid.UUID `json:"id"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Email     string    `json:"email"`
	Phone     *string   `json:"phone,omitempty"`
	Avatar    *string   `json:"avatar,omitempty"`
}

// ServiceInfo represents basic service information
type ServiceInfo struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Price       float64   `json:"price"`
	PriceType   string    `json:"price_type"`
	Duration    *int      `json:"duration,omitempty"`
	CategoryID  uuid.UUID `json:"category_id"`
}

// VendorAvailability represents vendor working hours
type VendorAvailability struct {
	ID          uuid.UUID `json:"id" db:"id"`
	VendorID    uuid.UUID `json:"vendor_id" db:"vendor_id"`
	DayOfWeek   int       `json:"day_of_week" db:"day_of_week"` // 0 = Sunday
	StartTime   string    `json:"start_time" db:"start_time"`   // HH:MM format
	EndTime     string    `json:"end_time" db:"end_time"`       // HH:MM format
	IsAvailable bool      `json:"is_available" db:"is_available"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// TimeSlot represents an available time slot
type TimeSlot struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Available bool      `json:"available"`
}

// Request/Response DTOs
type CreateBookingRequest struct {
	ServiceID   uuid.UUID `json:"service_id" validate:"required"`
	VendorID    uuid.UUID `json:"vendor_id" validate:"required"`
	ScheduledAt time.Time `json:"scheduled_at" validate:"required"`
	Address     Address   `json:"address" validate:"required"`
	Notes       *string   `json:"notes,omitempty"`
}

type UpdateBookingRequest struct {
	ScheduledAt *time.Time `json:"scheduled_at,omitempty"`
	Address     *Address   `json:"address,omitempty"`
	Notes       *string    `json:"notes,omitempty"`
	Status      *string    `json:"status,omitempty" validate:"omitempty,oneof=pending confirmed in_progress completed cancelled refunded"`
}

type BookingSearchQuery struct {
	CustomerID  *uuid.UUID `form:"customer_id"`
	VendorID    *uuid.UUID `form:"vendor_id"`
	ServiceID   *uuid.UUID `form:"service_id"`
	Status      string     `form:"status" validate:"omitempty,oneof=pending confirmed in_progress completed cancelled refunded"`
	DateFrom    *time.Time `form:"date_from"`
	DateTo      *time.Time `form:"date_to"`
	SortBy      string     `form:"sort_by" validate:"omitempty,oneof=scheduled_at created_at total_amount"`
	SortOrder   string     `form:"sort_order" validate:"omitempty,oneof=asc desc"`
	Page        int        `form:"page,default=1" validate:"min=1"`
	Limit       int        `form:"limit,default=20" validate:"min=1,max=100"`
}

type AvailabilityRequest struct {
	VendorID  uuid.UUID `json:"vendor_id" validate:"required"`
	ServiceID uuid.UUID `json:"service_id" validate:"required"`
	Date      time.Time `json:"date" validate:"required"`
}

type AvailabilityResponse struct {
	Date      time.Time  `json:"date"`
	TimeSlots []TimeSlot `json:"time_slots"`
}

type CreateAvailabilityRequest struct {
	DayOfWeek   int    `json:"day_of_week" validate:"required,min=0,max=6"`
	StartTime   string `json:"start_time" validate:"required"`
	EndTime     string `json:"end_time" validate:"required"`
	IsAvailable bool   `json:"is_available"`
}

type UpdateAvailabilityRequest struct {
	StartTime   *string `json:"start_time,omitempty"`
	EndTime     *string `json:"end_time,omitempty"`
	IsAvailable *bool   `json:"is_available,omitempty"`
}

// Booking Status Constants
const (
	BookingStatusPending    = "pending"
	BookingStatusConfirmed  = "confirmed"
	BookingStatusInProgress = "in_progress"
	BookingStatusCompleted  = "completed"
	BookingStatusCancelled  = "cancelled"
	BookingStatusRefunded   = "refunded"
)

// API Response wrapper
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
	Errors  []string    `json:"errors,omitempty"`
}

// Pagination
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"total_pages"`
}

// Booking Statistics
type BookingStats struct {
	TotalBookings     int64   `json:"total_bookings"`
	PendingBookings   int64   `json:"pending_bookings"`
	ConfirmedBookings int64   `json:"confirmed_bookings"`
	CompletedBookings int64   `json:"completed_bookings"`
	CancelledBookings int64   `json:"cancelled_bookings"`
	TotalRevenue      float64 `json:"total_revenue"`
	AvgBookingValue   float64 `json:"avg_booking_value"`
}

// JWT Claims (for authentication)
type JWTClaims struct {
	UserID uuid.UUID `json:"user_id"`
	Email  string    `json:"email"`
	Roles  []string  `json:"roles"`
	Exp    int64     `json:"exp"`
	Iat    int64     `json:"iat"`
}

// Notification types for booking events
type BookingNotification struct {
	BookingID uuid.UUID `json:"booking_id"`
	UserID    uuid.UUID `json:"user_id"`
	Type      string    `json:"type"`
	Message   string    `json:"message"`
	Data      map[string]interface{} `json:"data,omitempty"`
}
