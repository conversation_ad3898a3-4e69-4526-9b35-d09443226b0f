package repository

import (
	"database/sql"
	"shark/booking-service/internal/models"

	"github.com/google/uuid"
)

type AvailabilityRepository struct {
	db *sql.DB
}

func NewAvailabilityRepository(db *sql.DB) *AvailabilityRepository {
	return &AvailabilityRepository{db: db}
}

func (r *AvailabilityRepository) Create(availability *models.VendorAvailability) error {
	query := `
		INSERT INTO vendor_availability (id, vendor_id, day_of_week, start_time, end_time, is_available)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING created_at, updated_at`
	
	err := r.db.QueryRow(
		query,
		availability.ID,
		availability.VendorID,
		availability.DayOfWeek,
		availability.StartTime,
		availability.EndTime,
		availability.IsAvailable,
	).Scan(&availability.CreatedAt, &availability.UpdatedAt)
	
	return err
}

func (r *AvailabilityRepository) GetByID(id uuid.UUID) (*models.VendorAvailability, error) {
	availability := &models.VendorAvailability{}
	query := `
		SELECT id, vendor_id, day_of_week, start_time, end_time, is_available, created_at, updated_at
		FROM vendor_availability WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&availability.ID,
		&availability.VendorID,
		&availability.DayOfWeek,
		&availability.StartTime,
		&availability.EndTime,
		&availability.IsAvailable,
		&availability.CreatedAt,
		&availability.UpdatedAt,
	)
	
	return availability, err
}

func (r *AvailabilityRepository) Update(availability *models.VendorAvailability) error {
	query := `
		UPDATE vendor_availability 
		SET start_time = $2, end_time = $3, is_available = $4, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`
	
	err := r.db.QueryRow(
		query,
		availability.ID,
		availability.StartTime,
		availability.EndTime,
		availability.IsAvailable,
	).Scan(&availability.UpdatedAt)
	
	return err
}

func (r *AvailabilityRepository) Delete(id uuid.UUID) error {
	query := `DELETE FROM vendor_availability WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

func (r *AvailabilityRepository) GetByVendorID(vendorID uuid.UUID) ([]*models.VendorAvailability, error) {
	query := `
		SELECT id, vendor_id, day_of_week, start_time, end_time, is_available, created_at, updated_at
		FROM vendor_availability 
		WHERE vendor_id = $1 AND is_available = true
		ORDER BY day_of_week ASC, start_time ASC`
	
	rows, err := r.db.Query(query, vendorID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var availabilities []*models.VendorAvailability
	for rows.Next() {
		availability := &models.VendorAvailability{}
		err := rows.Scan(
			&availability.ID,
			&availability.VendorID,
			&availability.DayOfWeek,
			&availability.StartTime,
			&availability.EndTime,
			&availability.IsAvailable,
			&availability.CreatedAt,
			&availability.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		availabilities = append(availabilities, availability)
	}
	
	return availabilities, nil
}

func (r *AvailabilityRepository) GetByVendorAndDay(vendorID uuid.UUID, dayOfWeek int) ([]*models.VendorAvailability, error) {
	query := `
		SELECT id, vendor_id, day_of_week, start_time, end_time, is_available, created_at, updated_at
		FROM vendor_availability 
		WHERE vendor_id = $1 AND day_of_week = $2 AND is_available = true
		ORDER BY start_time ASC`
	
	rows, err := r.db.Query(query, vendorID, dayOfWeek)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var availabilities []*models.VendorAvailability
	for rows.Next() {
		availability := &models.VendorAvailability{}
		err := rows.Scan(
			&availability.ID,
			&availability.VendorID,
			&availability.DayOfWeek,
			&availability.StartTime,
			&availability.EndTime,
			&availability.IsAvailable,
			&availability.CreatedAt,
			&availability.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		availabilities = append(availabilities, availability)
	}
	
	return availabilities, nil
}

func (r *AvailabilityRepository) ExistsForVendorAndDay(vendorID uuid.UUID, dayOfWeek int, startTime, endTime string) (bool, error) {
	var exists bool
	query := `
		SELECT EXISTS(
			SELECT 1 FROM vendor_availability 
			WHERE vendor_id = $1 AND day_of_week = $2 
			AND ((start_time <= $3 AND end_time > $3) OR (start_time < $4 AND end_time >= $4) OR (start_time >= $3 AND end_time <= $4))
		)`
	
	err := r.db.QueryRow(query, vendorID, dayOfWeek, startTime, endTime).Scan(&exists)
	return exists, err
}

func (r *AvailabilityRepository) DeleteByVendorAndDay(vendorID uuid.UUID, dayOfWeek int) error {
	query := `DELETE FROM vendor_availability WHERE vendor_id = $1 AND day_of_week = $2`
	_, err := r.db.Exec(query, vendorID, dayOfWeek)
	return err
}
