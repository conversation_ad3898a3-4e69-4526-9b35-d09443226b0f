package repository

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"shark/booking-service/internal/models"
	"strings"
	"time"

	"github.com/google/uuid"
)

type BookingRepository struct {
	db *sql.DB
}

func NewBookingRepository(db *sql.DB) *BookingRepository {
	return &BookingRepository{db: db}
}

func (r *BookingRepository) Create(booking *models.Booking) error {
	// Serialize address to JSON
	addressJSON, err := json.Marshal(booking.Address)
	if err != nil {
		return err
	}
	
	query := `
		INSERT INTO bookings (id, customer_id, vendor_id, service_id, scheduled_at, duration, 
		                     total_amount, status, address, notes)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING created_at, updated_at`
	
	err = r.db.QueryRow(
		query,
		booking.ID,
		booking.CustomerID,
		booking.VendorID,
		booking.ServiceID,
		booking.ScheduledAt,
		booking.Duration,
		booking.TotalAmount,
		booking.Status,
		addressJSON,
		booking.Notes,
	).Scan(&booking.CreatedAt, &booking.UpdatedAt)
	
	return err
}

func (r *BookingRepository) GetByID(id uuid.UUID) (*models.Booking, error) {
	booking := &models.Booking{}
	var addressJSON []byte
	
	query := `
		SELECT id, customer_id, vendor_id, service_id, scheduled_at, duration, 
		       total_amount, status, address, notes, created_at, updated_at
		FROM bookings WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&booking.ID,
		&booking.CustomerID,
		&booking.VendorID,
		&booking.ServiceID,
		&booking.ScheduledAt,
		&booking.Duration,
		&booking.TotalAmount,
		&booking.Status,
		&addressJSON,
		&booking.Notes,
		&booking.CreatedAt,
		&booking.UpdatedAt,
	)
	
	if err != nil {
		return nil, err
	}
	
	// Deserialize address from JSON
	if err := json.Unmarshal(addressJSON, &booking.Address); err != nil {
		return nil, err
	}
	
	return booking, nil
}

func (r *BookingRepository) Update(booking *models.Booking) error {
	// Serialize address to JSON
	addressJSON, err := json.Marshal(booking.Address)
	if err != nil {
		return err
	}
	
	query := `
		UPDATE bookings 
		SET scheduled_at = $2, duration = $3, total_amount = $4, status = $5, 
		    address = $6, notes = $7, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`
	
	err = r.db.QueryRow(
		query,
		booking.ID,
		booking.ScheduledAt,
		booking.Duration,
		booking.TotalAmount,
		booking.Status,
		addressJSON,
		booking.Notes,
	).Scan(&booking.UpdatedAt)
	
	return err
}

func (r *BookingRepository) Delete(id uuid.UUID) error {
	query := `DELETE FROM bookings WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

func (r *BookingRepository) Search(searchQuery *models.BookingSearchQuery) ([]*models.Booking, int64, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1
	
	if searchQuery.CustomerID != nil {
		conditions = append(conditions, fmt.Sprintf("customer_id = $%d", argIndex))
		args = append(args, *searchQuery.CustomerID)
		argIndex++
	}
	
	if searchQuery.VendorID != nil {
		conditions = append(conditions, fmt.Sprintf("vendor_id = $%d", argIndex))
		args = append(args, *searchQuery.VendorID)
		argIndex++
	}
	
	if searchQuery.ServiceID != nil {
		conditions = append(conditions, fmt.Sprintf("service_id = $%d", argIndex))
		args = append(args, *searchQuery.ServiceID)
		argIndex++
	}
	
	if searchQuery.Status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, searchQuery.Status)
		argIndex++
	}
	
	if searchQuery.DateFrom != nil {
		conditions = append(conditions, fmt.Sprintf("scheduled_at >= $%d", argIndex))
		args = append(args, *searchQuery.DateFrom)
		argIndex++
	}
	
	if searchQuery.DateTo != nil {
		conditions = append(conditions, fmt.Sprintf("scheduled_at <= $%d", argIndex))
		args = append(args, *searchQuery.DateTo)
		argIndex++
	}
	
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}
	
	// Get total count
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM bookings %s", whereClause)
	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}
	
	// Build ORDER BY clause
	orderBy := "ORDER BY scheduled_at DESC"
	if searchQuery.SortBy != "" {
		direction := "ASC"
		if searchQuery.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("ORDER BY %s %s", searchQuery.SortBy, direction)
	}
	
	// Get bookings
	offset := (searchQuery.Page - 1) * searchQuery.Limit
	bookingQuery := fmt.Sprintf(`
		SELECT id, customer_id, vendor_id, service_id, scheduled_at, duration, 
		       total_amount, status, address, notes, created_at, updated_at
		FROM bookings 
		%s %s
		LIMIT $%d OFFSET $%d`, whereClause, orderBy, argIndex, argIndex+1)
	
	args = append(args, searchQuery.Limit, offset)
	
	rows, err := r.db.Query(bookingQuery, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()
	
	var bookings []*models.Booking
	for rows.Next() {
		booking := &models.Booking{}
		var addressJSON []byte
		
		err := rows.Scan(
			&booking.ID,
			&booking.CustomerID,
			&booking.VendorID,
			&booking.ServiceID,
			&booking.ScheduledAt,
			&booking.Duration,
			&booking.TotalAmount,
			&booking.Status,
			&addressJSON,
			&booking.Notes,
			&booking.CreatedAt,
			&booking.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}
		
		// Deserialize address from JSON
		if err := json.Unmarshal(addressJSON, &booking.Address); err != nil {
			return nil, 0, err
		}
		
		bookings = append(bookings, booking)
	}
	
	return bookings, total, nil
}

func (r *BookingRepository) GetVendorBookings(vendorID uuid.UUID, date time.Time) ([]*models.Booking, error) {
	// Get bookings for a specific vendor on a specific date
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)
	
	query := `
		SELECT id, customer_id, vendor_id, service_id, scheduled_at, duration, 
		       total_amount, status, address, notes, created_at, updated_at
		FROM bookings 
		WHERE vendor_id = $1 AND scheduled_at >= $2 AND scheduled_at < $3
		AND status NOT IN ('cancelled', 'refunded')
		ORDER BY scheduled_at ASC`
	
	rows, err := r.db.Query(query, vendorID, startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var bookings []*models.Booking
	for rows.Next() {
		booking := &models.Booking{}
		var addressJSON []byte
		
		err := rows.Scan(
			&booking.ID,
			&booking.CustomerID,
			&booking.VendorID,
			&booking.ServiceID,
			&booking.ScheduledAt,
			&booking.Duration,
			&booking.TotalAmount,
			&booking.Status,
			&addressJSON,
			&booking.Notes,
			&booking.CreatedAt,
			&booking.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		
		// Deserialize address from JSON
		if err := json.Unmarshal(addressJSON, &booking.Address); err != nil {
			return nil, err
		}
		
		bookings = append(bookings, booking)
	}
	
	return bookings, nil
}

func (r *BookingRepository) GetStats(vendorID *uuid.UUID) (*models.BookingStats, error) {
	stats := &models.BookingStats{}
	
	var query string
	var args []interface{}
	
	if vendorID != nil {
		query = `
			SELECT 
				COUNT(*) as total_bookings,
				COUNT(*) FILTER (WHERE status = 'pending') as pending_bookings,
				COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_bookings,
				COUNT(*) FILTER (WHERE status = 'completed') as completed_bookings,
				COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_bookings,
				COALESCE(SUM(total_amount) FILTER (WHERE status = 'completed'), 0) as total_revenue,
				COALESCE(AVG(total_amount), 0) as avg_booking_value
			FROM bookings WHERE vendor_id = $1`
		args = []interface{}{*vendorID}
	} else {
		query = `
			SELECT 
				COUNT(*) as total_bookings,
				COUNT(*) FILTER (WHERE status = 'pending') as pending_bookings,
				COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_bookings,
				COUNT(*) FILTER (WHERE status = 'completed') as completed_bookings,
				COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_bookings,
				COALESCE(SUM(total_amount) FILTER (WHERE status = 'completed'), 0) as total_revenue,
				COALESCE(AVG(total_amount), 0) as avg_booking_value
			FROM bookings`
	}
	
	err := r.db.QueryRow(query, args...).Scan(
		&stats.TotalBookings,
		&stats.PendingBookings,
		&stats.ConfirmedBookings,
		&stats.CompletedBookings,
		&stats.CancelledBookings,
		&stats.TotalRevenue,
		&stats.AvgBookingValue,
	)
	
	return stats, err
}
