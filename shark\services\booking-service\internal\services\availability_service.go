package services

import (
	"errors"
	"shark/booking-service/internal/config"
	"shark/booking-service/internal/models"
	"shark/booking-service/internal/repository"
	"time"

	"github.com/google/uuid"
)

type AvailabilityService struct {
	availabilityRepo *repository.AvailabilityRepository
	bookingRepo      *repository.BookingRepository
	config           *config.Config
}

func NewAvailabilityService(availabilityRepo *repository.AvailabilityRepository, bookingRepo *repository.BookingRepository, cfg *config.Config) *AvailabilityService {
	return &AvailabilityService{
		availabilityRepo: availabilityRepo,
		bookingRepo:      bookingRepo,
		config:           cfg,
	}
}

func (s *AvailabilityService) CreateAvailability(vendorID uuid.UUID, req *models.CreateAvailabilityRequest) (*models.VendorAvailability, error) {
	// Validate time format
	if !isValidTimeFormat(req.StartTime) || !isValidTimeFormat(req.EndTime) {
		return nil, errors.New("invalid time format, use HH:MM")
	}
	
	// Validate start time is before end time
	if req.StartTime >= req.EndTime {
		return nil, errors.New("start time must be before end time")
	}
	
	// Check for overlapping availability
	exists, err := s.availabilityRepo.ExistsForVendorAndDay(vendorID, req.DayOfWeek, req.StartTime, req.EndTime)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("overlapping availability already exists for this time slot")
	}
	
	availability := &models.VendorAvailability{
		ID:          uuid.New(),
		VendorID:    vendorID,
		DayOfWeek:   req.DayOfWeek,
		StartTime:   req.StartTime,
		EndTime:     req.EndTime,
		IsAvailable: req.IsAvailable,
	}
	
	if err := s.availabilityRepo.Create(availability); err != nil {
		return nil, err
	}
	
	return availability, nil
}

func (s *AvailabilityService) GetVendorAvailability(vendorID uuid.UUID) ([]*models.VendorAvailability, error) {
	return s.availabilityRepo.GetByVendorID(vendorID)
}

func (s *AvailabilityService) UpdateAvailability(vendorID uuid.UUID, availabilityID uuid.UUID, req *models.UpdateAvailabilityRequest) (*models.VendorAvailability, error) {
	// Get existing availability
	availability, err := s.availabilityRepo.GetByID(availabilityID)
	if err != nil {
		return nil, err
	}
	
	// Check ownership
	if availability.VendorID != vendorID {
		return nil, errors.New("unauthorized: availability belongs to another vendor")
	}
	
	// Update fields if provided
	if req.StartTime != nil {
		if !isValidTimeFormat(*req.StartTime) {
			return nil, errors.New("invalid start time format, use HH:MM")
		}
		availability.StartTime = *req.StartTime
	}
	
	if req.EndTime != nil {
		if !isValidTimeFormat(*req.EndTime) {
			return nil, errors.New("invalid end time format, use HH:MM")
		}
		availability.EndTime = *req.EndTime
	}
	
	if req.IsAvailable != nil {
		availability.IsAvailable = *req.IsAvailable
	}
	
	// Validate start time is before end time
	if availability.StartTime >= availability.EndTime {
		return nil, errors.New("start time must be before end time")
	}
	
	if err := s.availabilityRepo.Update(availability); err != nil {
		return nil, err
	}
	
	return availability, nil
}

func (s *AvailabilityService) DeleteAvailability(vendorID uuid.UUID, availabilityID uuid.UUID) error {
	// Get existing availability
	availability, err := s.availabilityRepo.GetByID(availabilityID)
	if err != nil {
		return err
	}
	
	// Check ownership
	if availability.VendorID != vendorID {
		return errors.New("unauthorized: availability belongs to another vendor")
	}
	
	return s.availabilityRepo.Delete(availabilityID)
}

func (s *AvailabilityService) GetAvailableTimeSlots(vendorID uuid.UUID, serviceID uuid.UUID, date time.Time) (*models.AvailabilityResponse, error) {
	// Get day of week (0 = Sunday)
	dayOfWeek := int(date.Weekday())
	
	// Get vendor availability for this day
	availabilities, err := s.availabilityRepo.GetByVendorAndDay(vendorID, dayOfWeek)
	if err != nil {
		return nil, err
	}
	
	if len(availabilities) == 0 {
		return &models.AvailabilityResponse{
			Date:      date,
			TimeSlots: []models.TimeSlot{},
		}, nil
	}
	
	// Get existing bookings for this date
	existingBookings, err := s.bookingRepo.GetVendorBookings(vendorID, date)
	if err != nil {
		return nil, err
	}
	
	// Generate time slots
	var timeSlots []models.TimeSlot
	
	for _, availability := range availabilities {
		slots := s.generateTimeSlots(date, availability.StartTime, availability.EndTime, s.config.DefaultSlotDuration)
		
		// Mark slots as unavailable if they conflict with existing bookings
		for i := range slots {
			slots[i].Available = !s.hasTimeSlotConflict(slots[i], existingBookings)
		}
		
		timeSlots = append(timeSlots, slots...)
	}
	
	return &models.AvailabilityResponse{
		Date:      date,
		TimeSlots: timeSlots,
	}, nil
}

func (s *AvailabilityService) generateTimeSlots(date time.Time, startTime, endTime string, slotDuration int) []models.TimeSlot {
	var slots []models.TimeSlot
	
	// Parse start and end times
	start, _ := time.Parse("15:04", startTime)
	end, _ := time.Parse("15:04", endTime)
	
	// Create datetime objects for the specific date
	startDateTime := time.Date(date.Year(), date.Month(), date.Day(), start.Hour(), start.Minute(), 0, 0, date.Location())
	endDateTime := time.Date(date.Year(), date.Month(), date.Day(), end.Hour(), end.Minute(), 0, 0, date.Location())
	
	// Generate slots
	current := startDateTime
	for current.Add(time.Duration(slotDuration)*time.Minute).Before(endDateTime) || current.Add(time.Duration(slotDuration)*time.Minute).Equal(endDateTime) {
		slot := models.TimeSlot{
			StartTime: current,
			EndTime:   current.Add(time.Duration(slotDuration) * time.Minute),
			Available: true, // Will be updated based on conflicts
		}
		slots = append(slots, slot)
		current = current.Add(time.Duration(slotDuration) * time.Minute)
	}
	
	return slots
}

func (s *AvailabilityService) hasTimeSlotConflict(slot models.TimeSlot, bookings []*models.Booking) bool {
	for _, booking := range bookings {
		bookingEnd := booking.ScheduledAt.Add(time.Duration(booking.Duration) * time.Minute)
		
		// Check if times overlap
		if slot.StartTime.Before(bookingEnd) && slot.EndTime.After(booking.ScheduledAt) {
			return true
		}
	}
	return false
}

func (s *AvailabilityService) SetWeeklyAvailability(vendorID uuid.UUID, weeklySchedule map[int][]models.CreateAvailabilityRequest) error {
	// This is a bulk operation to set availability for the entire week
	for dayOfWeek, daySchedule := range weeklySchedule {
		// Delete existing availability for this day
		if err := s.availabilityRepo.DeleteByVendorAndDay(vendorID, dayOfWeek); err != nil {
			return err
		}
		
		// Create new availability slots for this day
		for _, req := range daySchedule {
			req.DayOfWeek = dayOfWeek
			_, err := s.CreateAvailability(vendorID, &req)
			if err != nil {
				return err
			}
		}
	}
	
	return nil
}

// Helper function to validate time format (HH:MM)
func isValidTimeFormat(timeStr string) bool {
	_, err := time.Parse("15:04", timeStr)
	return err == nil
}
