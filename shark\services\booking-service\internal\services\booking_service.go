package services

import (
	"errors"
	"fmt"
	"shark/booking-service/internal/config"
	"shark/booking-service/internal/models"
	"shark/booking-service/internal/repository"
	"time"

	"github.com/google/uuid"
)

type BookingService struct {
	bookingRepo      *repository.BookingRepository
	availabilityRepo *repository.AvailabilityRepository
	config           *config.Config
}

func NewBookingService(bookingRepo *repository.BookingRepository, availabilityRepo *repository.AvailabilityRepository, cfg *config.Config) *BookingService {
	return &BookingService{
		bookingRepo:      bookingRepo,
		availabilityRepo: availabilityRepo,
		config:           cfg,
	}
}

func (s *BookingService) CreateBooking(customerID uuid.UUID, req *models.CreateBookingRequest) (*models.Booking, error) {
	// Validate booking time is in the future
	if req.ScheduledAt.Before(time.Now().Add(time.Duration(s.config.BookingAdvanceHours) * time.Hour)) {
		return nil, fmt.Errorf("booking must be at least %d hours in advance", s.config.BookingAdvanceHours)
	}
	
	// Check vendor availability
	available, err := s.IsVendorAvailable(req.VendorID, req.ScheduledAt)
	if err != nil {
		return nil, err
	}
	if !available {
		return nil, errors.New("vendor is not available at the requested time")
	}
	
	// TODO: Get service details from service catalog service
	// For now, we'll use placeholder values
	serviceDuration := 60 // minutes
	servicePrice := 1000.0 // amount
	
	// Create booking
	booking := &models.Booking{
		ID:          uuid.New(),
		CustomerID:  customerID,
		VendorID:    req.VendorID,
		ServiceID:   req.ServiceID,
		ScheduledAt: req.ScheduledAt,
		Duration:    serviceDuration,
		TotalAmount: servicePrice,
		Status:      models.BookingStatusPending,
		Address:     req.Address,
		Notes:       req.Notes,
	}
	
	if err := s.bookingRepo.Create(booking); err != nil {
		return nil, err
	}
	
	// TODO: Send notification to vendor
	// TODO: Create payment intent
	
	return booking, nil
}

func (s *BookingService) GetBooking(id uuid.UUID) (*models.Booking, error) {
	booking, err := s.bookingRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	// TODO: Load related data (customer, vendor, service info)
	
	return booking, nil
}

func (s *BookingService) UpdateBooking(bookingID uuid.UUID, userID uuid.UUID, req *models.UpdateBookingRequest) (*models.Booking, error) {
	// Get existing booking
	booking, err := s.bookingRepo.GetByID(bookingID)
	if err != nil {
		return nil, err
	}
	
	// Check permissions (customer or vendor can update)
	if booking.CustomerID != userID && booking.VendorID != userID {
		return nil, errors.New("unauthorized: you can only update your own bookings")
	}
	
	// Validate status transitions
	if req.Status != nil {
		if err := s.validateStatusTransition(booking.Status, *req.Status, userID, booking); err != nil {
			return nil, err
		}
		booking.Status = *req.Status
	}
	
	// Update scheduled time if provided
	if req.ScheduledAt != nil {
		// Only allow rescheduling if booking is pending or confirmed
		if booking.Status != models.BookingStatusPending && booking.Status != models.BookingStatusConfirmed {
			return nil, errors.New("cannot reschedule booking in current status")
		}
		
		// Check if new time is available
		available, err := s.IsVendorAvailable(booking.VendorID, *req.ScheduledAt)
		if err != nil {
			return nil, err
		}
		if !available {
			return nil, errors.New("vendor is not available at the requested time")
		}
		
		booking.ScheduledAt = *req.ScheduledAt
	}
	
	// Update address if provided
	if req.Address != nil {
		booking.Address = *req.Address
	}
	
	// Update notes if provided
	if req.Notes != nil {
		booking.Notes = req.Notes
	}
	
	if err := s.bookingRepo.Update(booking); err != nil {
		return nil, err
	}
	
	// TODO: Send notifications based on status change
	
	return booking, nil
}

func (s *BookingService) CancelBooking(bookingID uuid.UUID, userID uuid.UUID) error {
	booking, err := s.bookingRepo.GetByID(bookingID)
	if err != nil {
		return err
	}
	
	// Check permissions
	if booking.CustomerID != userID && booking.VendorID != userID {
		return errors.New("unauthorized: you can only cancel your own bookings")
	}
	
	// Check if cancellation is allowed
	if booking.Status == models.BookingStatusCompleted {
		return errors.New("cannot cancel completed booking")
	}
	
	if booking.Status == models.BookingStatusCancelled {
		return errors.New("booking is already cancelled")
	}
	
	// Check cancellation time limit
	if time.Until(booking.ScheduledAt) < time.Duration(s.config.CancellationHours)*time.Hour {
		return fmt.Errorf("booking can only be cancelled at least %d hours in advance", s.config.CancellationHours)
	}
	
	// Update status to cancelled
	booking.Status = models.BookingStatusCancelled
	
	if err := s.bookingRepo.Update(booking); err != nil {
		return err
	}
	
	// TODO: Process refund if payment was made
	// TODO: Send cancellation notifications
	
	return nil
}

func (s *BookingService) SearchBookings(query *models.BookingSearchQuery) (*models.PaginatedResponse, error) {
	bookings, total, err := s.bookingRepo.Search(query)
	if err != nil {
		return nil, err
	}
	
	// TODO: Load related data for each booking
	
	totalPages := int(total) / query.Limit
	if int(total)%query.Limit > 0 {
		totalPages++
	}
	
	return &models.PaginatedResponse{
		Data:       bookings,
		Total:      total,
		Page:       query.Page,
		Limit:      query.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *BookingService) GetBookingStats(vendorID *uuid.UUID) (*models.BookingStats, error) {
	return s.bookingRepo.GetStats(vendorID)
}

func (s *BookingService) IsVendorAvailable(vendorID uuid.UUID, scheduledAt time.Time) (bool, error) {
	// Get day of week (0 = Sunday)
	dayOfWeek := int(scheduledAt.Weekday())
	
	// Get vendor availability for this day
	availabilities, err := s.availabilityRepo.GetByVendorAndDay(vendorID, dayOfWeek)
	if err != nil {
		return false, err
	}
	
	if len(availabilities) == 0 {
		return false, nil // No availability set for this day
	}
	
	// Check if requested time falls within any availability window
	requestedTime := scheduledAt.Format("15:04")
	
	for _, availability := range availabilities {
		if requestedTime >= availability.StartTime && requestedTime < availability.EndTime {
			// Check for conflicting bookings
			hasConflict, err := s.hasBookingConflict(vendorID, scheduledAt)
			if err != nil {
				return false, err
			}
			return !hasConflict, nil
		}
	}
	
	return false, nil
}

func (s *BookingService) hasBookingConflict(vendorID uuid.UUID, scheduledAt time.Time) (bool, error) {
	// Get existing bookings for the vendor on this date
	bookings, err := s.bookingRepo.GetVendorBookings(vendorID, scheduledAt)
	if err != nil {
		return false, err
	}
	
	// Check for time conflicts
	for _, booking := range bookings {
		bookingEnd := booking.ScheduledAt.Add(time.Duration(booking.Duration) * time.Minute)
		requestedEnd := scheduledAt.Add(time.Duration(s.config.DefaultSlotDuration) * time.Minute)
		
		// Check if times overlap
		if scheduledAt.Before(bookingEnd) && requestedEnd.After(booking.ScheduledAt) {
			return true, nil
		}
	}
	
	return false, nil
}

func (s *BookingService) validateStatusTransition(currentStatus, newStatus string, userID uuid.UUID, booking *models.Booking) error {
	// Define allowed transitions
	allowedTransitions := map[string][]string{
		models.BookingStatusPending: {
			models.BookingStatusConfirmed,
			models.BookingStatusCancelled,
		},
		models.BookingStatusConfirmed: {
			models.BookingStatusInProgress,
			models.BookingStatusCancelled,
		},
		models.BookingStatusInProgress: {
			models.BookingStatusCompleted,
			models.BookingStatusCancelled,
		},
	}
	
	// Check if transition is allowed
	allowed, exists := allowedTransitions[currentStatus]
	if !exists {
		return fmt.Errorf("no transitions allowed from status: %s", currentStatus)
	}
	
	isAllowed := false
	for _, status := range allowed {
		if status == newStatus {
			isAllowed = true
			break
		}
	}
	
	if !isAllowed {
		return fmt.Errorf("transition from %s to %s is not allowed", currentStatus, newStatus)
	}
	
	// Check role-based permissions
	switch newStatus {
	case models.BookingStatusConfirmed:
		// Only vendor can confirm
		if booking.VendorID != userID {
			return errors.New("only vendor can confirm booking")
		}
	case models.BookingStatusInProgress:
		// Only vendor can mark as in progress
		if booking.VendorID != userID {
			return errors.New("only vendor can mark booking as in progress")
		}
	case models.BookingStatusCompleted:
		// Only vendor can mark as completed
		if booking.VendorID != userID {
			return errors.New("only vendor can mark booking as completed")
		}
	}
	
	return nil
}
