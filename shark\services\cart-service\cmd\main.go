package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"shark/cart-service/internal/handlers"
	"shark/cart-service/internal/repository"
	"shark/cart-service/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
	"github.com/rs/cors"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Connect to database
	db, err := connectToDatabase()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize repositories
	cartRepo := repository.NewCartRepository(db)
	couponRepo := repository.NewCouponRepository(db)
	taxRepo := repository.NewTaxRepository(db)
	feeRepo := repository.NewFeeRepository(db)

	// Initialize services
	cartService := service.NewCartService(cartRepo, couponRepo, taxRepo, feeRepo)

	// Initialize handlers
	cartHandler := handlers.NewCartHandler(cartService)

	// Setup router
	router := setupRouter(cartHandler)

	// Start server
	port := getEnv("PORT", "8080")
	log.Printf("Cart Service starting on port %s", port)
	log.Fatal(router.Run(":" + port))
}

func setupRouter(cartHandler *handlers.CartHandler) *gin.Engine {
	// Set Gin mode
	if getEnv("GIN_MODE", "debug") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// CORS middleware
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})
	router.Use(func(ctx *gin.Context) {
		c.HandlerFunc(ctx.Writer, ctx.Request)
		ctx.Next()
	})

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "cart-service",
		})
	})

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Cart routes
		cart := v1.Group("/cart")
		{
			cart.GET("", cartHandler.GetCart)
			cart.DELETE("", cartHandler.ClearCart)
			cart.GET("/summary", cartHandler.GetCartSummary)

			// Product routes
			cart.POST("/products", cartHandler.AddProductToCart)

			// Service routes
			cart.POST("/services", cartHandler.AddServiceToCart)

			// Item management
			cart.PUT("/items/:itemId", cartHandler.UpdateCartItem)
			cart.DELETE("/items/:itemId", cartHandler.RemoveCartItem)
			cart.DELETE("/services/:serviceId", cartHandler.RemoveCartService)

			// Coupon routes
			cart.POST("/coupons", cartHandler.ApplyCoupon)

			// Shipping routes
			cart.POST("/shipping/calculate", cartHandler.CalculateShipping)
		}
	}

	return router
}

func connectToDatabase() (*sql.DB, error) {
	// This would use the shared database connection
	// For now, we'll create a simple connection
	host := getEnv("DB_HOST", "localhost")
	port := getEnv("DB_PORT", "5433")
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "")
	dbname := getEnv("DB_NAME", "dodo")
	sslmode := getEnv("DB_SSL_MODE", "disable")

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		host, port, user, password, dbname, sslmode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	return db, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
