package handlers

import (
	"fmt"
	"net/http"
	"shark/cart-service/internal/models"
	"shark/cart-service/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type CartHandler struct {
	cartService *service.CartService
}

func NewCartHandler(cartService *service.CartService) *CartHandler {
	return &CartHandler{
		cartService: cartService,
	}
}

// GET /api/v1/cart
func (h *CartHandler) GetCart(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	cart, err := h.cartService.GetCart(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.CartResponse{
			Success: false,
			Message: "Failed to get cart",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.<PERSON>(http.StatusOK, models.CartResponse{
		Success: true,
		Data:    cart,
		Message: "Cart retrieved successfully",
	})
}

// POST /api/v1/cart/products
func (h *CartHandler) AddProductToCart(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.AddProductToCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	item, err := h.cartService.AddProductToCart(userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Failed to add product to cart",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    item,
		"message": "Product added to cart successfully",
	})
}

// POST /api/v1/cart/services
func (h *CartHandler) AddServiceToCart(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.AddServiceToCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	service, err := h.cartService.AddServiceToCart(userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Failed to add service to cart",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    service,
		"message": "Service added to cart successfully",
	})
}

// PUT /api/v1/cart/items/:itemId
func (h *CartHandler) UpdateCartItem(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	itemIDStr := c.Param("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Invalid item ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.UpdateCartItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	err = h.cartService.UpdateCartItem(userID, itemID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Failed to update cart item",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cart item updated successfully",
	})
}

// DELETE /api/v1/cart/items/:itemId
func (h *CartHandler) RemoveCartItem(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	itemIDStr := c.Param("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Invalid item ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	err = h.cartService.RemoveCartItem(userID, itemID)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Failed to remove cart item",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cart item removed successfully",
	})
}

// DELETE /api/v1/cart/services/:serviceId
func (h *CartHandler) RemoveCartService(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	serviceIDStr := c.Param("serviceId")
	serviceID, err := uuid.Parse(serviceIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Invalid service ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	err = h.cartService.RemoveCartService(userID, serviceID)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Failed to remove cart service",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cart service removed successfully",
	})
}

// DELETE /api/v1/cart
func (h *CartHandler) ClearCart(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	err = h.cartService.ClearCart(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.CartResponse{
			Success: false,
			Message: "Failed to clear cart",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cart cleared successfully",
	})
}

// POST /api/v1/cart/coupons
func (h *CartHandler) ApplyCoupon(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.ApplyCouponRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	summary, err := h.cartService.ApplyCoupon(userID, req.CouponCode)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartSummaryResponse{
			Success: false,
			Message: "Failed to apply coupon",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.CartSummaryResponse{
		Success: true,
		Data:    summary,
		Message: "Coupon applied successfully",
	})
}

// GET /api/v1/cart/summary
func (h *CartHandler) GetCartSummary(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	cart, err := h.cartService.GetCart(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.CartSummaryResponse{
			Success: false,
			Message: "Failed to get cart summary",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.CartSummaryResponse{
		Success: true,
		Data:    cart.Summary,
		Message: "Cart summary retrieved successfully",
	})
}

// POST /api/v1/cart/shipping/calculate
func (h *CartHandler) CalculateShipping(c *gin.Context) {
	userID, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CartResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.CalculateShippingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.CartResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	summary, err := h.cartService.CalculateShipping(userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CartSummaryResponse{
			Success: false,
			Message: "Failed to calculate shipping",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.CartSummaryResponse{
		Success: true,
		Data:    summary,
		Message: "Shipping calculated successfully",
	})
}

// Helper methods
func (h *CartHandler) getUserID(c *gin.Context) (uuid.UUID, error) {
	// Extract user ID from JWT token or session
	// This is a simplified implementation
	userIDStr := c.GetHeader("X-User-ID")
	if userIDStr == "" {
		// Try to get from query parameter for testing
		userIDStr = c.Query("user_id")
	}

	if userIDStr == "" {
		return uuid.Nil, fmt.Errorf("user ID not found")
	}

	return uuid.Parse(userIDStr)
}

func (h *CartHandler) validatePagination(c *gin.Context) (int, int, error) {
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	return page, limit, nil
}
