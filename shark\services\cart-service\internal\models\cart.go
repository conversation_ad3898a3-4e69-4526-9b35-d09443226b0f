package models

import (
	"time"

	"github.com/google/uuid"
)

// Cart represents a shopping cart
type Cart struct {
	ID        uuid.UUID `json:"id" db:"id"`
	UserID    uuid.UUID `json:"user_id" db:"user_id"`
	SessionID *string   `json:"session_id,omitempty" db:"session_id"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`

	// Related data
	Items    []CartItem    `json:"items,omitempty"`
	Services []CartService `json:"services,omitempty"`
	Summary  *CartSummary  `json:"summary,omitempty"`
}

// CartItem represents a product in the cart
type CartItem struct {
	ID               uuid.UUID        `json:"id" db:"id"`
	CartID           uuid.UUID        `json:"cart_id" db:"cart_id"`
	ProductID        uuid.UUID        `json:"product_id" db:"product_id"`
	VendorID         uuid.UUID        `json:"vendor_id" db:"vendor_id"`
	ProductVariantID *uuid.UUID       `json:"product_variant_id,omitempty" db:"product_variant_id"`
	Quantity         int              `json:"quantity" db:"quantity"`
	UnitPrice        float64          `json:"unit_price" db:"unit_price"`
	TotalPrice       float64          `json:"total_price" db:"total_price"`
	CreatedAt        time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time        `json:"updated_at" db:"updated_at"`

	// Related data
	Product     *ProductInfo        `json:"product,omitempty"`
	Vendor      *VendorInfo         `json:"vendor,omitempty"`
	Variant     *ProductVariantInfo `json:"variant,omitempty"`
	AddOns      []CartItemAddOn     `json:"add_ons,omitempty"`
}

// CartItemAddOn represents product add-ons in cart
type CartItemAddOn struct {
	ID              uuid.UUID `json:"id" db:"id"`
	CartItemID      uuid.UUID `json:"cart_item_id" db:"cart_item_id"`
	ProductAddOnID  uuid.UUID `json:"product_addon_id" db:"product_addon_id"`
	Quantity        int       `json:"quantity" db:"quantity"`
	UnitPrice       float64   `json:"unit_price" db:"unit_price"`
	TotalPrice      float64   `json:"total_price" db:"total_price"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`

	// Related data
	AddOn *ProductAddOnInfo `json:"add_on,omitempty"`
}

// CartService represents a service in the cart
type CartService struct {
	ID                uuid.UUID  `json:"id" db:"id"`
	CartID            uuid.UUID  `json:"cart_id" db:"cart_id"`
	ServiceID         uuid.UUID  `json:"service_id" db:"service_id"`
	VendorID          uuid.UUID  `json:"vendor_id" db:"vendor_id"`
	ServiceVariantID  *uuid.UUID `json:"service_variant_id,omitempty" db:"service_variant_id"`
	PreferredDate     *time.Time `json:"preferred_date,omitempty" db:"preferred_date"`
	PreferredTimeSlot *string    `json:"preferred_time_slot,omitempty" db:"preferred_time_slot"`
	Duration          *int       `json:"duration,omitempty" db:"duration"`
	UnitPrice         float64    `json:"unit_price" db:"unit_price"`
	TotalPrice        float64    `json:"total_price" db:"total_price"`
	Notes             *string    `json:"notes,omitempty" db:"notes"`
	CreatedAt         time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at" db:"updated_at"`

	// Related data
	Service     *ServiceInfo           `json:"service,omitempty"`
	Vendor      *VendorInfo            `json:"vendor,omitempty"`
	Variant     *ServiceVariantInfo    `json:"variant,omitempty"`
	AddOns      []CartServiceAddOn     `json:"add_ons,omitempty"`
}

// CartServiceAddOn represents service add-ons in cart
type CartServiceAddOn struct {
	ID               uuid.UUID `json:"id" db:"id"`
	CartServiceID    uuid.UUID `json:"cart_service_id" db:"cart_service_id"`
	ServiceAddOnID   uuid.UUID `json:"service_addon_id" db:"service_addon_id"`
	Quantity         int       `json:"quantity" db:"quantity"`
	UnitPrice        float64   `json:"unit_price" db:"unit_price"`
	TotalPrice       float64   `json:"total_price" db:"total_price"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`

	// Related data
	AddOn *ServiceAddOnInfo `json:"add_on,omitempty"`
}

// CartSummary represents cart totals and calculations
type CartSummary struct {
	ItemCount       int                `json:"item_count"`
	ServiceCount    int                `json:"service_count"`
	Subtotal        float64            `json:"subtotal"`
	TaxAmount       float64            `json:"tax_amount"`
	FeeAmount       float64            `json:"fee_amount"`
	ShippingAmount  float64            `json:"shipping_amount"`
	DiscountAmount  float64            `json:"discount_amount"`
	Total           float64            `json:"total"`
	AppliedCoupons  []AppliedCoupon    `json:"applied_coupons,omitempty"`
	TaxBreakdown    []TaxBreakdown     `json:"tax_breakdown,omitempty"`
	FeeBreakdown    []FeeBreakdown     `json:"fee_breakdown,omitempty"`
}

// Related info structs
type ProductInfo struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	SKU         string    `json:"sku"`
	Images      []string  `json:"images,omitempty"`
}

type ServiceInfo struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	PriceType   string    `json:"price_type"`
}

type VendorInfo struct {
	ID        uuid.UUID `json:"id"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Email     string    `json:"email"`
}

type ProductVariantInfo struct {
	ID              uuid.UUID              `json:"id"`
	Name            string                 `json:"name"`
	PriceAdjustment float64                `json:"price_adjustment"`
	Attributes      map[string]interface{} `json:"attributes,omitempty"`
}

type ServiceVariantInfo struct {
	ID        uuid.UUID `json:"id"`
	Name      string    `json:"name"`
	Price     float64   `json:"price"`
	Duration  int       `json:"duration"`
}

type ProductAddOnInfo struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Price       float64   `json:"price"`
}

type ServiceAddOnInfo struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Price       float64   `json:"price"`
}

type AppliedCoupon struct {
	Code           string  `json:"code"`
	Name           string  `json:"name"`
	DiscountType   string  `json:"discount_type"`
	DiscountAmount float64 `json:"discount_amount"`
}

type TaxBreakdown struct {
	Name   string  `json:"name"`
	Rate   float64 `json:"rate"`
	Amount float64 `json:"amount"`
}

type FeeBreakdown struct {
	Name   string  `json:"name"`
	Type   string  `json:"type"`
	Amount float64 `json:"amount"`
}

// Request DTOs
type AddProductToCartRequest struct {
	ProductID        uuid.UUID  `json:"product_id" validate:"required"`
	VendorID         uuid.UUID  `json:"vendor_id" validate:"required"`
	ProductVariantID *uuid.UUID `json:"product_variant_id,omitempty"`
	Quantity         int        `json:"quantity" validate:"required,min=1"`
	AddOns           []AddOnRequest `json:"add_ons,omitempty"`
}

type AddServiceToCartRequest struct {
	ServiceID         uuid.UUID  `json:"service_id" validate:"required"`
	VendorID          uuid.UUID  `json:"vendor_id" validate:"required"`
	ServiceVariantID  *uuid.UUID `json:"service_variant_id,omitempty"`
	PreferredDate     *time.Time `json:"preferred_date,omitempty"`
	PreferredTimeSlot *string    `json:"preferred_time_slot,omitempty"`
	Duration          *int       `json:"duration,omitempty"`
	Notes             *string    `json:"notes,omitempty"`
	AddOns            []AddOnRequest `json:"add_ons,omitempty"`
}

type AddOnRequest struct {
	AddOnID  uuid.UUID `json:"addon_id" validate:"required"`
	Quantity int       `json:"quantity" validate:"required,min=1"`
}

type UpdateCartItemRequest struct {
	Quantity int `json:"quantity" validate:"required,min=1"`
}

type ApplyCouponRequest struct {
	CouponCode string `json:"coupon_code" validate:"required"`
}

type CalculateShippingRequest struct {
	AddressID      *uuid.UUID `json:"address_id,omitempty"`
	ShippingMethod *uuid.UUID `json:"shipping_method_id,omitempty"`
	Address        *AddressInfo `json:"address,omitempty"`
}

type AddressInfo struct {
	AddressLine1 string `json:"address_line1" validate:"required"`
	AddressLine2 string `json:"address_line2,omitempty"`
	City         string `json:"city" validate:"required"`
	State        string `json:"state" validate:"required"`
	PostalCode   string `json:"postal_code" validate:"required"`
	Country      string `json:"country" validate:"required"`
}

// Response DTOs
type CartResponse struct {
	Success bool   `json:"success"`
	Data    *Cart  `json:"data,omitempty"`
	Message string `json:"message,omitempty"`
	Errors  []string `json:"errors,omitempty"`
}

type CartSummaryResponse struct {
	Success bool         `json:"success"`
	Data    *CartSummary `json:"data,omitempty"`
	Message string       `json:"message,omitempty"`
	Errors  []string     `json:"errors,omitempty"`
}

// Constants
const (
	CartItemTypeProduct = "product"
	CartItemTypeService = "service"
)

// Cart validation constants
const (
	MaxCartItems    = 100
	MaxCartServices = 50
	MaxItemQuantity = 999
)
