package repository

import (
	"database/sql"
	"fmt"
	"shark/cart-service/internal/models"
	"time"

	"github.com/google/uuid"
)

type CartRepository struct {
	db *sql.DB
}

func NewCartRepository(db *sql.DB) *CartRepository {
	return &CartRepository{db: db}
}

// Cart operations
func (r *CartRepository) GetOrCreateCart(userID uuid.UUID) (*models.Cart, error) {
	cart := &models.Cart{}
	
	// Try to get existing cart
	query := `SELECT id, user_id, session_id, created_at, updated_at FROM cart WHERE user_id = $1`
	err := r.db.QueryRow(query, userID).Scan(
		&cart.ID, &cart.UserID, &cart.SessionID, &cart.CreatedAt, &cart.UpdatedAt)
	
	if err == sql.ErrNoRows {
		// Create new cart
		cart.ID = uuid.New()
		cart.UserID = userID
		
		insertQuery := `
			INSERT INTO cart (id, user_id, created_at, updated_at) 
			VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			RETURNING created_at, updated_at`
		
		err = r.db.QueryRow(insertQuery, cart.ID, cart.UserID).Scan(&cart.CreatedAt, &cart.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to create cart: %w", err)
		}
	} else if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}
	
	return cart, nil
}

func (r *CartRepository) GetCartWithItems(userID uuid.UUID) (*models.Cart, error) {
	cart, err := r.GetOrCreateCart(userID)
	if err != nil {
		return nil, err
	}
	
	// Load cart items
	items, err := r.GetCartItems(cart.ID)
	if err != nil {
		return nil, err
	}
	cart.Items = items
	
	// Load cart services
	services, err := r.GetCartServices(cart.ID)
	if err != nil {
		return nil, err
	}
	cart.Services = services
	
	return cart, nil
}

func (r *CartRepository) ClearCart(userID uuid.UUID) error {
	cart, err := r.GetOrCreateCart(userID)
	if err != nil {
		return err
	}
	
	tx, err := r.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()
	
	// Delete cart item add-ons
	_, err = tx.Exec(`DELETE FROM cart_item_addons WHERE cart_item_id IN 
		(SELECT id FROM cart_items WHERE cart_id = $1)`, cart.ID)
	if err != nil {
		return err
	}
	
	// Delete cart service add-ons
	_, err = tx.Exec(`DELETE FROM cart_service_addons WHERE cart_service_id IN 
		(SELECT id FROM cart_services WHERE cart_id = $1)`, cart.ID)
	if err != nil {
		return err
	}
	
	// Delete cart items
	_, err = tx.Exec(`DELETE FROM cart_items WHERE cart_id = $1`, cart.ID)
	if err != nil {
		return err
	}
	
	// Delete cart services
	_, err = tx.Exec(`DELETE FROM cart_services WHERE cart_id = $1`, cart.ID)
	if err != nil {
		return err
	}
	
	// Update cart timestamp
	_, err = tx.Exec(`UPDATE cart SET updated_at = CURRENT_TIMESTAMP WHERE id = $1`, cart.ID)
	if err != nil {
		return err
	}
	
	return tx.Commit()
}

// Cart Items operations
func (r *CartRepository) AddProductToCart(cartID uuid.UUID, req *models.AddProductToCartRequest) (*models.CartItem, error) {
	tx, err := r.db.Begin()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()
	
	// Get vendor product pricing
	var unitPrice float64
	priceQuery := `
		SELECT COALESCE(vp.vendor_price, p.price) as unit_price
		FROM products p
		LEFT JOIN vendor_products vp ON p.id = vp.product_id AND vp.vendor_id = $2
		WHERE p.id = $1 AND p.is_active = true`
	
	err = tx.QueryRow(priceQuery, req.ProductID, req.VendorID).Scan(&unitPrice)
	if err != nil {
		return nil, fmt.Errorf("failed to get product price: %w", err)
	}
	
	// Check if item already exists in cart
	var existingItemID uuid.UUID
	existingQuery := `
		SELECT id FROM cart_items 
		WHERE cart_id = $1 AND product_id = $2 AND vendor_id = $3 
		AND COALESCE(product_variant_id, '00000000-0000-0000-0000-000000000000') = 
		    COALESCE($4, '00000000-0000-0000-0000-000000000000')`
	
	err = tx.QueryRow(existingQuery, cartID, req.ProductID, req.VendorID, req.ProductVariantID).Scan(&existingItemID)
	
	if err == sql.ErrNoRows {
		// Create new cart item
		item := &models.CartItem{
			ID:               uuid.New(),
			CartID:           cartID,
			ProductID:        req.ProductID,
			VendorID:         req.VendorID,
			ProductVariantID: req.ProductVariantID,
			Quantity:         req.Quantity,
			UnitPrice:        unitPrice,
			TotalPrice:       unitPrice * float64(req.Quantity),
		}
		
		insertQuery := `
			INSERT INTO cart_items (id, cart_id, product_id, vendor_id, product_variant_id, 
			                       quantity, unit_price, total_price, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			RETURNING created_at, updated_at`
		
		err = tx.QueryRow(insertQuery, item.ID, item.CartID, item.ProductID, item.VendorID,
			item.ProductVariantID, item.Quantity, item.UnitPrice, item.TotalPrice).Scan(
			&item.CreatedAt, &item.UpdatedAt)
		
		if err != nil {
			return nil, fmt.Errorf("failed to insert cart item: %w", err)
		}
		
		// Add add-ons if provided
		for _, addOnReq := range req.AddOns {
			err = r.addCartItemAddOn(tx, item.ID, addOnReq)
			if err != nil {
				return nil, err
			}
		}
		
		err = tx.Commit()
		if err != nil {
			return nil, err
		}
		
		return item, nil
	} else if err != nil {
		return nil, fmt.Errorf("failed to check existing cart item: %w", err)
	} else {
		// Update existing item quantity
		newQuantity := req.Quantity
		newTotalPrice := unitPrice * float64(newQuantity)
		
		updateQuery := `
			UPDATE cart_items 
			SET quantity = $2, total_price = $3, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1
			RETURNING quantity, unit_price, total_price, created_at, updated_at`
		
		item := &models.CartItem{
			ID:               existingItemID,
			CartID:           cartID,
			ProductID:        req.ProductID,
			VendorID:         req.VendorID,
			ProductVariantID: req.ProductVariantID,
		}
		
		err = tx.QueryRow(updateQuery, existingItemID, newQuantity, newTotalPrice).Scan(
			&item.Quantity, &item.UnitPrice, &item.TotalPrice, &item.CreatedAt, &item.UpdatedAt)
		
		if err != nil {
			return nil, fmt.Errorf("failed to update cart item: %w", err)
		}
		
		err = tx.Commit()
		if err != nil {
			return nil, err
		}
		
		return item, nil
	}
}

func (r *CartRepository) AddServiceToCart(cartID uuid.UUID, req *models.AddServiceToCartRequest) (*models.CartService, error) {
	tx, err := r.db.Begin()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()
	
	// Get vendor service pricing
	var unitPrice float64
	priceQuery := `
		SELECT COALESCE(vs.vendor_price, s.price) as unit_price
		FROM services s
		LEFT JOIN vendor_services vs ON s.id = vs.service_id AND vs.vendor_id = $2
		WHERE s.id = $1 AND s.is_active = true`
	
	err = tx.QueryRow(priceQuery, req.ServiceID, req.VendorID).Scan(&unitPrice)
	if err != nil {
		return nil, fmt.Errorf("failed to get service price: %w", err)
	}
	
	// Create new cart service
	service := &models.CartService{
		ID:                uuid.New(),
		CartID:            cartID,
		ServiceID:         req.ServiceID,
		VendorID:          req.VendorID,
		ServiceVariantID:  req.ServiceVariantID,
		PreferredDate:     req.PreferredDate,
		PreferredTimeSlot: req.PreferredTimeSlot,
		Duration:          req.Duration,
		UnitPrice:         unitPrice,
		TotalPrice:        unitPrice,
		Notes:             req.Notes,
	}
	
	insertQuery := `
		INSERT INTO cart_services (id, cart_id, service_id, vendor_id, service_variant_id,
		                          preferred_date, preferred_time_slot, duration, unit_price, 
		                          total_price, notes, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		RETURNING created_at, updated_at`
	
	err = tx.QueryRow(insertQuery, service.ID, service.CartID, service.ServiceID, service.VendorID,
		service.ServiceVariantID, service.PreferredDate, service.PreferredTimeSlot, service.Duration,
		service.UnitPrice, service.TotalPrice, service.Notes).Scan(&service.CreatedAt, &service.UpdatedAt)
	
	if err != nil {
		return nil, fmt.Errorf("failed to insert cart service: %w", err)
	}
	
	// Add add-ons if provided
	for _, addOnReq := range req.AddOns {
		err = r.addCartServiceAddOn(tx, service.ID, addOnReq)
		if err != nil {
			return nil, err
		}
	}
	
	err = tx.Commit()
	if err != nil {
		return nil, err
	}
	
	return service, nil
}

func (r *CartRepository) UpdateCartItem(itemID uuid.UUID, quantity int) error {
	query := `
		UPDATE cart_items 
		SET quantity = $2, total_price = unit_price * $2, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1`
	
	_, err := r.db.Exec(query, itemID, quantity)
	return err
}

func (r *CartRepository) RemoveCartItem(itemID uuid.UUID) error {
	tx, err := r.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()
	
	// Delete cart item add-ons first
	_, err = tx.Exec(`DELETE FROM cart_item_addons WHERE cart_item_id = $1`, itemID)
	if err != nil {
		return err
	}
	
	// Delete cart item
	_, err = tx.Exec(`DELETE FROM cart_items WHERE id = $1`, itemID)
	if err != nil {
		return err
	}
	
	return tx.Commit()
}

func (r *CartRepository) RemoveCartService(serviceID uuid.UUID) error {
	tx, err := r.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()
	
	// Delete cart service add-ons first
	_, err = tx.Exec(`DELETE FROM cart_service_addons WHERE cart_service_id = $1`, serviceID)
	if err != nil {
		return err
	}
	
	// Delete cart service
	_, err = tx.Exec(`DELETE FROM cart_services WHERE id = $1`, serviceID)
	if err != nil {
		return err
	}
	
	return tx.Commit()
}

func (r *CartRepository) GetCartItems(cartID uuid.UUID) ([]models.CartItem, error) {
	query := `
		SELECT ci.id, ci.cart_id, ci.product_id, ci.vendor_id, ci.product_variant_id,
		       ci.quantity, ci.unit_price, ci.total_price, ci.created_at, ci.updated_at,
		       p.name, p.description, p.sku, p.images,
		       u.first_name, u.last_name, u.email
		FROM cart_items ci
		JOIN products p ON ci.product_id = p.id
		JOIN users u ON ci.vendor_id = u.id
		WHERE ci.cart_id = $1
		ORDER BY ci.created_at ASC`
	
	rows, err := r.db.Query(query, cartID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var items []models.CartItem
	for rows.Next() {
		var item models.CartItem
		var product models.ProductInfo
		var vendor models.VendorInfo
		var images sql.NullString
		
		err := rows.Scan(
			&item.ID, &item.CartID, &item.ProductID, &item.VendorID, &item.ProductVariantID,
			&item.Quantity, &item.UnitPrice, &item.TotalPrice, &item.CreatedAt, &item.UpdatedAt,
			&product.Name, &product.Description, &product.SKU, &images,
			&vendor.FirstName, &vendor.LastName, &vendor.Email)
		
		if err != nil {
			return nil, err
		}
		
		product.ID = item.ProductID
		vendor.ID = item.VendorID
		
		if images.Valid {
			// Parse images JSON array - simplified for now
			product.Images = []string{images.String}
		}
		
		item.Product = &product
		item.Vendor = &vendor
		
		// Load add-ons for this item
		addOns, err := r.getCartItemAddOns(item.ID)
		if err != nil {
			return nil, err
		}
		item.AddOns = addOns
		
		items = append(items, item)
	}
	
	return items, nil
}

func (r *CartRepository) GetCartServices(cartID uuid.UUID) ([]models.CartService, error) {
	query := `
		SELECT cs.id, cs.cart_id, cs.service_id, cs.vendor_id, cs.service_variant_id,
		       cs.preferred_date, cs.preferred_time_slot, cs.duration, cs.unit_price, 
		       cs.total_price, cs.notes, cs.created_at, cs.updated_at,
		       s.name, s.description, s.price_type,
		       u.first_name, u.last_name, u.email
		FROM cart_services cs
		JOIN services s ON cs.service_id = s.id
		JOIN users u ON cs.vendor_id = u.id
		WHERE cs.cart_id = $1
		ORDER BY cs.created_at ASC`
	
	rows, err := r.db.Query(query, cartID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var services []models.CartService
	for rows.Next() {
		var service models.CartService
		var serviceInfo models.ServiceInfo
		var vendor models.VendorInfo
		
		err := rows.Scan(
			&service.ID, &service.CartID, &service.ServiceID, &service.VendorID, &service.ServiceVariantID,
			&service.PreferredDate, &service.PreferredTimeSlot, &service.Duration, &service.UnitPrice,
			&service.TotalPrice, &service.Notes, &service.CreatedAt, &service.UpdatedAt,
			&serviceInfo.Name, &serviceInfo.Description, &serviceInfo.PriceType,
			&vendor.FirstName, &vendor.LastName, &vendor.Email)
		
		if err != nil {
			return nil, err
		}
		
		serviceInfo.ID = service.ServiceID
		vendor.ID = service.VendorID
		
		service.Service = &serviceInfo
		service.Vendor = &vendor
		
		// Load add-ons for this service
		addOns, err := r.getCartServiceAddOns(service.ID)
		if err != nil {
			return nil, err
		}
		service.AddOns = addOns
		
		services = append(services, service)
	}
	
	return services, nil
}

// Helper methods
func (r *CartRepository) addCartItemAddOn(tx *sql.Tx, cartItemID uuid.UUID, addOnReq models.AddOnRequest) error {
	// Get add-on price
	var unitPrice float64
	err := tx.QueryRow(`SELECT price FROM product_addons WHERE id = $1`, addOnReq.AddOnID).Scan(&unitPrice)
	if err != nil {
		return fmt.Errorf("failed to get add-on price: %w", err)
	}
	
	totalPrice := unitPrice * float64(addOnReq.Quantity)
	
	_, err = tx.Exec(`
		INSERT INTO cart_item_addons (id, cart_item_id, product_addon_id, quantity, unit_price, total_price, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)`,
		uuid.New(), cartItemID, addOnReq.AddOnID, addOnReq.Quantity, unitPrice, totalPrice)
	
	return err
}

func (r *CartRepository) addCartServiceAddOn(tx *sql.Tx, cartServiceID uuid.UUID, addOnReq models.AddOnRequest) error {
	// Get add-on price
	var unitPrice float64
	err := tx.QueryRow(`SELECT price FROM service_addons WHERE id = $1`, addOnReq.AddOnID).Scan(&unitPrice)
	if err != nil {
		return fmt.Errorf("failed to get add-on price: %w", err)
	}
	
	totalPrice := unitPrice * float64(addOnReq.Quantity)
	
	_, err = tx.Exec(`
		INSERT INTO cart_service_addons (id, cart_service_id, service_addon_id, quantity, unit_price, total_price, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)`,
		uuid.New(), cartServiceID, addOnReq.AddOnID, addOnReq.Quantity, unitPrice, totalPrice)
	
	return err
}

func (r *CartRepository) getCartItemAddOns(cartItemID uuid.UUID) ([]models.CartItemAddOn, error) {
	query := `
		SELECT cia.id, cia.cart_item_id, cia.product_addon_id, cia.quantity, 
		       cia.unit_price, cia.total_price, cia.created_at,
		       pa.name, pa.description, pa.price
		FROM cart_item_addons cia
		JOIN product_addons pa ON cia.product_addon_id = pa.id
		WHERE cia.cart_item_id = $1`
	
	rows, err := r.db.Query(query, cartItemID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var addOns []models.CartItemAddOn
	for rows.Next() {
		var addOn models.CartItemAddOn
		var addOnInfo models.ProductAddOnInfo
		
		err := rows.Scan(
			&addOn.ID, &addOn.CartItemID, &addOn.ProductAddOnID, &addOn.Quantity,
			&addOn.UnitPrice, &addOn.TotalPrice, &addOn.CreatedAt,
			&addOnInfo.Name, &addOnInfo.Description, &addOnInfo.Price)
		
		if err != nil {
			return nil, err
		}
		
		addOnInfo.ID = addOn.ProductAddOnID
		addOn.AddOn = &addOnInfo
		addOns = append(addOns, addOn)
	}
	
	return addOns, nil
}

func (r *CartRepository) getCartServiceAddOns(cartServiceID uuid.UUID) ([]models.CartServiceAddOn, error) {
	query := `
		SELECT csa.id, csa.cart_service_id, csa.service_addon_id, csa.quantity, 
		       csa.unit_price, csa.total_price, csa.created_at,
		       sa.name, sa.description, sa.price
		FROM cart_service_addons csa
		JOIN service_addons sa ON csa.service_addon_id = sa.id
		WHERE csa.cart_service_id = $1`
	
	rows, err := r.db.Query(query, cartServiceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var addOns []models.CartServiceAddOn
	for rows.Next() {
		var addOn models.CartServiceAddOn
		var addOnInfo models.ServiceAddOnInfo
		
		err := rows.Scan(
			&addOn.ID, &addOn.CartServiceID, &addOn.ServiceAddOnID, &addOn.Quantity,
			&addOn.UnitPrice, &addOn.TotalPrice, &addOn.CreatedAt,
			&addOnInfo.Name, &addOnInfo.Description, &addOnInfo.Price)
		
		if err != nil {
			return nil, err
		}
		
		addOnInfo.ID = addOn.ServiceAddOnID
		addOn.AddOn = &addOnInfo
		addOns = append(addOns, addOn)
	}
	
	return addOns, nil
}
