package repository

import (
	"database/sql"
	"fmt"
	"shark/cart-service/internal/service"
	"time"

	"github.com/google/uuid"
)

type CouponRepository struct {
	db *sql.DB
}

func NewCouponRepository(db *sql.DB) *CouponRepository {
	return &CouponRepository{db: db}
}

func (r *CouponRepository) ValidateCoupon(code string, userID uuid.UUID) (*service.Coupon, error) {
	coupon := &service.Coupon{}
	
	query := `
		SELECT id, code, name, discount_type, discount_value, minimum_order_amount, 
		       maximum_discount_amount, usage_limit, usage_limit_per_user, used_count,
		       is_active, valid_from, valid_until
		FROM coupons 
		WHERE code = $1 AND is_active = true`
	
	var validFrom, validUntil sql.NullTime
	var usageLimit, usageLimitPerUser sql.NullInt64
	var usedCount int
	
	err := r.db.QueryRow(query, code).Scan(
		&coupon.ID, &coupon.Code, &coupon.Name, &coupon.DiscountType, &coupon.DiscountValue,
		&coupon.MinimumOrderAmount, &coupon.MaximumDiscountAmount, &usageLimit, &usageLimitPerUser,
		&usedCount, &validFrom, &validUntil)
	
	if err == sql.ErrNoRows {
		return nil, fmt.Errorf("coupon not found or inactive")
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}
	
	// Check if coupon is still valid (time-based)
	now := time.Now()
	if validFrom.Valid && now.Before(validFrom.Time) {
		return nil, fmt.Errorf("coupon is not yet valid")
	}
	if validUntil.Valid && now.After(validUntil.Time) {
		return nil, fmt.Errorf("coupon has expired")
	}
	
	// Check usage limits
	if usageLimit.Valid && usedCount >= int(usageLimit.Int64) {
		return nil, fmt.Errorf("coupon usage limit exceeded")
	}
	
	// Check per-user usage limit
	if usageLimitPerUser.Valid {
		userUsageCount, err := r.getUserCouponUsageCount(coupon.ID, userID)
		if err != nil {
			return nil, fmt.Errorf("failed to check user coupon usage: %w", err)
		}
		if userUsageCount >= int(usageLimitPerUser.Int64) {
			return nil, fmt.Errorf("user coupon usage limit exceeded")
		}
	}
	
	return coupon, nil
}

func (r *CouponRepository) CalculateDiscount(coupon *service.Coupon, subtotal float64) (float64, error) {
	// Check minimum order amount
	if subtotal < coupon.MinimumOrderAmount {
		return 0, fmt.Errorf("order amount (%.2f) is below minimum required (%.2f)", 
			subtotal, coupon.MinimumOrderAmount)
	}
	
	var discount float64
	
	switch coupon.DiscountType {
	case "percentage":
		discount = subtotal * (coupon.DiscountValue / 100)
		// Apply maximum discount limit if set
		if coupon.MaximumDiscountAmount != nil && discount > *coupon.MaximumDiscountAmount {
			discount = *coupon.MaximumDiscountAmount
		}
	case "fixed_amount":
		discount = coupon.DiscountValue
		// Don't allow discount to exceed subtotal
		if discount > subtotal {
			discount = subtotal
		}
	case "free_shipping":
		// Free shipping discount is handled separately in shipping calculation
		discount = 0
	default:
		return 0, fmt.Errorf("unsupported discount type: %s", coupon.DiscountType)
	}
	
	return discount, nil
}

func (r *CouponRepository) RecordCouponUsage(couponID, userID uuid.UUID, orderID *uuid.UUID, bookingID *uuid.UUID, discountAmount float64) error {
	tx, err := r.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()
	
	// Record usage
	_, err = tx.Exec(`
		INSERT INTO coupon_usage (id, coupon_id, user_id, order_id, booking_id, discount_amount, used_at)
		VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)`,
		uuid.New(), couponID, userID, orderID, bookingID, discountAmount)
	
	if err != nil {
		return fmt.Errorf("failed to record coupon usage: %w", err)
	}
	
	// Increment used count
	_, err = tx.Exec(`
		UPDATE coupons 
		SET used_count = used_count + 1, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1`, couponID)
	
	if err != nil {
		return fmt.Errorf("failed to update coupon usage count: %w", err)
	}
	
	return tx.Commit()
}

func (r *CouponRepository) getUserCouponUsageCount(couponID, userID uuid.UUID) (int, error) {
	var count int
	err := r.db.QueryRow(`
		SELECT COUNT(*) FROM coupon_usage 
		WHERE coupon_id = $1 AND user_id = $2`, couponID, userID).Scan(&count)
	
	return count, err
}

func (r *CouponRepository) GetActiveCoupons() ([]*service.Coupon, error) {
	query := `
		SELECT id, code, name, discount_type, discount_value, minimum_order_amount, 
		       maximum_discount_amount, usage_limit, usage_limit_per_user, used_count,
		       valid_from, valid_until
		FROM coupons 
		WHERE is_active = true 
		AND (valid_from IS NULL OR valid_from <= CURRENT_TIMESTAMP)
		AND (valid_until IS NULL OR valid_until >= CURRENT_TIMESTAMP)
		ORDER BY created_at DESC`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var coupons []*service.Coupon
	for rows.Next() {
		coupon := &service.Coupon{}
		var validFrom, validUntil sql.NullTime
		var usageLimit, usageLimitPerUser sql.NullInt64
		var usedCount int
		
		err := rows.Scan(
			&coupon.ID, &coupon.Code, &coupon.Name, &coupon.DiscountType, &coupon.DiscountValue,
			&coupon.MinimumOrderAmount, &coupon.MaximumDiscountAmount, &usageLimit, &usageLimitPerUser,
			&usedCount, &validFrom, &validUntil)
		
		if err != nil {
			return nil, err
		}
		
		coupons = append(coupons, coupon)
	}
	
	return coupons, nil
}

func (r *CouponRepository) GetCouponByCode(code string) (*service.Coupon, error) {
	coupon := &service.Coupon{}
	
	query := `
		SELECT id, code, name, discount_type, discount_value, minimum_order_amount, 
		       maximum_discount_amount
		FROM coupons 
		WHERE code = $1 AND is_active = true`
	
	err := r.db.QueryRow(query, code).Scan(
		&coupon.ID, &coupon.Code, &coupon.Name, &coupon.DiscountType, &coupon.DiscountValue,
		&coupon.MinimumOrderAmount, &coupon.MaximumDiscountAmount)
	
	if err == sql.ErrNoRows {
		return nil, fmt.Errorf("coupon not found")
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}
	
	return coupon, nil
}
