package repository

import (
	"database/sql"
	"shark/cart-service/internal/models"
)

type FeeRepository struct {
	db *sql.DB
}

func NewFeeRepository(db *sql.DB) *FeeRepository {
	return &FeeRepository{db: db}
}

func (r *FeeRepository) CalculateFees(subtotal float64, applicableTo string) ([]models.FeeBreakdown, float64, error) {
	query := `
		SELECT name, fee_type, calculation_type, amount, minimum_amount, maximum_amount
		FROM fees 
		WHERE is_active = true 
		AND (applicable_to = 'all' OR applicable_to = $1)
		ORDER BY name ASC`
	
	rows, err := r.db.Query(query, applicableTo)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()
	
	var breakdown []models.FeeBreakdown
	var totalFees float64
	
	for rows.Next() {
		var name, feeType, calculationType string
		var amount, minimumAmount float64
		var maximumAmount sql.NullFloat64
		
		err := rows.Scan(&name, &feeType, &calculationType, &amount, &minimumAmount, &maximumAmount)
		if err != nil {
			return nil, 0, err
		}
		
		var feeAmount float64
		
		switch calculationType {
		case "fixed":
			feeAmount = amount
		case "percentage":
			feeAmount = subtotal * (amount / 100)
			// Apply minimum and maximum limits
			if feeAmount < minimumAmount {
				feeAmount = minimumAmount
			}
			if maximumAmount.Valid && feeAmount > maximumAmount.Float64 {
				feeAmount = maximumAmount.Float64
			}
		case "tiered":
			// For tiered fees, we would need to query fee_tiers table
			feeAmount = r.calculateTieredFee(name, subtotal)
		}
		
		totalFees += feeAmount
		
		breakdown = append(breakdown, models.FeeBreakdown{
			Name:   name,
			Type:   feeType,
			Amount: feeAmount,
		})
	}
	
	return breakdown, totalFees, nil
}

func (r *FeeRepository) calculateTieredFee(feeName string, amount float64) float64 {
	// This would query the fee_tiers table to calculate tiered fees
	// For now, return a simple calculation
	return 0.0
}

func (r *FeeRepository) GetActiveFees() ([]models.FeeBreakdown, error) {
	query := `
		SELECT name, fee_type, calculation_type, amount, applicable_to
		FROM fees 
		WHERE is_active = true 
		ORDER BY name ASC`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var fees []models.FeeBreakdown
	for rows.Next() {
		var name, feeType, calculationType, applicableTo string
		var amount float64
		
		err := rows.Scan(&name, &feeType, &calculationType, &amount, &applicableTo)
		if err != nil {
			return nil, err
		}
		
		fees = append(fees, models.FeeBreakdown{
			Name:   name,
			Type:   feeType,
			Amount: amount,
		})
	}
	
	return fees, nil
}
