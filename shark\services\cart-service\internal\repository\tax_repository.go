package repository

import (
	"database/sql"
	"shark/cart-service/internal/models"
)

type TaxRepository struct {
	db *sql.DB
}

func NewTaxRepository(db *sql.DB) *TaxRepository {
	return &TaxRepository{db: db}
}

func (r *TaxRepository) CalculateTaxes(subtotal float64, state, country string) ([]models.TaxBreakdown, float64, error) {
	query := `
		SELECT name, tax_type, rate
		FROM tax_rules 
		WHERE is_active = true 
		AND (country IS NULL OR country = $1)
		AND (state IS NULL OR state = $2)
		ORDER BY priority ASC`
	
	rows, err := r.db.Query(query, country, state)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()
	
	var breakdown []models.TaxBreakdown
	var totalTax float64
	
	for rows.Next() {
		var name, taxType string
		var rate float64
		
		err := rows.Scan(&name, &taxType, &rate)
		if err != nil {
			return nil, 0, err
		}
		
		taxAmount := subtotal * rate
		totalTax += taxAmount
		
		breakdown = append(breakdown, models.TaxBreakdown{
			Name:   name,
			Rate:   rate,
			Amount: taxAmount,
		})
	}
	
	return breakdown, totalTax, nil
}

func (r *TaxRepository) GetTaxRulesByLocation(country, state, city, postalCode string) ([]models.TaxBreakdown, error) {
	query := `
		SELECT name, tax_type, rate, priority
		FROM tax_rules 
		WHERE is_active = true 
		AND (country IS NULL OR country = $1)
		AND (state IS NULL OR state = $2)
		AND (city IS NULL OR city = $3)
		AND (postal_code IS NULL OR postal_code = $4)
		ORDER BY priority ASC`
	
	rows, err := r.db.Query(query, country, state, city, postalCode)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var rules []models.TaxBreakdown
	for rows.Next() {
		var name, taxType string
		var rate float64
		var priority int
		
		err := rows.Scan(&name, &taxType, &rate, &priority)
		if err != nil {
			return nil, err
		}
		
		rules = append(rules, models.TaxBreakdown{
			Name: name,
			Rate: rate,
		})
	}
	
	return rules, nil
}
