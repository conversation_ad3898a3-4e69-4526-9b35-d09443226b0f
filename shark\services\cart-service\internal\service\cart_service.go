package service

import (
	"fmt"
	"shark/cart-service/internal/models"
	"shark/cart-service/internal/repository"

	"github.com/google/uuid"
)

type CartService struct {
	cartRepo   *repository.CartRepository
	couponRepo *repository.CouponRepository
	taxRepo    *repository.TaxRepository
	feeRepo    *repository.FeeRepository
}

func NewCartService(cartRepo *repository.CartRepository, couponRepo *repository.CouponRepository, 
	taxRepo *repository.TaxRepository, feeRepo *repository.FeeRepository) *CartService {
	return &CartService{
		cartRepo:   cartRepo,
		couponRepo: couponRepo,
		taxRepo:    taxRepo,
		feeRepo:    feeRepo,
	}
}

func (s *CartService) GetCart(userID uuid.UUID) (*models.Cart, error) {
	cart, err := s.cartRepo.GetCartWithItems(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}
	
	// Calculate cart summary
	summary, err := s.CalculateCartSummary(cart)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate cart summary: %w", err)
	}
	cart.Summary = summary
	
	return cart, nil
}

func (s *CartService) AddProductToCart(userID uuid.UUID, req *models.AddProductToCartRequest) (*models.CartItem, error) {
	// Validate request
	if err := s.validateAddProductRequest(req); err != nil {
		return nil, err
	}
	
	cart, err := s.cartRepo.GetOrCreateCart(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}
	
	// Check cart limits
	if err := s.checkCartLimits(cart.ID); err != nil {
		return nil, err
	}
	
	item, err := s.cartRepo.AddProductToCart(cart.ID, req)
	if err != nil {
		return nil, fmt.Errorf("failed to add product to cart: %w", err)
	}
	
	return item, nil
}

func (s *CartService) AddServiceToCart(userID uuid.UUID, req *models.AddServiceToCartRequest) (*models.CartService, error) {
	// Validate request
	if err := s.validateAddServiceRequest(req); err != nil {
		return nil, err
	}
	
	cart, err := s.cartRepo.GetOrCreateCart(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}
	
	// Check cart limits
	if err := s.checkCartLimits(cart.ID); err != nil {
		return nil, err
	}
	
	service, err := s.cartRepo.AddServiceToCart(cart.ID, req)
	if err != nil {
		return nil, fmt.Errorf("failed to add service to cart: %w", err)
	}
	
	return service, nil
}

func (s *CartService) UpdateCartItem(userID uuid.UUID, itemID uuid.UUID, req *models.UpdateCartItemRequest) error {
	// Validate that the item belongs to the user's cart
	cart, err := s.cartRepo.GetOrCreateCart(userID)
	if err != nil {
		return fmt.Errorf("failed to get cart: %w", err)
	}
	
	// Verify item ownership
	if !s.isItemInCart(cart.ID, itemID) {
		return fmt.Errorf("cart item not found or does not belong to user")
	}
	
	if req.Quantity <= 0 {
		return s.cartRepo.RemoveCartItem(itemID)
	}
	
	if req.Quantity > models.MaxItemQuantity {
		return fmt.Errorf("quantity exceeds maximum allowed (%d)", models.MaxItemQuantity)
	}
	
	return s.cartRepo.UpdateCartItem(itemID, req.Quantity)
}

func (s *CartService) RemoveCartItem(userID uuid.UUID, itemID uuid.UUID) error {
	// Validate that the item belongs to the user's cart
	cart, err := s.cartRepo.GetOrCreateCart(userID)
	if err != nil {
		return fmt.Errorf("failed to get cart: %w", err)
	}
	
	// Verify item ownership
	if !s.isItemInCart(cart.ID, itemID) {
		return fmt.Errorf("cart item not found or does not belong to user")
	}
	
	return s.cartRepo.RemoveCartItem(itemID)
}

func (s *CartService) RemoveCartService(userID uuid.UUID, serviceID uuid.UUID) error {
	// Validate that the service belongs to the user's cart
	cart, err := s.cartRepo.GetOrCreateCart(userID)
	if err != nil {
		return fmt.Errorf("failed to get cart: %w", err)
	}
	
	// Verify service ownership
	if !s.isServiceInCart(cart.ID, serviceID) {
		return fmt.Errorf("cart service not found or does not belong to user")
	}
	
	return s.cartRepo.RemoveCartService(serviceID)
}

func (s *CartService) ClearCart(userID uuid.UUID) error {
	return s.cartRepo.ClearCart(userID)
}

func (s *CartService) ApplyCoupon(userID uuid.UUID, couponCode string) (*models.CartSummary, error) {
	cart, err := s.cartRepo.GetCartWithItems(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}
	
	// Validate coupon
	coupon, err := s.couponRepo.ValidateCoupon(couponCode, userID)
	if err != nil {
		return nil, fmt.Errorf("invalid coupon: %w", err)
	}
	
	// Calculate cart summary with coupon
	summary, err := s.CalculateCartSummaryWithCoupon(cart, coupon)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate cart summary with coupon: %w", err)
	}
	
	return summary, nil
}

func (s *CartService) CalculateCartSummary(cart *models.Cart) (*models.CartSummary, error) {
	summary := &models.CartSummary{}
	
	// Calculate item counts and subtotal
	for _, item := range cart.Items {
		summary.ItemCount++
		summary.Subtotal += item.TotalPrice
		
		// Add add-on prices
		for _, addOn := range item.AddOns {
			summary.Subtotal += addOn.TotalPrice
		}
	}
	
	for _, service := range cart.Services {
		summary.ServiceCount++
		summary.Subtotal += service.TotalPrice
		
		// Add add-on prices
		for _, addOn := range service.AddOns {
			summary.Subtotal += addOn.TotalPrice
		}
	}
	
	// Calculate taxes (simplified - would need user address for real calculation)
	taxBreakdown, taxAmount, err := s.taxRepo.CalculateTaxes(summary.Subtotal, "CA", "US")
	if err != nil {
		return nil, fmt.Errorf("failed to calculate taxes: %w", err)
	}
	summary.TaxAmount = taxAmount
	summary.TaxBreakdown = taxBreakdown
	
	// Calculate fees
	feeBreakdown, feeAmount, err := s.feeRepo.CalculateFees(summary.Subtotal, "all")
	if err != nil {
		return nil, fmt.Errorf("failed to calculate fees: %w", err)
	}
	summary.FeeAmount = feeAmount
	summary.FeeBreakdown = feeBreakdown
	
	// Calculate total
	summary.Total = summary.Subtotal + summary.TaxAmount + summary.FeeAmount + summary.ShippingAmount - summary.DiscountAmount
	
	return summary, nil
}

func (s *CartService) CalculateCartSummaryWithCoupon(cart *models.Cart, coupon *models.Coupon) (*models.CartSummary, error) {
	summary, err := s.CalculateCartSummary(cart)
	if err != nil {
		return nil, err
	}
	
	// Apply coupon discount
	discount, err := s.couponRepo.CalculateDiscount(coupon, summary.Subtotal)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate discount: %w", err)
	}
	
	summary.DiscountAmount = discount
	summary.AppliedCoupons = []models.AppliedCoupon{
		{
			Code:           coupon.Code,
			Name:           coupon.Name,
			DiscountType:   coupon.DiscountType,
			DiscountAmount: discount,
		},
	}
	
	// Recalculate total
	summary.Total = summary.Subtotal + summary.TaxAmount + summary.FeeAmount + summary.ShippingAmount - summary.DiscountAmount
	
	// Ensure total is not negative
	if summary.Total < 0 {
		summary.Total = 0
	}
	
	return summary, nil
}

func (s *CartService) CalculateShipping(userID uuid.UUID, req *models.CalculateShippingRequest) (*models.CartSummary, error) {
	cart, err := s.cartRepo.GetCartWithItems(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}
	
	summary, err := s.CalculateCartSummary(cart)
	if err != nil {
		return nil, err
	}
	
	// Calculate shipping cost based on address and method
	// This would integrate with shipping service
	shippingCost := 5.99 // Simplified for now
	
	if req.ShippingMethod != nil {
		// Get specific shipping method cost
		// shippingCost = s.shippingRepo.GetShippingCost(*req.ShippingMethod, address)
	}
	
	summary.ShippingAmount = shippingCost
	summary.Total = summary.Subtotal + summary.TaxAmount + summary.FeeAmount + summary.ShippingAmount - summary.DiscountAmount
	
	return summary, nil
}

// Validation methods
func (s *CartService) validateAddProductRequest(req *models.AddProductToCartRequest) error {
	if req.Quantity <= 0 {
		return fmt.Errorf("quantity must be greater than 0")
	}
	
	if req.Quantity > models.MaxItemQuantity {
		return fmt.Errorf("quantity exceeds maximum allowed (%d)", models.MaxItemQuantity)
	}
	
	// Additional validations could include:
	// - Product exists and is active
	// - Vendor exists and is active
	// - Product variant exists (if specified)
	// - Add-ons exist and belong to the product
	
	return nil
}

func (s *CartService) validateAddServiceRequest(req *models.AddServiceToCartRequest) error {
	// Validate service booking request
	// - Service exists and is active
	// - Vendor exists and is active
	// - Service variant exists (if specified)
	// - Preferred date is in the future
	// - Time slot is available
	// - Add-ons exist and belong to the service
	
	return nil
}

func (s *CartService) checkCartLimits(cartID uuid.UUID) error {
	// Check if cart has reached maximum items/services
	// This would query the database to count current items
	
	return nil
}

func (s *CartService) isItemInCart(cartID uuid.UUID, itemID uuid.UUID) bool {
	// Verify that the item belongs to the specified cart
	// This would query the database
	
	return true // Simplified for now
}

func (s *CartService) isServiceInCart(cartID uuid.UUID, serviceID uuid.UUID) bool {
	// Verify that the service belongs to the specified cart
	// This would query the database
	
	return true // Simplified for now
}

// Coupon model for service layer
type Coupon struct {
	ID                    uuid.UUID `json:"id"`
	Code                  string    `json:"code"`
	Name                  string    `json:"name"`
	DiscountType          string    `json:"discount_type"`
	DiscountValue         float64   `json:"discount_value"`
	MinimumOrderAmount    float64   `json:"minimum_order_amount"`
	MaximumDiscountAmount *float64  `json:"maximum_discount_amount,omitempty"`
}
