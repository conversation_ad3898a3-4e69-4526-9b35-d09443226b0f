package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"shark/coupon-service/internal/handlers"
	"shark/coupon-service/internal/repository"
	"shark/coupon-service/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
	_ "github.com/lib/pq"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Connect to database
	db, err := connectToDatabase()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize repositories
	couponRepo := repository.NewCouponRepository(db)

	// Initialize services
	couponService := service.NewCouponService(couponRepo)

	// Initialize handlers
	couponHandler := handlers.NewCouponHandler(couponService)

	// Setup router
	router := setupRouter(couponHandler)

	// Start server
	port := getEnv("PORT", "8081")
	log.Printf("Coupon Service starting on port %s", port)
	log.Fatal(router.Run(":" + port))
}

func setupRouter(couponHandler *handlers.CouponHandler) *gin.Engine {
	// Set Gin mode
	if getEnv("GIN_MODE", "debug") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// CORS middleware
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})
	router.Use(func(ctx *gin.Context) {
		c.HandlerFunc(ctx.Writer, ctx.Request)
		ctx.Next()
	})

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "coupon-service",
		})
	})

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Coupon routes
		coupons := v1.Group("/coupons")
		{
			// CRUD operations
			coupons.GET("", couponHandler.ListCoupons)
			coupons.POST("", couponHandler.CreateCoupon)
			coupons.GET("/:id", couponHandler.GetCoupon)
			coupons.PUT("/:id", couponHandler.UpdateCoupon)
			coupons.DELETE("/:id", couponHandler.DeleteCoupon)

			// Get by code
			coupons.GET("/code/:code", couponHandler.GetCouponByCode)

			// Validation and application
			coupons.POST("/validate", couponHandler.ValidateCoupon)
			coupons.POST("/apply", couponHandler.ApplyCoupon)

			// Analytics and stats
			coupons.GET("/stats", couponHandler.GetCouponStats)

			// Bulk operations
			coupons.POST("/bulk", couponHandler.BulkCreateCoupons)

			// Utility endpoints
			coupons.GET("/check-code", couponHandler.CheckCodeAvailability)
		}
	}

	return router
}

func connectToDatabase() (*sql.DB, error) {
	host := getEnv("DB_HOST", "localhost")
	port := getEnv("DB_PORT", "5433")
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "")
	dbname := getEnv("DB_NAME", "dodo")
	sslmode := getEnv("DB_SSL_MODE", "disable")

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		host, port, user, password, dbname, sslmode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	log.Println("✅ Connected to database successfully")
	return db, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
