package handlers

import (
	"fmt"
	"net/http"
	"shark/coupon-service/internal/models"
	"shark/coupon-service/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type CouponHandler struct {
	couponService *service.CouponService
}

func NewCouponHandler(couponService *service.CouponService) *CouponHandler {
	return &CouponHandler{
		couponService: couponService,
	}
}

// GET /api/v1/coupons
func (h *CouponHandler) ListCoupons(c *gin.Context) {
	var query models.CouponSearchQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, models.CouponsResponse{
			Success: false,
			Message: "Invalid query parameters",
			Errors:  []string{err.Error()},
		})
		return
	}

	coupons, total, err := h.couponService.ListCoupons(&query)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, models.CouponsResponse{
			Success: false,
			Message: "Failed to list coupons",
			Errors:  []string{err.Error()},
		})
		return
	}

	// Calculate pagination info
	totalPages := (total + query.Limit - 1) / query.Limit
	hasNext := query.Page < totalPages
	hasPrev := query.Page > 1

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    coupons,
		"pagination": gin.H{
			"page":        query.Page,
			"limit":       query.Limit,
			"total":       total,
			"total_pages": totalPages,
			"has_next":    hasNext,
			"has_prev":    hasPrev,
		},
		"message": "Coupons retrieved successfully",
	})
}

// POST /api/v1/coupons
func (h *CouponHandler) CreateCoupon(c *gin.Context) {
	createdBy, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.CouponResponse{
			Success: false,
			Message: "Unauthorized",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.CreateCouponRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	coupon, err := h.couponService.CreateCoupon(&req, createdBy)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Failed to create coupon",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusCreated, models.CouponResponse{
		Success: true,
		Data:    coupon,
		Message: "Coupon created successfully",
	})
}

// GET /api/v1/coupons/:id
func (h *CouponHandler) GetCoupon(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Invalid coupon ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	coupon, err := h.couponService.GetCoupon(id)
	if err != nil {
		c.JSON(http.StatusNotFound, models.CouponResponse{
			Success: false,
			Message: "Coupon not found",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.CouponResponse{
		Success: true,
		Data:    coupon,
		Message: "Coupon retrieved successfully",
	})
}

// GET /api/v1/coupons/code/:code
func (h *CouponHandler) GetCouponByCode(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Coupon code is required",
		})
		return
	}

	coupon, err := h.couponService.GetCouponByCode(code)
	if err != nil {
		c.JSON(http.StatusNotFound, models.CouponResponse{
			Success: false,
			Message: "Coupon not found",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.CouponResponse{
		Success: true,
		Data:    coupon,
		Message: "Coupon retrieved successfully",
	})
}

// PUT /api/v1/coupons/:id
func (h *CouponHandler) UpdateCoupon(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Invalid coupon ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.UpdateCouponRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	coupon, err := h.couponService.UpdateCoupon(id, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Failed to update coupon",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.CouponResponse{
		Success: true,
		Data:    coupon,
		Message: "Coupon updated successfully",
	})
}

// DELETE /api/v1/coupons/:id
func (h *CouponHandler) DeleteCoupon(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Invalid coupon ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	err = h.couponService.DeleteCoupon(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.CouponResponse{
			Success: false,
			Message: "Failed to delete coupon",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Coupon deleted successfully",
	})
}

// POST /api/v1/coupons/validate
func (h *CouponHandler) ValidateCoupon(c *gin.Context) {
	var req models.ValidateCouponRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ValidateCouponResponse{
			Success: false,
			Valid:   false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	coupon, discount, err := h.couponService.ValidateCoupon(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidateCouponResponse{
			Success:        false,
			Valid:          false,
			DiscountAmount: 0,
			Message:        "Coupon validation failed",
			Errors:         []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.ValidateCouponResponse{
		Success:        true,
		Valid:          true,
		DiscountAmount: discount,
		Message:        fmt.Sprintf("Coupon '%s' is valid. Discount: $%.2f", coupon.Code, discount),
	})
}

// POST /api/v1/coupons/apply
func (h *CouponHandler) ApplyCoupon(c *gin.Context) {
	var req models.ApplyCouponRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ValidateCouponResponse{
			Success: false,
			Valid:   false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	coupon, discount, err := h.couponService.ApplyCoupon(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidateCouponResponse{
			Success:        false,
			Valid:          false,
			DiscountAmount: 0,
			Message:        "Failed to apply coupon",
			Errors:         []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"coupon":          coupon,
			"discount_amount": discount,
			"applied_at":      "now", // Would be actual timestamp
		},
		"message": fmt.Sprintf("Coupon '%s' applied successfully. Discount: $%.2f", coupon.Code, discount),
	})
}

// GET /api/v1/coupons/stats
func (h *CouponHandler) GetCouponStats(c *gin.Context) {
	stats, err := h.couponService.GetCouponStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.CouponStatsResponse{
			Success: false,
			Message: "Failed to get coupon statistics",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.CouponStatsResponse{
		Success: true,
		Data:    stats,
		Message: "Coupon statistics retrieved successfully",
	})
}

// POST /api/v1/coupons/bulk
func (h *CouponHandler) BulkCreateCoupons(c *gin.Context) {
	createdBy, err := h.getUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Unauthorized",
			"errors":  []string{err.Error()},
		})
		return
	}

	var requests []*models.CreateCouponRequest
	if err := c.ShouldBindJSON(&requests); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request",
			"errors":  []string{err.Error()},
		})
		return
	}

	if len(requests) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Cannot create more than 100 coupons at once",
		})
		return
	}

	coupons, errors := h.couponService.BulkCreateCoupons(requests, createdBy)

	// Count successful and failed operations
	successCount := 0
	failedCount := 0
	for _, err := range errors {
		if err == nil {
			successCount++
		} else {
			failedCount++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"coupons":       coupons,
			"errors":        errors,
			"success_count": successCount,
			"failed_count":  failedCount,
		},
		"message": fmt.Sprintf("Bulk operation completed. %d successful, %d failed", successCount, failedCount),
	})
}

// POST /api/v1/coupons/check-code
func (h *CouponHandler) CheckCodeAvailability(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Coupon code is required",
		})
		return
	}

	available, err := h.couponService.CheckCodeAvailability(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to check code availability",
			"errors":  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"available": available,
		"message":   fmt.Sprintf("Code '%s' is %s", code, map[bool]string{true: "available", false: "taken"}[available]),
	})
}

// Helper methods
func (h *CouponHandler) getUserID(c *gin.Context) (uuid.UUID, error) {
	// Extract user ID from JWT token or session
	// This is a simplified implementation
	userIDStr := c.GetHeader("X-User-ID")
	if userIDStr == "" {
		// Try to get from query parameter for testing
		userIDStr = c.Query("user_id")
	}

	if userIDStr == "" {
		return uuid.Nil, fmt.Errorf("user ID not found")
	}

	return uuid.Parse(userIDStr)
}

func (h *CouponHandler) validatePagination(c *gin.Context) (int, int, error) {
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	return page, limit, nil
}
