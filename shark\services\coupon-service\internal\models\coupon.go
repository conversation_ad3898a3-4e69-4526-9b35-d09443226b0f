package models

import (
	"time"

	"github.com/google/uuid"
)

// Coupon represents a discount coupon
type Coupon struct {
	ID                    uuid.UUID  `json:"id" db:"id"`
	Code                  string     `json:"code" db:"code"`
	Name                  string     `json:"name" db:"name"`
	Description           string     `json:"description" db:"description"`
	DiscountType          string     `json:"discount_type" db:"discount_type"`
	DiscountValue         float64    `json:"discount_value" db:"discount_value"`
	MinimumOrderAmount    float64    `json:"minimum_order_amount" db:"minimum_order_amount"`
	MaximumDiscountAmount *float64   `json:"maximum_discount_amount,omitempty" db:"maximum_discount_amount"`
	UsageLimit            *int       `json:"usage_limit,omitempty" db:"usage_limit"`
	UsageLimitPerUser     int        `json:"usage_limit_per_user" db:"usage_limit_per_user"`
	UsedCount             int        `json:"used_count" db:"used_count"`
	IsActive              bool       `json:"is_active" db:"is_active"`
	ValidFrom             time.Time  `json:"valid_from" db:"valid_from"`
	ValidUntil            *time.Time `json:"valid_until,omitempty" db:"valid_until"`
	ApplicableTo          string     `json:"applicable_to" db:"applicable_to"`
	CreatedBy             uuid.UUID  `json:"created_by" db:"created_by"`
	CreatedAt             time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt             time.Time  `json:"updated_at" db:"updated_at"`

	// Related data
	Categories    []CouponCategory `json:"categories,omitempty"`
	UsageHistory  []CouponUsage    `json:"usage_history,omitempty"`
	RemainingUses *int             `json:"remaining_uses,omitempty"`
}

// CouponUsage represents coupon usage tracking
type CouponUsage struct {
	ID             uuid.UUID  `json:"id" db:"id"`
	CouponID       uuid.UUID  `json:"coupon_id" db:"coupon_id"`
	UserID         uuid.UUID  `json:"user_id" db:"user_id"`
	OrderID        *uuid.UUID `json:"order_id,omitempty" db:"order_id"`
	BookingID      *uuid.UUID `json:"booking_id,omitempty" db:"booking_id"`
	DiscountAmount float64    `json:"discount_amount" db:"discount_amount"`
	UsedAt         time.Time  `json:"used_at" db:"used_at"`

	// Related data
	User    *UserInfo    `json:"user,omitempty"`
	Order   *OrderInfo   `json:"order,omitempty"`
	Booking *BookingInfo `json:"booking,omitempty"`
}

// CouponCategory represents category-specific coupons
type CouponCategory struct {
	ID                  uuid.UUID  `json:"id" db:"id"`
	CouponID            uuid.UUID  `json:"coupon_id" db:"coupon_id"`
	ProductCategoryID   *uuid.UUID `json:"product_category_id,omitempty" db:"product_category_id"`
	ServiceCategoryID   *uuid.UUID `json:"service_category_id,omitempty" db:"service_category_id"`

	// Related data
	ProductCategory *CategoryInfo `json:"product_category,omitempty"`
	ServiceCategory *CategoryInfo `json:"service_category,omitempty"`
}

// Related info structs
type UserInfo struct {
	ID        uuid.UUID `json:"id"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Email     string    `json:"email"`
}

type OrderInfo struct {
	ID          uuid.UUID `json:"id"`
	OrderNumber string    `json:"order_number"`
	TotalAmount float64   `json:"total_amount"`
	Status      string    `json:"status"`
}

type BookingInfo struct {
	ID            uuid.UUID `json:"id"`
	BookingNumber string    `json:"booking_number"`
	TotalAmount   float64   `json:"total_amount"`
	Status        string    `json:"status"`
}

type CategoryInfo struct {
	ID   uuid.UUID `json:"id"`
	Name string    `json:"name"`
	Icon string    `json:"icon"`
}

// Request DTOs
type CreateCouponRequest struct {
	Code                  string     `json:"code" validate:"required,min=3,max=50"`
	Name                  string     `json:"name" validate:"required,min=1,max=100"`
	Description           string     `json:"description" validate:"required,min=1,max=500"`
	DiscountType          string     `json:"discount_type" validate:"required,oneof=percentage fixed_amount free_shipping"`
	DiscountValue         float64    `json:"discount_value" validate:"required,min=0"`
	MinimumOrderAmount    float64    `json:"minimum_order_amount" validate:"min=0"`
	MaximumDiscountAmount *float64   `json:"maximum_discount_amount,omitempty" validate:"omitempty,min=0"`
	UsageLimit            *int       `json:"usage_limit,omitempty" validate:"omitempty,min=1"`
	UsageLimitPerUser     int        `json:"usage_limit_per_user" validate:"min=1"`
	ValidFrom             *time.Time `json:"valid_from,omitempty"`
	ValidUntil            *time.Time `json:"valid_until,omitempty"`
	ApplicableTo          string     `json:"applicable_to" validate:"required,oneof=all products services categories"`
	CategoryIDs           []uuid.UUID `json:"category_ids,omitempty"`
}

type UpdateCouponRequest struct {
	Name                  *string    `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Description           *string    `json:"description,omitempty" validate:"omitempty,min=1,max=500"`
	DiscountValue         *float64   `json:"discount_value,omitempty" validate:"omitempty,min=0"`
	MinimumOrderAmount    *float64   `json:"minimum_order_amount,omitempty" validate:"omitempty,min=0"`
	MaximumDiscountAmount *float64   `json:"maximum_discount_amount,omitempty" validate:"omitempty,min=0"`
	UsageLimit            *int       `json:"usage_limit,omitempty" validate:"omitempty,min=1"`
	UsageLimitPerUser     *int       `json:"usage_limit_per_user,omitempty" validate:"omitempty,min=1"`
	IsActive              *bool      `json:"is_active,omitempty"`
	ValidFrom             *time.Time `json:"valid_from,omitempty"`
	ValidUntil            *time.Time `json:"valid_until,omitempty"`
}

type ValidateCouponRequest struct {
	Code      string  `json:"code" validate:"required"`
	UserID    uuid.UUID `json:"user_id" validate:"required"`
	Subtotal  float64 `json:"subtotal" validate:"required,min=0"`
	OrderType string  `json:"order_type" validate:"required,oneof=product service"`
}

type ApplyCouponRequest struct {
	Code      string    `json:"code" validate:"required"`
	UserID    uuid.UUID `json:"user_id" validate:"required"`
	OrderID   *uuid.UUID `json:"order_id,omitempty"`
	BookingID *uuid.UUID `json:"booking_id,omitempty"`
	Subtotal  float64   `json:"subtotal" validate:"required,min=0"`
}

type CouponSearchQuery struct {
	Code         string     `form:"code"`
	Name         string     `form:"name"`
	DiscountType string     `form:"discount_type" validate:"omitempty,oneof=percentage fixed_amount free_shipping"`
	IsActive     *bool      `form:"is_active"`
	ValidFrom    *time.Time `form:"valid_from"`
	ValidUntil   *time.Time `form:"valid_until"`
	CreatedBy    *uuid.UUID `form:"created_by"`
	SortBy       string     `form:"sort_by" validate:"omitempty,oneof=code name discount_value created_at"`
	SortOrder    string     `form:"sort_order" validate:"omitempty,oneof=asc desc"`
	Page         int        `form:"page,default=1" validate:"min=1"`
	Limit        int        `form:"limit,default=20" validate:"min=1,max=100"`
}

// Response DTOs
type CouponResponse struct {
	Success bool    `json:"success"`
	Data    *Coupon `json:"data,omitempty"`
	Message string  `json:"message,omitempty"`
	Errors  []string `json:"errors,omitempty"`
}

type CouponsResponse struct {
	Success bool      `json:"success"`
	Data    []*Coupon `json:"data,omitempty"`
	Message string    `json:"message,omitempty"`
	Errors  []string  `json:"errors,omitempty"`
}

type ValidateCouponResponse struct {
	Success        bool    `json:"success"`
	Valid          bool    `json:"valid"`
	DiscountAmount float64 `json:"discount_amount"`
	Message        string  `json:"message,omitempty"`
	Errors         []string `json:"errors,omitempty"`
}

type CouponStatsResponse struct {
	Success bool         `json:"success"`
	Data    *CouponStats `json:"data,omitempty"`
	Message string       `json:"message,omitempty"`
	Errors  []string     `json:"errors,omitempty"`
}

// Statistics
type CouponStats struct {
	TotalCoupons       int64   `json:"total_coupons"`
	ActiveCoupons      int64   `json:"active_coupons"`
	ExpiredCoupons     int64   `json:"expired_coupons"`
	TotalUsages        int64   `json:"total_usages"`
	TotalDiscountGiven float64 `json:"total_discount_given"`
	TopCoupons         []TopCouponUsage `json:"top_coupons"`
}

type TopCouponUsage struct {
	Code        string  `json:"code"`
	Name        string  `json:"name"`
	UsageCount  int     `json:"usage_count"`
	TotalSaved  float64 `json:"total_saved"`
}

// Constants
const (
	DiscountTypePercentage   = "percentage"
	DiscountTypeFixedAmount  = "fixed_amount"
	DiscountTypeFreeShipping = "free_shipping"
)

const (
	ApplicableToAll        = "all"
	ApplicableToProducts   = "products"
	ApplicableToServices   = "services"
	ApplicableToCategories = "categories"
)

// Validation constants
const (
	MaxCouponCodeLength = 50
	MaxCouponNameLength = 100
	MaxUsageLimit       = 10000
	MaxDiscountValue    = 10000.00
)
