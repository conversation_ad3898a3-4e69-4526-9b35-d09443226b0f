package repository

import (
	"database/sql"
	"fmt"
	"shark/coupon-service/internal/models"
	"strings"
	"time"

	"github.com/google/uuid"
)

type CouponRepository struct {
	db *sql.DB
}

func NewCouponRepository(db *sql.DB) *CouponRepository {
	return &CouponRepository{db: db}
}

// CRUD Operations
func (r *CouponRepository) Create(coupon *models.Coupon) error {
	tx, err := r.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Insert coupon
	query := `
		INSERT INTO coupons (id, code, name, description, discount_type, discount_value, 
		                    minimum_order_amount, maximum_discount_amount, usage_limit, 
		                    usage_limit_per_user, is_active, valid_from, valid_until, 
		                    applicable_to, created_by, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		RETURNING created_at, updated_at`

	err = tx.QueryRow(query,
		coupon.ID, coupon.Code, coupon.Name, coupon.Description, coupon.DiscountType,
		coupon.DiscountValue, coupon.MinimumOrderAmount, coupon.MaximumDiscountAmount,
		coupon.UsageLimit, coupon.UsageLimitPerUser, coupon.IsActive, coupon.ValidFrom,
		coupon.ValidUntil, coupon.ApplicableTo, coupon.CreatedBy).Scan(&coupon.CreatedAt, &coupon.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create coupon: %w", err)
	}

	// Insert category associations if applicable
	if coupon.ApplicableTo == models.ApplicableToCategories && len(coupon.Categories) > 0 {
		for _, category := range coupon.Categories {
			_, err = tx.Exec(`
				INSERT INTO coupon_categories (id, coupon_id, product_category_id, service_category_id)
				VALUES ($1, $2, $3, $4)`,
				uuid.New(), coupon.ID, category.ProductCategoryID, category.ServiceCategoryID)
			
			if err != nil {
				return fmt.Errorf("failed to create coupon category: %w", err)
			}
		}
	}

	return tx.Commit()
}

func (r *CouponRepository) GetByID(id uuid.UUID) (*models.Coupon, error) {
	coupon := &models.Coupon{}
	
	query := `
		SELECT id, code, name, description, discount_type, discount_value, 
		       minimum_order_amount, maximum_discount_amount, usage_limit, 
		       usage_limit_per_user, used_count, is_active, valid_from, 
		       valid_until, applicable_to, created_by, created_at, updated_at
		FROM coupons WHERE id = $1`

	err := r.db.QueryRow(query, id).Scan(
		&coupon.ID, &coupon.Code, &coupon.Name, &coupon.Description, &coupon.DiscountType,
		&coupon.DiscountValue, &coupon.MinimumOrderAmount, &coupon.MaximumDiscountAmount,
		&coupon.UsageLimit, &coupon.UsageLimitPerUser, &coupon.UsedCount, &coupon.IsActive,
		&coupon.ValidFrom, &coupon.ValidUntil, &coupon.ApplicableTo, &coupon.CreatedBy,
		&coupon.CreatedAt, &coupon.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, fmt.Errorf("coupon not found")
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}

	// Load categories if applicable
	if coupon.ApplicableTo == models.ApplicableToCategories {
		categories, err := r.getCouponCategories(coupon.ID)
		if err != nil {
			return nil, err
		}
		coupon.Categories = categories
	}

	// Calculate remaining uses
	if coupon.UsageLimit != nil {
		remaining := *coupon.UsageLimit - coupon.UsedCount
		coupon.RemainingUses = &remaining
	}

	return coupon, nil
}

func (r *CouponRepository) GetByCode(code string) (*models.Coupon, error) {
	coupon := &models.Coupon{}
	
	query := `
		SELECT id, code, name, description, discount_type, discount_value, 
		       minimum_order_amount, maximum_discount_amount, usage_limit, 
		       usage_limit_per_user, used_count, is_active, valid_from, 
		       valid_until, applicable_to, created_by, created_at, updated_at
		FROM coupons WHERE code = $1`

	err := r.db.QueryRow(query, code).Scan(
		&coupon.ID, &coupon.Code, &coupon.Name, &coupon.Description, &coupon.DiscountType,
		&coupon.DiscountValue, &coupon.MinimumOrderAmount, &coupon.MaximumDiscountAmount,
		&coupon.UsageLimit, &coupon.UsageLimitPerUser, &coupon.UsedCount, &coupon.IsActive,
		&coupon.ValidFrom, &coupon.ValidUntil, &coupon.ApplicableTo, &coupon.CreatedBy,
		&coupon.CreatedAt, &coupon.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, fmt.Errorf("coupon not found")
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}

	return coupon, nil
}

func (r *CouponRepository) List(query *models.CouponSearchQuery) ([]*models.Coupon, int, error) {
	// Build WHERE clause
	whereClause, args := r.buildWhereClause(query)
	
	// Count total records
	countQuery := "SELECT COUNT(*) FROM coupons" + whereClause
	var total int
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count coupons: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "created_at DESC"
	if query.SortBy != "" {
		direction := "ASC"
		if query.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("%s %s", query.SortBy, direction)
	}

	// Calculate offset
	offset := (query.Page - 1) * query.Limit

	// Main query
	mainQuery := fmt.Sprintf(`
		SELECT id, code, name, description, discount_type, discount_value, 
		       minimum_order_amount, maximum_discount_amount, usage_limit, 
		       usage_limit_per_user, used_count, is_active, valid_from, 
		       valid_until, applicable_to, created_by, created_at, updated_at
		FROM coupons%s ORDER BY %s LIMIT $%d OFFSET $%d`,
		whereClause, orderBy, len(args)+1, len(args)+2)

	args = append(args, query.Limit, offset)

	rows, err := r.db.Query(mainQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list coupons: %w", err)
	}
	defer rows.Close()

	var coupons []*models.Coupon
	for rows.Next() {
		coupon := &models.Coupon{}
		err := rows.Scan(
			&coupon.ID, &coupon.Code, &coupon.Name, &coupon.Description, &coupon.DiscountType,
			&coupon.DiscountValue, &coupon.MinimumOrderAmount, &coupon.MaximumDiscountAmount,
			&coupon.UsageLimit, &coupon.UsageLimitPerUser, &coupon.UsedCount, &coupon.IsActive,
			&coupon.ValidFrom, &coupon.ValidUntil, &coupon.ApplicableTo, &coupon.CreatedBy,
			&coupon.CreatedAt, &coupon.UpdatedAt)

		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan coupon: %w", err)
		}

		// Calculate remaining uses
		if coupon.UsageLimit != nil {
			remaining := *coupon.UsageLimit - coupon.UsedCount
			coupon.RemainingUses = &remaining
		}

		coupons = append(coupons, coupon)
	}

	return coupons, total, nil
}

func (r *CouponRepository) Update(id uuid.UUID, updates *models.UpdateCouponRequest) error {
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if updates.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *updates.Name)
		argIndex++
	}

	if updates.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *updates.Description)
		argIndex++
	}

	if updates.DiscountValue != nil {
		setParts = append(setParts, fmt.Sprintf("discount_value = $%d", argIndex))
		args = append(args, *updates.DiscountValue)
		argIndex++
	}

	if updates.MinimumOrderAmount != nil {
		setParts = append(setParts, fmt.Sprintf("minimum_order_amount = $%d", argIndex))
		args = append(args, *updates.MinimumOrderAmount)
		argIndex++
	}

	if updates.MaximumDiscountAmount != nil {
		setParts = append(setParts, fmt.Sprintf("maximum_discount_amount = $%d", argIndex))
		args = append(args, *updates.MaximumDiscountAmount)
		argIndex++
	}

	if updates.UsageLimit != nil {
		setParts = append(setParts, fmt.Sprintf("usage_limit = $%d", argIndex))
		args = append(args, *updates.UsageLimit)
		argIndex++
	}

	if updates.UsageLimitPerUser != nil {
		setParts = append(setParts, fmt.Sprintf("usage_limit_per_user = $%d", argIndex))
		args = append(args, *updates.UsageLimitPerUser)
		argIndex++
	}

	if updates.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *updates.IsActive)
		argIndex++
	}

	if updates.ValidFrom != nil {
		setParts = append(setParts, fmt.Sprintf("valid_from = $%d", argIndex))
		args = append(args, *updates.ValidFrom)
		argIndex++
	}

	if updates.ValidUntil != nil {
		setParts = append(setParts, fmt.Sprintf("valid_until = $%d", argIndex))
		args = append(args, *updates.ValidUntil)
		argIndex++
	}

	if len(setParts) == 0 {
		return fmt.Errorf("no fields to update")
	}

	setParts = append(setParts, fmt.Sprintf("updated_at = CURRENT_TIMESTAMP"))
	
	query := fmt.Sprintf("UPDATE coupons SET %s WHERE id = $%d", 
		strings.Join(setParts, ", "), argIndex)
	args = append(args, id)

	result, err := r.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to update coupon: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("coupon not found")
	}

	return nil
}

func (r *CouponRepository) Delete(id uuid.UUID) error {
	result, err := r.db.Exec("UPDATE coupons SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1", id)
	if err != nil {
		return fmt.Errorf("failed to delete coupon: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("coupon not found")
	}

	return nil
}

// Validation and Usage
func (r *CouponRepository) ValidateCoupon(code string, userID uuid.UUID, subtotal float64, orderType string) (*models.Coupon, float64, error) {
	coupon, err := r.GetByCode(code)
	if err != nil {
		return nil, 0, fmt.Errorf("coupon not found: %w", err)
	}

	// Check if coupon is active
	if !coupon.IsActive {
		return nil, 0, fmt.Errorf("coupon is not active")
	}

	// Check time validity
	now := time.Now()
	if now.Before(coupon.ValidFrom) {
		return nil, 0, fmt.Errorf("coupon is not yet valid")
	}
	if coupon.ValidUntil != nil && now.After(*coupon.ValidUntil) {
		return nil, 0, fmt.Errorf("coupon has expired")
	}

	// Check usage limits
	if coupon.UsageLimit != nil && coupon.UsedCount >= *coupon.UsageLimit {
		return nil, 0, fmt.Errorf("coupon usage limit exceeded")
	}

	// Check per-user usage limit
	userUsageCount, err := r.getUserUsageCount(coupon.ID, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to check user usage: %w", err)
	}
	if userUsageCount >= coupon.UsageLimitPerUser {
		return nil, 0, fmt.Errorf("user usage limit exceeded")
	}

	// Check minimum order amount
	if subtotal < coupon.MinimumOrderAmount {
		return nil, 0, fmt.Errorf("order amount (%.2f) is below minimum required (%.2f)", 
			subtotal, coupon.MinimumOrderAmount)
	}

	// Calculate discount
	discount, err := r.calculateDiscount(coupon, subtotal)
	if err != nil {
		return nil, 0, err
	}

	return coupon, discount, nil
}

func (r *CouponRepository) RecordUsage(couponID, userID uuid.UUID, orderID, bookingID *uuid.UUID, discountAmount float64) error {
	tx, err := r.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Record usage
	_, err = tx.Exec(`
		INSERT INTO coupon_usage (id, coupon_id, user_id, order_id, booking_id, discount_amount, used_at)
		VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)`,
		uuid.New(), couponID, userID, orderID, bookingID, discountAmount)

	if err != nil {
		return fmt.Errorf("failed to record usage: %w", err)
	}

	// Increment used count
	_, err = tx.Exec(`
		UPDATE coupons SET used_count = used_count + 1, updated_at = CURRENT_TIMESTAMP 
		WHERE id = $1`, couponID)

	if err != nil {
		return fmt.Errorf("failed to update used count: %w", err)
	}

	return tx.Commit()
}

// Statistics
func (r *CouponRepository) GetStats() (*models.CouponStats, error) {
	stats := &models.CouponStats{}

	// Get basic counts
	err := r.db.QueryRow(`
		SELECT 
			COUNT(*) as total,
			COUNT(CASE WHEN is_active = true THEN 1 END) as active,
			COUNT(CASE WHEN is_active = true AND valid_until < CURRENT_TIMESTAMP THEN 1 END) as expired
		FROM coupons`).Scan(&stats.TotalCoupons, &stats.ActiveCoupons, &stats.ExpiredCoupons)

	if err != nil {
		return nil, fmt.Errorf("failed to get basic stats: %w", err)
	}

	// Get usage stats
	err = r.db.QueryRow(`
		SELECT 
			COUNT(*) as total_usages,
			COALESCE(SUM(discount_amount), 0) as total_discount
		FROM coupon_usage`).Scan(&stats.TotalUsages, &stats.TotalDiscountGiven)

	if err != nil {
		return nil, fmt.Errorf("failed to get usage stats: %w", err)
	}

	// Get top coupons
	topCoupons, err := r.getTopCoupons()
	if err != nil {
		return nil, err
	}
	stats.TopCoupons = topCoupons

	return stats, nil
}

// Helper methods
func (r *CouponRepository) buildWhereClause(query *models.CouponSearchQuery) (string, []interface{}) {
	conditions := []string{}
	args := []interface{}{}
	argIndex := 1

	if query.Code != "" {
		conditions = append(conditions, fmt.Sprintf("code ILIKE $%d", argIndex))
		args = append(args, "%"+query.Code+"%")
		argIndex++
	}

	if query.Name != "" {
		conditions = append(conditions, fmt.Sprintf("name ILIKE $%d", argIndex))
		args = append(args, "%"+query.Name+"%")
		argIndex++
	}

	if query.DiscountType != "" {
		conditions = append(conditions, fmt.Sprintf("discount_type = $%d", argIndex))
		args = append(args, query.DiscountType)
		argIndex++
	}

	if query.IsActive != nil {
		conditions = append(conditions, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *query.IsActive)
		argIndex++
	}

	if query.CreatedBy != nil {
		conditions = append(conditions, fmt.Sprintf("created_by = $%d", argIndex))
		args = append(args, *query.CreatedBy)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	return whereClause, args
}

func (r *CouponRepository) getCouponCategories(couponID uuid.UUID) ([]models.CouponCategory, error) {
	query := `
		SELECT cc.id, cc.coupon_id, cc.product_category_id, cc.service_category_id,
		       pc.name as product_category_name, pc.icon as product_category_icon,
		       sc.name as service_category_name, sc.icon as service_category_icon
		FROM coupon_categories cc
		LEFT JOIN product_categories pc ON cc.product_category_id = pc.id
		LEFT JOIN service_categories sc ON cc.service_category_id = sc.id
		WHERE cc.coupon_id = $1`

	rows, err := r.db.Query(query, couponID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var categories []models.CouponCategory
	for rows.Next() {
		var category models.CouponCategory
		var productName, productIcon, serviceName, serviceIcon sql.NullString

		err := rows.Scan(
			&category.ID, &category.CouponID, &category.ProductCategoryID, &category.ServiceCategoryID,
			&productName, &productIcon, &serviceName, &serviceIcon)

		if err != nil {
			return nil, err
		}

		if category.ProductCategoryID != nil {
			category.ProductCategory = &models.CategoryInfo{
				ID:   *category.ProductCategoryID,
				Name: productName.String,
				Icon: productIcon.String,
			}
		}

		if category.ServiceCategoryID != nil {
			category.ServiceCategory = &models.CategoryInfo{
				ID:   *category.ServiceCategoryID,
				Name: serviceName.String,
				Icon: serviceIcon.String,
			}
		}

		categories = append(categories, category)
	}

	return categories, nil
}

func (r *CouponRepository) getUserUsageCount(couponID, userID uuid.UUID) (int, error) {
	var count int
	err := r.db.QueryRow(`
		SELECT COUNT(*) FROM coupon_usage 
		WHERE coupon_id = $1 AND user_id = $2`, couponID, userID).Scan(&count)
	return count, err
}

func (r *CouponRepository) calculateDiscount(coupon *models.Coupon, subtotal float64) (float64, error) {
	var discount float64

	switch coupon.DiscountType {
	case models.DiscountTypePercentage:
		discount = subtotal * (coupon.DiscountValue / 100)
		if coupon.MaximumDiscountAmount != nil && discount > *coupon.MaximumDiscountAmount {
			discount = *coupon.MaximumDiscountAmount
		}
	case models.DiscountTypeFixedAmount:
		discount = coupon.DiscountValue
		if discount > subtotal {
			discount = subtotal
		}
	case models.DiscountTypeFreeShipping:
		discount = 0 // Handled separately in shipping calculation
	default:
		return 0, fmt.Errorf("unsupported discount type: %s", coupon.DiscountType)
	}

	return discount, nil
}

func (r *CouponRepository) getTopCoupons() ([]models.TopCouponUsage, error) {
	query := `
		SELECT c.code, c.name, COUNT(cu.id) as usage_count, COALESCE(SUM(cu.discount_amount), 0) as total_saved
		FROM coupons c
		LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id
		WHERE c.is_active = true
		GROUP BY c.id, c.code, c.name
		ORDER BY usage_count DESC, total_saved DESC
		LIMIT 10`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var topCoupons []models.TopCouponUsage
	for rows.Next() {
		var top models.TopCouponUsage
		err := rows.Scan(&top.Code, &top.Name, &top.UsageCount, &top.TotalSaved)
		if err != nil {
			return nil, err
		}
		topCoupons = append(topCoupons, top)
	}

	return topCoupons, nil
}
