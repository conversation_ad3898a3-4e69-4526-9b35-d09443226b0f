package service

import (
	"fmt"
	"shark/coupon-service/internal/models"
	"shark/coupon-service/internal/repository"
	"strings"
	"time"

	"github.com/google/uuid"
)

type CouponService struct {
	couponRepo *repository.CouponRepository
}

func NewCouponService(couponRepo *repository.CouponRepository) *CouponService {
	return &CouponService{
		couponRepo: couponRepo,
	}
}

// CRUD Operations
func (s *CouponService) CreateCoupon(req *models.CreateCouponRequest, createdBy uuid.UUID) (*models.Coupon, error) {
	// Validate request
	if err := s.validateCreateRequest(req); err != nil {
		return nil, err
	}

	// Check if code already exists
	existing, _ := s.couponRepo.GetByCode(req.Code)
	if existing != nil {
		return nil, fmt.Errorf("coupon code already exists")
	}

	// Create coupon
	coupon := &models.Coupon{
		ID:                    uuid.New(),
		Code:                  strings.ToUpper(req.Code),
		Name:                  req.Name,
		Description:           req.Description,
		DiscountType:          req.DiscountType,
		DiscountValue:         req.DiscountValue,
		MinimumOrderAmount:    req.MinimumOrderAmount,
		MaximumDiscountAmount: req.MaximumDiscountAmount,
		UsageLimit:            req.UsageLimit,
		UsageLimitPerUser:     req.UsageLimitPerUser,
		UsedCount:             0,
		IsActive:              true,
		ValidFrom:             time.Now(),
		ValidUntil:            req.ValidUntil,
		ApplicableTo:          req.ApplicableTo,
		CreatedBy:             createdBy,
	}

	if req.ValidFrom != nil {
		coupon.ValidFrom = *req.ValidFrom
	}

	// Handle category associations
	if req.ApplicableTo == models.ApplicableToCategories && len(req.CategoryIDs) > 0 {
		for _, categoryID := range req.CategoryIDs {
			category := models.CouponCategory{
				ID:       uuid.New(),
				CouponID: coupon.ID,
			}

			// Determine if it's a product or service category
			// This would need to be enhanced to check the actual category type
			category.ProductCategoryID = &categoryID

			coupon.Categories = append(coupon.Categories, category)
		}
	}

	err := s.couponRepo.Create(coupon)
	if err != nil {
		return nil, fmt.Errorf("failed to create coupon: %w", err)
	}

	return coupon, nil
}

func (s *CouponService) GetCoupon(id uuid.UUID) (*models.Coupon, error) {
	coupon, err := s.couponRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}

	return coupon, nil
}

func (s *CouponService) GetCouponByCode(code string) (*models.Coupon, error) {
	coupon, err := s.couponRepo.GetByCode(code)
	if err != nil {
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}

	return coupon, nil
}

func (s *CouponService) ListCoupons(query *models.CouponSearchQuery) ([]*models.Coupon, int, error) {
	// Set defaults
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.Limit <= 0 {
		query.Limit = 20
	}
	if query.Limit > 100 {
		query.Limit = 100
	}

	coupons, total, err := s.couponRepo.List(query)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list coupons: %w", err)
	}

	return coupons, total, nil
}

func (s *CouponService) UpdateCoupon(id uuid.UUID, req *models.UpdateCouponRequest) (*models.Coupon, error) {
	// Validate request
	if err := s.validateUpdateRequest(req); err != nil {
		return nil, err
	}

	// Check if coupon exists
	existing, err := s.couponRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("coupon not found: %w", err)
	}

	// Prevent updating code of used coupons
	if existing.UsedCount > 0 && req.DiscountValue != nil {
		return nil, fmt.Errorf("cannot modify discount value of used coupon")
	}

	err = s.couponRepo.Update(id, req)
	if err != nil {
		return nil, fmt.Errorf("failed to update coupon: %w", err)
	}

	// Return updated coupon
	return s.couponRepo.GetByID(id)
}

func (s *CouponService) DeleteCoupon(id uuid.UUID) error {
	// Check if coupon exists
	existing, err := s.couponRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("coupon not found: %w", err)
	}

	// Prevent deleting used coupons
	if existing.UsedCount > 0 {
		return fmt.Errorf("cannot delete coupon that has been used")
	}

	err = s.couponRepo.Delete(id)
	if err != nil {
		return fmt.Errorf("failed to delete coupon: %w", err)
	}

	return nil
}

// Validation and Usage
func (s *CouponService) ValidateCoupon(req *models.ValidateCouponRequest) (*models.Coupon, float64, error) {
	coupon, discount, err := s.couponRepo.ValidateCoupon(req.Code, req.UserID, req.Subtotal, req.OrderType)
	if err != nil {
		return nil, 0, fmt.Errorf("coupon validation failed: %w", err)
	}

	return coupon, discount, nil
}

func (s *CouponService) ApplyCoupon(req *models.ApplyCouponRequest) (*models.Coupon, float64, error) {
	// Validate coupon first
	validateReq := &models.ValidateCouponRequest{
		Code:      req.Code,
		UserID:    req.UserID,
		Subtotal:  req.Subtotal,
		OrderType: "product", // Default to product
	}

	if req.BookingID != nil {
		validateReq.OrderType = "service"
	}

	coupon, discount, err := s.ValidateCoupon(validateReq)
	if err != nil {
		return nil, 0, err
	}

	// Record usage
	err = s.couponRepo.RecordUsage(coupon.ID, req.UserID, req.OrderID, req.BookingID, discount)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to record coupon usage: %w", err)
	}

	return coupon, discount, nil
}

// Analytics
func (s *CouponService) GetCouponStats() (*models.CouponStats, error) {
	stats, err := s.couponRepo.GetStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get coupon stats: %w", err)
	}

	return stats, nil
}

func (s *CouponService) GetCouponUsageHistory(couponID uuid.UUID, page, limit int) ([]models.CouponUsage, int, error) {
	// This would be implemented in the repository
	// For now, return empty slice
	return []models.CouponUsage{}, 0, nil
}

// Bulk Operations
func (s *CouponService) BulkCreateCoupons(coupons []*models.CreateCouponRequest, createdBy uuid.UUID) ([]*models.Coupon, []error) {
	var results []*models.Coupon
	var errors []error

	for _, req := range coupons {
		coupon, err := s.CreateCoupon(req, createdBy)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to create coupon %s: %w", req.Code, err))
			results = append(results, nil)
		} else {
			errors = append(errors, nil)
			results = append(results, coupon)
		}
	}

	return results, errors
}

func (s *CouponService) BulkDeactivateCoupons(couponIDs []uuid.UUID) []error {
	var errors []error

	for _, id := range couponIDs {
		req := &models.UpdateCouponRequest{
			IsActive: &[]bool{false}[0],
		}

		_, err := s.UpdateCoupon(id, req)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to deactivate coupon %s: %w", id, err))
		} else {
			errors = append(errors, nil)
		}
	}

	return errors
}

// Validation methods
func (s *CouponService) validateCreateRequest(req *models.CreateCouponRequest) error {
	// Code validation
	if len(req.Code) < 3 || len(req.Code) > models.MaxCouponCodeLength {
		return fmt.Errorf("coupon code must be between 3 and %d characters", models.MaxCouponCodeLength)
	}

	// Discount value validation
	if req.DiscountValue <= 0 {
		return fmt.Errorf("discount value must be greater than 0")
	}

	if req.DiscountValue > models.MaxDiscountValue {
		return fmt.Errorf("discount value cannot exceed %.2f", models.MaxDiscountValue)
	}

	// Percentage validation
	if req.DiscountType == models.DiscountTypePercentage && req.DiscountValue > 100 {
		return fmt.Errorf("percentage discount cannot exceed 100%%")
	}

	// Usage limit validation
	if req.UsageLimit != nil && *req.UsageLimit > models.MaxUsageLimit {
		return fmt.Errorf("usage limit cannot exceed %d", models.MaxUsageLimit)
	}

	if req.UsageLimitPerUser <= 0 {
		return fmt.Errorf("usage limit per user must be greater than 0")
	}

	// Date validation
	if req.ValidFrom != nil && req.ValidUntil != nil {
		if req.ValidUntil.Before(*req.ValidFrom) {
			return fmt.Errorf("valid until date must be after valid from date")
		}
	}

	// Category validation
	if req.ApplicableTo == models.ApplicableToCategories && len(req.CategoryIDs) == 0 {
		return fmt.Errorf("category IDs are required when applicable_to is 'categories'")
	}

	return nil
}

func (s *CouponService) validateUpdateRequest(req *models.UpdateCouponRequest) error {
	// Discount value validation
	if req.DiscountValue != nil {
		if *req.DiscountValue <= 0 {
			return fmt.Errorf("discount value must be greater than 0")
		}
		if *req.DiscountValue > models.MaxDiscountValue {
			return fmt.Errorf("discount value cannot exceed %.2f", models.MaxDiscountValue)
		}
	}

	// Usage limit validation
	if req.UsageLimit != nil && *req.UsageLimit > models.MaxUsageLimit {
		return fmt.Errorf("usage limit cannot exceed %d", models.MaxUsageLimit)
	}

	if req.UsageLimitPerUser != nil && *req.UsageLimitPerUser <= 0 {
		return fmt.Errorf("usage limit per user must be greater than 0")
	}

	// Date validation
	if req.ValidFrom != nil && req.ValidUntil != nil {
		if req.ValidUntil.Before(*req.ValidFrom) {
			return fmt.Errorf("valid until date must be after valid from date")
		}
	}

	return nil
}

// Utility methods
func (s *CouponService) GenerateCouponCode(prefix string, length int) string {
	if length < 6 {
		length = 6
	}
	if length > models.MaxCouponCodeLength {
		length = models.MaxCouponCodeLength
	}

	// Simple code generation - in production, use a more sophisticated method
	timestamp := time.Now().Unix()
	code := fmt.Sprintf("%s%d", strings.ToUpper(prefix), timestamp)
	
	if len(code) > length {
		code = code[:length]
	}

	return code
}

func (s *CouponService) CheckCodeAvailability(code string) (bool, error) {
	_, err := s.couponRepo.GetByCode(code)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return true, nil // Code is available
		}
		return false, err // Database error
	}
	return false, nil // Code already exists
}
