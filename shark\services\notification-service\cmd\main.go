package main

import (
	"log"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
)

func main() {
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	router := setupRouter()

	port := os.Getenv("PORT")
	if port == "" {
		port = "8006"
	}

	log.Printf("Notification Service starting on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func setupRouter() http.Handler {
	router := gin.Default()

	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "service": "notification-service"})
	})

	api := router.Group("/api/v1")
	{
		public := api.Group("/public")
		{
			public.GET("/notifications", func(c *gin.Context) {
				c.JSON(200, gin.H{"success": true, "data": []interface{}{}})
			})
		}
	}

	return c.Handler(router)
}
