package main

import (
	"log"
	"net/http"
	"os"

	"shark/payment-service/internal/config"
	"shark/payment-service/internal/handlers"
	"shark/payment-service/internal/middleware"
	"shark/payment-service/internal/repository"
	"shark/payment-service/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
)

func main() {
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	cfg := config.Load()

	db, err := config.InitDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	paymentRepo := repository.NewPaymentRepository(db)
	paymentService := services.NewPaymentService(paymentRepo, cfg)
	paymentHandler := handlers.NewPaymentHandler(paymentService)

	router := setupRouter(paymentHandler, cfg)

	port := os.Getenv("PORT")
	if port == "" {
		port = "8004"
	}

	log.Printf("Payment Service starting on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func setupRouter(paymentHandler *handlers.PaymentHandler, cfg *config.Config) http.Handler {
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "service": "payment-service"})
	})

	api := router.Group("/api/v1")
	{
		protected := api.Group("")
		protected.Use(middleware.AuthMiddleware(cfg.JWTSecret))
		{
			// Customer payment routes
			customer := protected.Group("/customer")
			{
				customer.POST("/payment-intents", paymentHandler.CreatePaymentIntent)
				customer.POST("/payments", paymentHandler.ProcessPayment)
				customer.GET("/payments", paymentHandler.GetMyPayments)
				customer.GET("/payments/:id", paymentHandler.GetPayment)
				customer.POST("/refunds", paymentHandler.CreateRefund)
				customer.GET("/payments/stats", paymentHandler.GetPaymentStats)
			}

			// Admin routes
			admin := protected.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				admin.GET("/payments/search", paymentHandler.SearchPayments)
				admin.GET("/payments/stats", paymentHandler.GetPaymentStats)
			}
		}
	}

	return c.Handler(router)
}
