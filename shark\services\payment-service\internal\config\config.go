package config

import (
	"database/sql"
	"fmt"
	"os"
	"strconv"

	_ "github.com/lib/pq"
)

type Config struct {
	Environment string
	Port        string
	DatabaseURL string
	JWTSecret   string
	
	// Database config
	DBHost     string
	DBPort     int
	DBUser     string
	DBPassword string
	DBName     string
	DBSSLMode  string
	
	// Stripe configuration
	StripeSecretKey      string
	StripePublishableKey string
	StripeWebhookSecret  string
	
	// External service URLs
	UserServiceURL    string
	BookingServiceURL string
	OrderServiceURL   string
	
	// Business rules
	DefaultCurrency     string
	MinPaymentAmount    float64
	MaxPaymentAmount    float64
	PaymentIntentExpiry int // hours
	RefundPolicy        int // days
	
	// Redis config
	RedisURL string
}

func Load() *Config {
	return &Config{
		Environment: getEnv("ENVIRONMENT", "development"),
		Port:        getEnv("PORT", "8004"),
		DatabaseURL: getEnv("DATABASE_URL", ""),
		JWTSecret:   getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		
		DBHost:     getEnv("DB_HOST", "localhost"),
		DBPort:     getEnvAsInt("DB_PORT", 5432),
		DBUser:     getEnv("DB_USER", "shark_user"),
		DBPassword: getEnv("DB_PASSWORD", "shark_password"),
		DBName:     getEnv("DB_NAME", "shark_db"),
		DBSSLMode:  getEnv("DB_SSL_MODE", "disable"),
		
		StripeSecretKey:      getEnv("STRIPE_SECRET_KEY", "sk_test_..."),
		StripePublishableKey: getEnv("STRIPE_PUBLISHABLE_KEY", "pk_test_..."),
		StripeWebhookSecret:  getEnv("STRIPE_WEBHOOK_SECRET", "whsec_..."),
		
		UserServiceURL:    getEnv("USER_SERVICE_URL", "http://localhost:8001"),
		BookingServiceURL: getEnv("BOOKING_SERVICE_URL", "http://localhost:8003"),
		OrderServiceURL:   getEnv("ORDER_SERVICE_URL", "http://localhost:8006"),
		
		DefaultCurrency:     getEnv("DEFAULT_CURRENCY", "USD"),
		MinPaymentAmount:    getEnvAsFloat("MIN_PAYMENT_AMOUNT", 0.50),
		MaxPaymentAmount:    getEnvAsFloat("MAX_PAYMENT_AMOUNT", 10000.00),
		PaymentIntentExpiry: getEnvAsInt("PAYMENT_INTENT_EXPIRY", 24),
		RefundPolicy:        getEnvAsInt("REFUND_POLICY_DAYS", 30),
		
		RedisURL: getEnv("REDIS_URL", "redis://localhost:6379"),
	}
}

func InitDB(cfg *Config) (*sql.DB, error) {
	var dsn string
	
	if cfg.DatabaseURL != "" {
		dsn = cfg.DatabaseURL
	} else {
		dsn = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			cfg.DBHost, cfg.DBPort, cfg.DBUser, cfg.DBPassword, cfg.DBName, cfg.DBSSLMode)
	}
	
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}
	
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}
	
	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	
	return db, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsFloat(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}
