package models

import (
	"time"

	"github.com/google/uuid"
)

// Payment represents a payment transaction
type Payment struct {
	ID              uuid.UUID `json:"id" db:"id"`
	UserID          uuid.UUID `json:"user_id" db:"user_id"`
	BookingID       *uuid.UUID `json:"booking_id,omitempty" db:"booking_id"`
	OrderID         *uuid.UUID `json:"order_id,omitempty" db:"order_id"`
	Amount          float64   `json:"amount" db:"amount"`
	Currency        string    `json:"currency" db:"currency"`
	Status          string    `json:"status" db:"status"`
	PaymentMethod   string    `json:"payment_method" db:"payment_method"`
	StripePaymentID *string   `json:"stripe_payment_id,omitempty" db:"stripe_payment_id"`
	Description     string    `json:"description" db:"description"`
	Metadata        map[string]interface{} `json:"metadata,omitempty" db:"metadata"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
	
	// Related data
	User    *UserInfo    `json:"user,omitempty"`
	Booking *BookingInfo `json:"booking,omitempty"`
	Order   *OrderInfo   `json:"order,omitempty"`
}

// PaymentIntent represents a payment intent before processing
type PaymentIntent struct {
	ID            uuid.UUID `json:"id" db:"id"`
	UserID        uuid.UUID `json:"user_id" db:"user_id"`
	Amount        float64   `json:"amount" db:"amount"`
	Currency      string    `json:"currency" db:"currency"`
	Status        string    `json:"status" db:"status"`
	ClientSecret  string    `json:"client_secret" db:"client_secret"`
	StripeIntentID string   `json:"stripe_intent_id" db:"stripe_intent_id"`
	BookingID     *uuid.UUID `json:"booking_id,omitempty" db:"booking_id"`
	OrderID       *uuid.UUID `json:"order_id,omitempty" db:"order_id"`
	ExpiresAt     time.Time `json:"expires_at" db:"expires_at"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
}

// Refund represents a payment refund
type Refund struct {
	ID          uuid.UUID `json:"id" db:"id"`
	PaymentID   uuid.UUID `json:"payment_id" db:"payment_id"`
	Amount      float64   `json:"amount" db:"amount"`
	Reason      string    `json:"reason" db:"reason"`
	Status      string    `json:"status" db:"status"`
	StripeRefundID *string `json:"stripe_refund_id,omitempty" db:"stripe_refund_id"`
	ProcessedAt *time.Time `json:"processed_at,omitempty" db:"processed_at"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	
	// Related data
	Payment *Payment `json:"payment,omitempty"`
}

// Wallet represents user wallet/balance
type Wallet struct {
	ID        uuid.UUID `json:"id" db:"id"`
	UserID    uuid.UUID `json:"user_id" db:"user_id"`
	Balance   float64   `json:"balance" db:"balance"`
	Currency  string    `json:"currency" db:"currency"`
	IsActive  bool      `json:"is_active" db:"is_active"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// WalletTransaction represents wallet balance changes
type WalletTransaction struct {
	ID            uuid.UUID `json:"id" db:"id"`
	WalletID      uuid.UUID `json:"wallet_id" db:"wallet_id"`
	Type          string    `json:"type" db:"type"` // credit, debit
	Amount        float64   `json:"amount" db:"amount"`
	BalanceBefore float64   `json:"balance_before" db:"balance_before"`
	BalanceAfter  float64   `json:"balance_after" db:"balance_after"`
	Description   string    `json:"description" db:"description"`
	ReferenceID   *uuid.UUID `json:"reference_id,omitempty" db:"reference_id"`
	ReferenceType *string   `json:"reference_type,omitempty" db:"reference_type"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
}

// Related info structs
type UserInfo struct {
	ID        uuid.UUID `json:"id"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Email     string    `json:"email"`
}

type BookingInfo struct {
	ID          uuid.UUID `json:"id"`
	ServiceName string    `json:"service_name"`
	VendorName  string    `json:"vendor_name"`
	ScheduledAt time.Time `json:"scheduled_at"`
}

type OrderInfo struct {
	ID         uuid.UUID `json:"id"`
	OrderNumber string   `json:"order_number"`
	TotalItems int       `json:"total_items"`
}

// Request/Response DTOs
type CreatePaymentIntentRequest struct {
	Amount    float64    `json:"amount" validate:"required,min=0.01"`
	Currency  string     `json:"currency" validate:"required,len=3"`
	BookingID *uuid.UUID `json:"booking_id,omitempty"`
	OrderID   *uuid.UUID `json:"order_id,omitempty"`
}

type ProcessPaymentRequest struct {
	PaymentIntentID uuid.UUID `json:"payment_intent_id" validate:"required"`
	PaymentMethod   string    `json:"payment_method" validate:"required"`
	StripePaymentID *string   `json:"stripe_payment_id,omitempty"`
}

type CreateRefundRequest struct {
	PaymentID uuid.UUID `json:"payment_id" validate:"required"`
	Amount    *float64  `json:"amount,omitempty" validate:"omitempty,min=0.01"`
	Reason    string    `json:"reason" validate:"required,max=500"`
}

type WalletTopUpRequest struct {
	Amount        float64 `json:"amount" validate:"required,min=1"`
	PaymentMethod string  `json:"payment_method" validate:"required"`
}

type WalletTransferRequest struct {
	ToUserID    uuid.UUID `json:"to_user_id" validate:"required"`
	Amount      float64   `json:"amount" validate:"required,min=0.01"`
	Description string    `json:"description" validate:"required,max=200"`
}

type PaymentSearchQuery struct {
	UserID        *uuid.UUID `form:"user_id"`
	BookingID     *uuid.UUID `form:"booking_id"`
	OrderID       *uuid.UUID `form:"order_id"`
	Status        string     `form:"status" validate:"omitempty,oneof=pending processing completed failed cancelled refunded"`
	PaymentMethod string     `form:"payment_method" validate:"omitempty,oneof=stripe wallet cash"`
	DateFrom      *time.Time `form:"date_from"`
	DateTo        *time.Time `form:"date_to"`
	MinAmount     *float64   `form:"min_amount" validate:"omitempty,min=0"`
	MaxAmount     *float64   `form:"max_amount" validate:"omitempty,min=0"`
	SortBy        string     `form:"sort_by" validate:"omitempty,oneof=created_at amount"`
	SortOrder     string     `form:"sort_order" validate:"omitempty,oneof=asc desc"`
	Page          int        `form:"page,default=1" validate:"min=1"`
	Limit         int        `form:"limit,default=20" validate:"min=1,max=100"`
}

// Payment Status Constants
const (
	PaymentStatusPending    = "pending"
	PaymentStatusProcessing = "processing"
	PaymentStatusCompleted  = "completed"
	PaymentStatusFailed     = "failed"
	PaymentStatusCancelled  = "cancelled"
	PaymentStatusRefunded   = "refunded"
)

// Payment Method Constants
const (
	PaymentMethodStripe = "stripe"
	PaymentMethodWallet = "wallet"
	PaymentMethodCash   = "cash"
)

// Wallet Transaction Types
const (
	WalletTransactionCredit = "credit"
	WalletTransactionDebit  = "debit"
)

// API Response wrapper
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
	Errors  []string    `json:"errors,omitempty"`
}

// Pagination
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"total_pages"`
}

// Payment Statistics
type PaymentStats struct {
	TotalPayments     int64   `json:"total_payments"`
	CompletedPayments int64   `json:"completed_payments"`
	FailedPayments    int64   `json:"failed_payments"`
	RefundedPayments  int64   `json:"refunded_payments"`
	TotalRevenue      float64 `json:"total_revenue"`
	TotalRefunds      float64 `json:"total_refunds"`
	AvgPaymentAmount  float64 `json:"avg_payment_amount"`
}

// JWT Claims
type JWTClaims struct {
	UserID uuid.UUID `json:"user_id"`
	Email  string    `json:"email"`
	Roles  []string  `json:"roles"`
	Exp    int64     `json:"exp"`
	Iat    int64     `json:"iat"`
}
