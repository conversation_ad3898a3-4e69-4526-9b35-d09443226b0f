package repository

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"shark/payment-service/internal/models"
	"strings"
	"time"

	"github.com/google/uuid"
)

type PaymentRepository struct {
	db *sql.DB
}

func NewPaymentRepository(db *sql.DB) *PaymentRepository {
	return &PaymentRepository{db: db}
}

func (r *PaymentRepository) CreatePayment(payment *models.Payment) error {
	metadataJSON, _ := json.Marshal(payment.Metadata)
	
	query := `
		INSERT INTO payments (id, user_id, booking_id, order_id, amount, currency, status, 
		                     payment_method, stripe_payment_id, description, metadata)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING created_at, updated_at`
	
	err := r.db.QueryRow(
		query,
		payment.ID,
		payment.UserID,
		payment.BookingID,
		payment.OrderID,
		payment.Amount,
		payment.Currency,
		payment.Status,
		payment.PaymentMethod,
		payment.StripePaymentID,
		payment.Description,
		metadataJSON,
	).Scan(&payment.CreatedAt, &payment.UpdatedAt)
	
	return err
}

func (r *PaymentRepository) GetByID(id uuid.UUID) (*models.Payment, error) {
	payment := &models.Payment{}
	var metadataJSON []byte
	
	query := `
		SELECT id, user_id, booking_id, order_id, amount, currency, status, 
		       payment_method, stripe_payment_id, description, metadata, created_at, updated_at
		FROM payments WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&payment.ID,
		&payment.UserID,
		&payment.BookingID,
		&payment.OrderID,
		&payment.Amount,
		&payment.Currency,
		&payment.Status,
		&payment.PaymentMethod,
		&payment.StripePaymentID,
		&payment.Description,
		&metadataJSON,
		&payment.CreatedAt,
		&payment.UpdatedAt,
	)
	
	if err != nil {
		return nil, err
	}
	
	if len(metadataJSON) > 0 {
		json.Unmarshal(metadataJSON, &payment.Metadata)
	}
	
	return payment, nil
}

func (r *PaymentRepository) UpdatePayment(payment *models.Payment) error {
	metadataJSON, _ := json.Marshal(payment.Metadata)
	
	query := `
		UPDATE payments 
		SET status = $2, stripe_payment_id = $3, metadata = $4, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`
	
	err := r.db.QueryRow(
		query,
		payment.ID,
		payment.Status,
		payment.StripePaymentID,
		metadataJSON,
	).Scan(&payment.UpdatedAt)
	
	return err
}

func (r *PaymentRepository) SearchPayments(query *models.PaymentSearchQuery) ([]*models.Payment, int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1
	
	if query.UserID != nil {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, *query.UserID)
		argIndex++
	}
	
	if query.BookingID != nil {
		conditions = append(conditions, fmt.Sprintf("booking_id = $%d", argIndex))
		args = append(args, *query.BookingID)
		argIndex++
	}
	
	if query.OrderID != nil {
		conditions = append(conditions, fmt.Sprintf("order_id = $%d", argIndex))
		args = append(args, *query.OrderID)
		argIndex++
	}
	
	if query.Status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, query.Status)
		argIndex++
	}
	
	if query.PaymentMethod != "" {
		conditions = append(conditions, fmt.Sprintf("payment_method = $%d", argIndex))
		args = append(args, query.PaymentMethod)
		argIndex++
	}
	
	if query.DateFrom != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *query.DateFrom)
		argIndex++
	}
	
	if query.DateTo != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *query.DateTo)
		argIndex++
	}
	
	if query.MinAmount != nil {
		conditions = append(conditions, fmt.Sprintf("amount >= $%d", argIndex))
		args = append(args, *query.MinAmount)
		argIndex++
	}
	
	if query.MaxAmount != nil {
		conditions = append(conditions, fmt.Sprintf("amount <= $%d", argIndex))
		args = append(args, *query.MaxAmount)
		argIndex++
	}
	
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}
	
	// Get total count
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM payments %s", whereClause)
	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}
	
	// Build ORDER BY clause
	orderBy := "ORDER BY created_at DESC"
	if query.SortBy != "" {
		direction := "ASC"
		if query.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("ORDER BY %s %s", query.SortBy, direction)
	}
	
	// Get payments
	offset := (query.Page - 1) * query.Limit
	paymentQuery := fmt.Sprintf(`
		SELECT id, user_id, booking_id, order_id, amount, currency, status, 
		       payment_method, stripe_payment_id, description, metadata, created_at, updated_at
		FROM payments 
		%s %s
		LIMIT $%d OFFSET $%d`, whereClause, orderBy, argIndex, argIndex+1)
	
	args = append(args, query.Limit, offset)
	
	rows, err := r.db.Query(paymentQuery, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()
	
	var payments []*models.Payment
	for rows.Next() {
		payment := &models.Payment{}
		var metadataJSON []byte
		
		err := rows.Scan(
			&payment.ID,
			&payment.UserID,
			&payment.BookingID,
			&payment.OrderID,
			&payment.Amount,
			&payment.Currency,
			&payment.Status,
			&payment.PaymentMethod,
			&payment.StripePaymentID,
			&payment.Description,
			&metadataJSON,
			&payment.CreatedAt,
			&payment.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}
		
		if len(metadataJSON) > 0 {
			json.Unmarshal(metadataJSON, &payment.Metadata)
		}
		
		payments = append(payments, payment)
	}
	
	return payments, total, nil
}

func (r *PaymentRepository) GetStats(userID *uuid.UUID) (*models.PaymentStats, error) {
	stats := &models.PaymentStats{}
	
	var query string
	var args []interface{}
	
	if userID != nil {
		query = `
			SELECT 
				COUNT(*) as total_payments,
				COUNT(*) FILTER (WHERE status = 'completed') as completed_payments,
				COUNT(*) FILTER (WHERE status = 'failed') as failed_payments,
				COUNT(*) FILTER (WHERE status = 'refunded') as refunded_payments,
				COALESCE(SUM(amount) FILTER (WHERE status = 'completed'), 0) as total_revenue,
				COALESCE(SUM(amount) FILTER (WHERE status = 'refunded'), 0) as total_refunds,
				COALESCE(AVG(amount), 0) as avg_payment_amount
			FROM payments WHERE user_id = $1`
		args = []interface{}{*userID}
	} else {
		query = `
			SELECT 
				COUNT(*) as total_payments,
				COUNT(*) FILTER (WHERE status = 'completed') as completed_payments,
				COUNT(*) FILTER (WHERE status = 'failed') as failed_payments,
				COUNT(*) FILTER (WHERE status = 'refunded') as refunded_payments,
				COALESCE(SUM(amount) FILTER (WHERE status = 'completed'), 0) as total_revenue,
				COALESCE(SUM(amount) FILTER (WHERE status = 'refunded'), 0) as total_refunds,
				COALESCE(AVG(amount), 0) as avg_payment_amount
			FROM payments`
	}
	
	err := r.db.QueryRow(query, args...).Scan(
		&stats.TotalPayments,
		&stats.CompletedPayments,
		&stats.FailedPayments,
		&stats.RefundedPayments,
		&stats.TotalRevenue,
		&stats.TotalRefunds,
		&stats.AvgPaymentAmount,
	)
	
	return stats, err
}

// Payment Intent methods
func (r *PaymentRepository) CreatePaymentIntent(intent *models.PaymentIntent) error {
	query := `
		INSERT INTO payment_intents (id, user_id, amount, currency, status, client_secret, 
		                           stripe_intent_id, booking_id, order_id, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING created_at, updated_at`
	
	err := r.db.QueryRow(
		query,
		intent.ID,
		intent.UserID,
		intent.Amount,
		intent.Currency,
		intent.Status,
		intent.ClientSecret,
		intent.StripeIntentID,
		intent.BookingID,
		intent.OrderID,
		intent.ExpiresAt,
	).Scan(&intent.CreatedAt, &intent.UpdatedAt)
	
	return err
}

func (r *PaymentRepository) GetPaymentIntentByID(id uuid.UUID) (*models.PaymentIntent, error) {
	intent := &models.PaymentIntent{}
	
	query := `
		SELECT id, user_id, amount, currency, status, client_secret, stripe_intent_id, 
		       booking_id, order_id, expires_at, created_at, updated_at
		FROM payment_intents WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&intent.ID,
		&intent.UserID,
		&intent.Amount,
		&intent.Currency,
		&intent.Status,
		&intent.ClientSecret,
		&intent.StripeIntentID,
		&intent.BookingID,
		&intent.OrderID,
		&intent.ExpiresAt,
		&intent.CreatedAt,
		&intent.UpdatedAt,
	)
	
	return intent, err
}

func (r *PaymentRepository) UpdatePaymentIntent(intent *models.PaymentIntent) error {
	query := `
		UPDATE payment_intents 
		SET status = $2, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`
	
	err := r.db.QueryRow(query, intent.ID, intent.Status).Scan(&intent.UpdatedAt)
	return err
}

func (r *PaymentRepository) CleanupExpiredIntents() error {
	query := `DELETE FROM payment_intents WHERE expires_at < CURRENT_TIMESTAMP AND status = 'pending'`
	_, err := r.db.Exec(query)
	return err
}
