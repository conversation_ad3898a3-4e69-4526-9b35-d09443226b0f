package services

import (
	"errors"
	"fmt"
	"shark/payment-service/internal/config"
	"shark/payment-service/internal/models"
	"shark/payment-service/internal/repository"
	"time"

	"github.com/google/uuid"
	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/paymentintent"
)

type PaymentService struct {
	paymentRepo *repository.PaymentRepository
	config      *config.Config
}

func NewPaymentService(paymentRepo *repository.PaymentRepository, cfg *config.Config) *PaymentService {
	// Initialize Stripe
	stripe.Key = cfg.StripeSecretKey
	
	return &PaymentService{
		paymentRepo: paymentRepo,
		config:      cfg,
	}
}

func (s *PaymentService) CreatePaymentIntent(userID uuid.UUID, req *models.CreatePaymentIntentRequest) (*models.PaymentIntent, error) {
	// Validate amount
	if req.Amount < s.config.MinPaymentAmount || req.Amount > s.config.MaxPaymentAmount {
		return nil, fmt.Errorf("amount must be between %.2f and %.2f", s.config.MinPaymentAmount, s.config.MaxPaymentAmount)
	}
	
	// Create Stripe payment intent
	params := &stripe.PaymentIntentParams{
		Amount:   stripe.Int64(int64(req.Amount * 100)), // Convert to cents
		Currency: stripe.String(req.Currency),
		Metadata: map[string]string{
			"user_id": userID.String(),
		},
	}
	
	if req.BookingID != nil {
		params.Metadata["booking_id"] = req.BookingID.String()
	}
	if req.OrderID != nil {
		params.Metadata["order_id"] = req.OrderID.String()
	}
	
	stripeIntent, err := paymentintent.New(params)
	if err != nil {
		return nil, fmt.Errorf("failed to create Stripe payment intent: %w", err)
	}
	
	// Create local payment intent
	intent := &models.PaymentIntent{
		ID:             uuid.New(),
		UserID:         userID,
		Amount:         req.Amount,
		Currency:       req.Currency,
		Status:         models.PaymentStatusPending,
		ClientSecret:   stripeIntent.ClientSecret,
		StripeIntentID: stripeIntent.ID,
		BookingID:      req.BookingID,
		OrderID:        req.OrderID,
		ExpiresAt:      time.Now().Add(time.Duration(s.config.PaymentIntentExpiry) * time.Hour),
	}
	
	if err := s.paymentRepo.CreatePaymentIntent(intent); err != nil {
		return nil, err
	}
	
	return intent, nil
}

func (s *PaymentService) ProcessPayment(userID uuid.UUID, req *models.ProcessPaymentRequest) (*models.Payment, error) {
	// Get payment intent
	intent, err := s.paymentRepo.GetPaymentIntentByID(req.PaymentIntentID)
	if err != nil {
		return nil, errors.New("payment intent not found")
	}
	
	// Verify ownership
	if intent.UserID != userID {
		return nil, errors.New("unauthorized: payment intent belongs to another user")
	}
	
	// Check if intent is still valid
	if time.Now().After(intent.ExpiresAt) {
		return nil, errors.New("payment intent has expired")
	}
	
	// Create payment record
	payment := &models.Payment{
		ID:              uuid.New(),
		UserID:          userID,
		BookingID:       intent.BookingID,
		OrderID:         intent.OrderID,
		Amount:          intent.Amount,
		Currency:        intent.Currency,
		Status:          models.PaymentStatusProcessing,
		PaymentMethod:   req.PaymentMethod,
		StripePaymentID: req.StripePaymentID,
		Description:     fmt.Sprintf("Payment for amount %.2f %s", intent.Amount, intent.Currency),
		Metadata:        make(map[string]interface{}),
	}
	
	if err := s.paymentRepo.CreatePayment(payment); err != nil {
		return nil, err
	}
	
	// Update payment intent status
	intent.Status = models.PaymentStatusProcessing
	s.paymentRepo.UpdatePaymentIntent(intent)
	
	// For demo purposes, mark as completed immediately
	// In production, this would be handled by webhooks
	payment.Status = models.PaymentStatusCompleted
	s.paymentRepo.UpdatePayment(payment)
	
	return payment, nil
}

func (s *PaymentService) GetPayment(id uuid.UUID) (*models.Payment, error) {
	return s.paymentRepo.GetByID(id)
}

func (s *PaymentService) SearchPayments(query *models.PaymentSearchQuery) (*models.PaginatedResponse, error) {
	payments, total, err := s.paymentRepo.SearchPayments(query)
	if err != nil {
		return nil, err
	}
	
	totalPages := int(total) / query.Limit
	if int(total)%query.Limit > 0 {
		totalPages++
	}
	
	return &models.PaginatedResponse{
		Data:       payments,
		Total:      total,
		Page:       query.Page,
		Limit:      query.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *PaymentService) GetPaymentStats(userID *uuid.UUID) (*models.PaymentStats, error) {
	return s.paymentRepo.GetStats(userID)
}

func (s *PaymentService) CreateRefund(userID uuid.UUID, req *models.CreateRefundRequest) (*models.Refund, error) {
	// Get payment
	payment, err := s.paymentRepo.GetByID(req.PaymentID)
	if err != nil {
		return nil, errors.New("payment not found")
	}
	
	// Check if payment can be refunded
	if payment.Status != models.PaymentStatusCompleted {
		return nil, errors.New("only completed payments can be refunded")
	}
	
	// Check refund policy
	if time.Since(payment.CreatedAt) > time.Duration(s.config.RefundPolicy)*24*time.Hour {
		return nil, fmt.Errorf("refund period of %d days has expired", s.config.RefundPolicy)
	}
	
	// Determine refund amount
	refundAmount := payment.Amount
	if req.Amount != nil {
		if *req.Amount > payment.Amount {
			return nil, errors.New("refund amount cannot exceed payment amount")
		}
		refundAmount = *req.Amount
	}
	
	// Create refund record
	refund := &models.Refund{
		ID:        uuid.New(),
		PaymentID: req.PaymentID,
		Amount:    refundAmount,
		Reason:    req.Reason,
		Status:    models.PaymentStatusProcessing,
	}
	
	// For demo purposes, mark as completed immediately
	refund.Status = models.PaymentStatusCompleted
	now := time.Now()
	refund.ProcessedAt = &now
	
	// Update payment status if full refund
	if refundAmount == payment.Amount {
		payment.Status = models.PaymentStatusRefunded
		s.paymentRepo.UpdatePayment(payment)
	}
	
	return refund, nil
}

func (s *PaymentService) CleanupExpiredIntents() error {
	return s.paymentRepo.CleanupExpiredIntents()
}
