package main

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"

	"shark/product-service/internal/config"
	"shark/product-service/internal/handlers"
	"shark/product-service/internal/repository"
	"shark/product-service/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
)

func main() {
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Load configuration
	cfg := config.Load()

	// Initialize database connection
	db, err := config.InitDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize repositories
	variantRepo := repository.NewProductVariantRepository(db)

	// Initialize services
	variantService := services.NewProductVariantService(variantRepo)

	// Initialize handlers
	variantHandler := handlers.NewProductVariantHandler(variantService)

	router := setupRouter(variantHandler)

	port := cfg.Port
	if port == "" {
		port = "8010"
	}

	log.Printf("Product Service starting on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func initDB() (*sql.DB, error) {
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "shark_user")
	dbPassword := getEnv("DB_PASSWORD", "shark_password")
	dbName := getEnv("DB_NAME", "shark_db")
	dbSSLMode := getEnv("DB_SSL_MODE", "disable")

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

func setupRouter(variantHandler *handlers.ProductVariantHandler) http.Handler {
	router := gin.Default()

	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "service": "product-service"})
	})

	api := router.Group("/api/v1")
	{
		public := api.Group("/public")
		{
			public.GET("/products/search", func(c *gin.Context) {
				c.JSON(200, gin.H{"success": true, "data": []interface{}{}})
			})
			public.GET("/categories", func(c *gin.Context) {
				c.JSON(200, gin.H{"success": true, "data": []interface{}{}})
			})

			// Product variants and add-ons (public read access)
			public.GET("/product/:product_id/variants", variantHandler.GetProductVariants)
			public.GET("/product/:product_id/addons", variantHandler.GetProductAddOns)
		}

		// Vendor routes (for demo, no auth middleware)
		vendor := api.Group("/vendor")
		{
			// Product variants management
			vendor.POST("/product/:product_id/variants", variantHandler.CreateVariant)
			vendor.PUT("/variants/:variant_id", variantHandler.UpdateVariant)
			vendor.DELETE("/variants/:variant_id", variantHandler.DeleteVariant)

			// Product add-ons management
			vendor.POST("/product/:product_id/addons", variantHandler.CreateAddOn)
			vendor.PUT("/addons/:addon_id", variantHandler.UpdateAddOn)
			vendor.DELETE("/addons/:addon_id", variantHandler.DeleteAddOn)
		}
	}

	return c.Handler(router)
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
