package config

import (
	"database/sql"
	"fmt"
	"os"
	"strconv"

	_ "github.com/lib/pq"
)

type Config struct {
	Environment string
	Port        string
	DatabaseURL string
	JWTSecret   string
	
	// Database config
	DBHost     string
	DBPort     int
	DBUser     string
	DBPassword string
	DBName     string
	DBSSLMode  string
	
	// External service URLs
	UserServiceURL           string
	ServiceCatalogServiceURL string
	PaymentServiceURL        string
	OrderServiceURL          string
	
	// File upload config
	UploadPath    string
	MaxFileSize   int64
	AllowedTypes  []string
	
	// Business rules
	DefaultCurrency      string
	MinProductPrice      float64
	MaxProductPrice      float64
	DefaultStockQuantity int
	LowStockThreshold    int
	
	// Redis config (for caching)
	RedisURL string
}

func Load() *Config {
	return &Config{
		Environment: getEnv("ENVIRONMENT", "development"),
		Port:        getEnv("PORT", "8010"),
		DatabaseURL: getEnv("DATABASE_URL", ""),
		JWTSecret:   getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		
		DBHost:     getEnv("DB_HOST", "localhost"),
		DBPort:     getEnvAsInt("DB_PORT", 5433),
		DBUser:     getEnv("DB_USER", "postgres"),
		DBPassword: getEnv("DB_PASSWORD", ""),
		DBName:     getEnv("DB_NAME", "dodo"),
		DBSSLMode:  getEnv("DB_SSL_MODE", "disable"),
		
		UserServiceURL:           getEnv("USER_SERVICE_URL", "http://localhost:8011"),
		ServiceCatalogServiceURL: getEnv("SERVICE_CATALOG_SERVICE_URL", "http://localhost:8002"),
		PaymentServiceURL:        getEnv("PAYMENT_SERVICE_URL", "http://localhost:8004"),
		OrderServiceURL:          getEnv("ORDER_SERVICE_URL", "http://localhost:8008"),
		
		UploadPath:   getEnv("UPLOAD_PATH", "./uploads/products"),
		MaxFileSize:  getEnvAsInt64("MAX_FILE_SIZE", 10*1024*1024), // 10MB
		AllowedTypes: []string{"image/jpeg", "image/png", "image/webp", "image/gif"},
		
		DefaultCurrency:      getEnv("DEFAULT_CURRENCY", "USD"),
		MinProductPrice:      getEnvAsFloat("MIN_PRODUCT_PRICE", 0.01),
		MaxProductPrice:      getEnvAsFloat("MAX_PRODUCT_PRICE", 100000.00),
		DefaultStockQuantity: getEnvAsInt("DEFAULT_STOCK_QUANTITY", 0),
		LowStockThreshold:    getEnvAsInt("LOW_STOCK_THRESHOLD", 10),
		
		RedisURL: getEnv("REDIS_URL", "redis://localhost:6379"),
	}
}

func InitDB(cfg *Config) (*sql.DB, error) {
	var dsn string
	
	if cfg.DatabaseURL != "" {
		dsn = cfg.DatabaseURL
	} else {
		dsn = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			cfg.DBHost, cfg.DBPort, cfg.DBUser, cfg.DBPassword, cfg.DBName, cfg.DBSSLMode)
	}
	
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}
	
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}
	
	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	
	return db, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsFloat(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}
