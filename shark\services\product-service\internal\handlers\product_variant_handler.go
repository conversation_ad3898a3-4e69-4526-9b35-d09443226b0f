package handlers

import (
	"net/http"

	"shark/product-service/internal/models"
	"shark/product-service/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type ProductVariantHandler struct {
	variantService *services.ProductVariantService
	validator      *validator.Validate
}

func NewProductVariantHandler(variantService *services.ProductVariantService) *ProductVariantHandler {
	return &ProductVariantHandler{
		variantService: variantService,
		validator:      validator.New(),
	}
}

// Product Variants
func (h *ProductVariantHandler) CreateVariant(c *gin.Context) {
	// For demo purposes, we'll use a dummy vendor ID
	// In production, this would come from JWT middleware
	vendorID := uuid.New()
	
	productIDStr := c.Param("product_id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid product ID format",
		})
		return
	}
	
	var req models.CreateProductVariantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	variant, err := h.variantService.CreateVariant(vendorID, productID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Data:    variant,
		Message: "Product variant created successfully",
	})
}

func (h *ProductVariantHandler) GetProductVariants(c *gin.Context) {
	productIDStr := c.Param("product_id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid product ID format",
		})
		return
	}
	
	variants, err := h.variantService.GetProductVariants(productID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get product variants",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    variants,
	})
}

func (h *ProductVariantHandler) UpdateVariant(c *gin.Context) {
	vendorID := uuid.New() // Demo vendor ID
	
	variantIDStr := c.Param("variant_id")
	variantID, err := uuid.Parse(variantIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid variant ID format",
		})
		return
	}
	
	var req models.UpdateProductVariantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	variant, err := h.variantService.UpdateVariant(vendorID, variantID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    variant,
		Message: "Product variant updated successfully",
	})
}

func (h *ProductVariantHandler) DeleteVariant(c *gin.Context) {
	vendorID := uuid.New() // Demo vendor ID
	
	variantIDStr := c.Param("variant_id")
	variantID, err := uuid.Parse(variantIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid variant ID format",
		})
		return
	}
	
	if err := h.variantService.DeleteVariant(vendorID, variantID); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Product variant deleted successfully",
	})
}

// Product Add-ons
func (h *ProductVariantHandler) CreateAddOn(c *gin.Context) {
	vendorID := uuid.New() // Demo vendor ID
	
	productIDStr := c.Param("product_id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid product ID format",
		})
		return
	}
	
	var req models.CreateProductAddOnRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	addOn, err := h.variantService.CreateAddOn(vendorID, productID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Data:    addOn,
		Message: "Product add-on created successfully",
	})
}

func (h *ProductVariantHandler) GetProductAddOns(c *gin.Context) {
	productIDStr := c.Param("product_id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid product ID format",
		})
		return
	}
	
	addOns, err := h.variantService.GetProductAddOns(productID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get product add-ons",
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    addOns,
	})
}

func (h *ProductVariantHandler) UpdateAddOn(c *gin.Context) {
	vendorID := uuid.New() // Demo vendor ID
	
	addOnIDStr := c.Param("addon_id")
	addOnID, err := uuid.Parse(addOnIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid add-on ID format",
		})
		return
	}
	
	var req models.UpdateProductAddOnRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Errors:  []string{err.Error()},
		})
		return
	}
	
	if err := h.validator.Struct(&req); err != nil {
		var errors []string
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, err.Error())
		}
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Validation failed",
			Errors:  errors,
		})
		return
	}
	
	addOn, err := h.variantService.UpdateAddOn(vendorID, addOnID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    addOn,
		Message: "Product add-on updated successfully",
	})
}

func (h *ProductVariantHandler) DeleteAddOn(c *gin.Context) {
	vendorID := uuid.New() // Demo vendor ID
	
	addOnIDStr := c.Param("addon_id")
	addOnID, err := uuid.Parse(addOnIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid add-on ID format",
		})
		return
	}
	
	if err := h.variantService.DeleteAddOn(vendorID, addOnID); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Product add-on deleted successfully",
	})
}
