package models

import (
	"time"

	"github.com/google/uuid"
)

// Product represents a physical product
type Product struct {
	ID          uuid.UUID `json:"id" db:"id"`
	VendorID    uuid.UUID `json:"vendor_id" db:"vendor_id"`
	CategoryID  uuid.UUID `json:"category_id" db:"category_id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	Price       float64   `json:"price" db:"price"`
	SKU         string    `json:"sku" db:"sku"`
	Images      []string  `json:"images" db:"images"`
	Tags        []string  `json:"tags" db:"tags"`
	Weight      *float64  `json:"weight,omitempty" db:"weight"`
	Dimensions  *string   `json:"dimensions,omitempty" db:"dimensions"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`

	// Related data
	Vendor    *VendorInfo      `json:"vendor,omitempty"`
	Category  *CategoryInfo    `json:"category,omitempty"`
	Inventory *Inventory       `json:"inventory,omitempty"`
	Variants  []ProductVariant `json:"variants,omitempty"`
	AddOns    []ProductAddOn   `json:"add_ons,omitempty"`
}

// ProductCategory represents product categories
type ProductCategory struct {
	ID          uuid.UUID  `json:"id" db:"id"`
	Name        string     `json:"name" db:"name"`
	Description string     `json:"description" db:"description"`
	Icon        string     `json:"icon" db:"icon"`
	ParentID    *uuid.UUID `json:"parent_id,omitempty" db:"parent_id"`
	IsActive    bool       `json:"is_active" db:"is_active"`
	SortOrder   int        `json:"sort_order" db:"sort_order"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`

	// Related data
	Parent       *ProductCategory  `json:"parent,omitempty"`
	Children     []ProductCategory `json:"children,omitempty"`
	ProductCount *int              `json:"product_count,omitempty"`
}

// Inventory represents product inventory
type Inventory struct {
	ID            uuid.UUID  `json:"id" db:"id"`
	ProductID     uuid.UUID  `json:"product_id" db:"product_id"`
	Quantity      int        `json:"quantity" db:"quantity"`
	ReservedQty   int        `json:"reserved_qty" db:"reserved_qty"`
	MinThreshold  int        `json:"min_threshold" db:"min_threshold"`
	MaxThreshold  int        `json:"max_threshold" db:"max_threshold"`
	RestockLevel  int        `json:"restock_level" db:"restock_level"`
	LastRestocked *time.Time `json:"last_restocked,omitempty" db:"last_restocked"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`
}

// InventoryTransaction represents inventory movements
type InventoryTransaction struct {
	ID            uuid.UUID  `json:"id" db:"id"`
	ProductID     uuid.UUID  `json:"product_id" db:"product_id"`
	Type          string     `json:"type" db:"type"` // in, out, reserved, released
	Quantity      int        `json:"quantity" db:"quantity"`
	PreviousQty   int        `json:"previous_qty" db:"previous_qty"`
	NewQty        int        `json:"new_qty" db:"new_qty"`
	Reason        string     `json:"reason" db:"reason"`
	ReferenceID   *uuid.UUID `json:"reference_id,omitempty" db:"reference_id"`
	ReferenceType *string    `json:"reference_type,omitempty" db:"reference_type"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
}

// Related info structs
type VendorInfo struct {
	ID        uuid.UUID `json:"id"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Email     string    `json:"email"`
	Phone     *string   `json:"phone,omitempty"`
}

type CategoryInfo struct {
	ID   uuid.UUID `json:"id"`
	Name string    `json:"name"`
	Icon string    `json:"icon"`
}

// Request/Response DTOs
type CreateProductRequest struct {
	CategoryID  uuid.UUID `json:"category_id" validate:"required"`
	Name        string    `json:"name" validate:"required,min=1,max=200"`
	Description string    `json:"description" validate:"required,min=1,max=2000"`
	Price       float64   `json:"price" validate:"required,min=0"`
	SKU         string    `json:"sku" validate:"required,min=1,max=50"`
	Images      []string  `json:"images,omitempty"`
	Tags        []string  `json:"tags,omitempty"`
	Weight      *float64  `json:"weight,omitempty" validate:"omitempty,min=0"`
	Dimensions  *string   `json:"dimensions,omitempty"`

	// Initial inventory
	InitialQuantity int `json:"initial_quantity" validate:"min=0"`
	MinThreshold    int `json:"min_threshold" validate:"min=0"`
	MaxThreshold    int `json:"max_threshold" validate:"min=0"`
	RestockLevel    int `json:"restock_level" validate:"min=0"`
}

type UpdateProductRequest struct {
	CategoryID  *uuid.UUID `json:"category_id,omitempty"`
	Name        *string    `json:"name,omitempty" validate:"omitempty,min=1,max=200"`
	Description *string    `json:"description,omitempty" validate:"omitempty,min=1,max=2000"`
	Price       *float64   `json:"price,omitempty" validate:"omitempty,min=0"`
	SKU         *string    `json:"sku,omitempty" validate:"omitempty,min=1,max=50"`
	Images      []string   `json:"images,omitempty"`
	Tags        []string   `json:"tags,omitempty"`
	Weight      *float64   `json:"weight,omitempty" validate:"omitempty,min=0"`
	Dimensions  *string    `json:"dimensions,omitempty"`
	IsActive    *bool      `json:"is_active,omitempty"`
}

type ProductSearchQuery struct {
	Query      string     `form:"q"`
	CategoryID *uuid.UUID `form:"category_id"`
	VendorID   *uuid.UUID `form:"vendor_id"`
	MinPrice   *float64   `form:"min_price" validate:"omitempty,min=0"`
	MaxPrice   *float64   `form:"max_price" validate:"omitempty,min=0"`
	Tags       []string   `form:"tags"`
	IsActive   *bool      `form:"is_active"`
	InStock    *bool      `form:"in_stock"`
	SortBy     string     `form:"sort_by" validate:"omitempty,oneof=name price created_at"`
	SortOrder  string     `form:"sort_order" validate:"omitempty,oneof=asc desc"`
	Page       int        `form:"page,default=1" validate:"min=1"`
	Limit      int        `form:"limit,default=20" validate:"min=1,max=100"`
}

type UpdateInventoryRequest struct {
	Type        string     `json:"type" validate:"required,oneof=in out reserved released"`
	Quantity    int        `json:"quantity" validate:"required,min=1"`
	Reason      string     `json:"reason" validate:"required,max=200"`
	ReferenceID *uuid.UUID `json:"reference_id,omitempty"`
}

type CreateCategoryRequest struct {
	Name        string     `json:"name" validate:"required,min=1,max=100"`
	Description string     `json:"description" validate:"required,min=1,max=500"`
	Icon        string     `json:"icon" validate:"required,max=50"`
	ParentID    *uuid.UUID `json:"parent_id,omitempty"`
	SortOrder   *int       `json:"sort_order,omitempty"`
}

type UpdateCategoryRequest struct {
	Name        *string    `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Description *string    `json:"description,omitempty" validate:"omitempty,min=1,max=500"`
	Icon        *string    `json:"icon,omitempty" validate:"omitempty,max=50"`
	ParentID    *uuid.UUID `json:"parent_id,omitempty"`
	IsActive    *bool      `json:"is_active,omitempty"`
	SortOrder   *int       `json:"sort_order,omitempty"`
}

// Inventory Transaction Types
const (
	InventoryTransactionIn       = "in"
	InventoryTransactionOut      = "out"
	InventoryTransactionReserved = "reserved"
	InventoryTransactionReleased = "released"
)

// API Response wrapper
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
	Errors  []string    `json:"errors,omitempty"`
}

// Pagination
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"total_pages"`
}

// Product Statistics
type ProductStats struct {
	TotalProducts       int64   `json:"total_products"`
	ActiveProducts      int64   `json:"active_products"`
	OutOfStockProducts  int64   `json:"out_of_stock_products"`
	LowStockProducts    int64   `json:"low_stock_products"`
	TotalCategories     int64   `json:"total_categories"`
	AvgProductPrice     float64 `json:"avg_product_price"`
	TotalInventoryValue float64 `json:"total_inventory_value"`
}

// ProductVariant represents different variants of a product (e.g., sizes, colors, configurations)
type ProductVariant struct {
	ID              uuid.UUID              `json:"id" db:"id"`
	ProductID       uuid.UUID              `json:"product_id" db:"product_id"`
	Name            string                 `json:"name" db:"name" validate:"required,min=1,max=100"`
	Description     string                 `json:"description" db:"description"`
	PriceAdjustment float64                `json:"price_adjustment" db:"price_adjustment"` // price difference from base
	SKUSuffix       *string                `json:"sku_suffix,omitempty" db:"sku_suffix"`
	Attributes      map[string]interface{} `json:"attributes,omitempty" db:"attributes"` // flexible attributes
	IsDefault       bool                   `json:"is_default" db:"is_default"`
	IsActive        bool                   `json:"is_active" db:"is_active"`
	SortOrder       int                    `json:"sort_order" db:"sort_order"`
	CreatedAt       time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at" db:"updated_at"`
}

// ProductAddOn represents additional products/services that can be added
type ProductAddOn struct {
	ID          uuid.UUID `json:"id" db:"id"`
	ProductID   uuid.UUID `json:"product_id" db:"product_id"`
	Name        string    `json:"name" db:"name" validate:"required,min=1,max=100"`
	Description string    `json:"description" db:"description" validate:"required,min=1,max=500"`
	Price       float64   `json:"price" db:"price" validate:"required,min=0"`
	IsRequired  bool      `json:"is_required" db:"is_required"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	MaxQuantity *int      `json:"max_quantity,omitempty" db:"max_quantity"` // null = unlimited
	SortOrder   int       `json:"sort_order" db:"sort_order"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Request DTOs for product variants and add-ons
type CreateProductVariantRequest struct {
	Name            string                 `json:"name" validate:"required,min=1,max=100"`
	Description     string                 `json:"description"`
	PriceAdjustment float64                `json:"price_adjustment"`
	SKUSuffix       *string                `json:"sku_suffix,omitempty"`
	Attributes      map[string]interface{} `json:"attributes,omitempty"`
	IsDefault       bool                   `json:"is_default"`
	SortOrder       *int                   `json:"sort_order,omitempty"`
}

type UpdateProductVariantRequest struct {
	Name            *string                `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Description     *string                `json:"description,omitempty"`
	PriceAdjustment *float64               `json:"price_adjustment,omitempty"`
	SKUSuffix       *string                `json:"sku_suffix,omitempty"`
	Attributes      map[string]interface{} `json:"attributes,omitempty"`
	IsDefault       *bool                  `json:"is_default,omitempty"`
	IsActive        *bool                  `json:"is_active,omitempty"`
	SortOrder       *int                   `json:"sort_order,omitempty"`
}

type CreateProductAddOnRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description string  `json:"description" validate:"required,min=1,max=500"`
	Price       float64 `json:"price" validate:"required,min=0"`
	IsRequired  bool    `json:"is_required"`
	MaxQuantity *int    `json:"max_quantity,omitempty"`
	SortOrder   *int    `json:"sort_order,omitempty"`
}

type UpdateProductAddOnRequest struct {
	Name        *string  `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Description *string  `json:"description,omitempty" validate:"omitempty,min=1,max=500"`
	Price       *float64 `json:"price,omitempty" validate:"omitempty,min=0"`
	IsRequired  *bool    `json:"is_required,omitempty"`
	IsActive    *bool    `json:"is_active,omitempty"`
	MaxQuantity *int     `json:"max_quantity,omitempty"`
	SortOrder   *int     `json:"sort_order,omitempty"`
}

// JWT Claims
type JWTClaims struct {
	UserID uuid.UUID `json:"user_id"`
	Email  string    `json:"email"`
	Roles  []string  `json:"roles"`
	Exp    int64     `json:"exp"`
	Iat    int64     `json:"iat"`
}
