package repository

import (
	"database/sql"
	"fmt"
	"shark/product-service/internal/models"
	"strings"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

type ProductRepository struct {
	db *sql.DB
}

func NewProductRepository(db *sql.DB) *ProductRepository {
	return &ProductRepository{db: db}
}

// CRUD Operations
func (r *ProductRepository) Create(product *models.Product) error {
	query := `
		INSERT INTO products (id, vendor_id, category_id, name, description, price, sku, 
		                     stock_quantity, images, tags, is_active, weight, dimensions)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		RETURNING created_at, updated_at`

	err := r.db.QueryRow(
		query,
		product.ID,
		product.VendorID,
		product.CategoryID,
		product.Name,
		product.Description,
		product.Price,
		product.SKU,
		product.StockQuantity,
		pq.Array(product.Images),
		pq.Array(product.Tags),
		product.IsActive,
		product.Weight,
		product.Dimensions,
	).Scan(&product.CreatedAt, &product.UpdatedAt)

	return err
}

func (r *ProductRepository) GetByID(id uuid.UUID) (*models.Product, error) {
	product := &models.Product{}
	query := `
		SELECT p.id, p.vendor_id, p.category_id, p.name, p.description, p.price, p.sku, 
		       p.stock_quantity, p.images, p.tags, p.is_active, p.weight, p.dimensions,
		       p.created_at, p.updated_at,
		       u.first_name, u.last_name, u.email,
		       pc.name as category_name, pc.icon as category_icon
		FROM products p
		JOIN users u ON p.vendor_id = u.id
		JOIN product_categories pc ON p.category_id = pc.id
		WHERE p.id = $1`

	var images, tags pq.StringArray
	var vendorFirstName, vendorLastName, vendorEmail, categoryName, categoryIcon string

	err := r.db.QueryRow(query, id).Scan(
		&product.ID,
		&product.VendorID,
		&product.CategoryID,
		&product.Name,
		&product.Description,
		&product.Price,
		&product.SKU,
		&product.StockQuantity,
		&images,
		&tags,
		&product.IsActive,
		&product.Weight,
		&product.Dimensions,
		&product.CreatedAt,
		&product.UpdatedAt,
		&vendorFirstName,
		&vendorLastName,
		&vendorEmail,
		&categoryName,
		&categoryIcon,
	)

	if err != nil {
		return nil, err
	}

	product.Images = []string(images)
	product.Tags = []string(tags)

	// Set vendor info
	product.Vendor = &models.VendorInfo{
		ID:        product.VendorID,
		FirstName: vendorFirstName,
		LastName:  vendorLastName,
		Email:     vendorEmail,
	}

	// Set category info
	product.Category = &models.CategoryInfo{
		ID:   product.CategoryID,
		Name: categoryName,
		Icon: categoryIcon,
	}

	return product, nil
}

func (r *ProductRepository) Update(product *models.Product) error {
	query := `
		UPDATE products 
		SET category_id = $2, name = $3, description = $4, price = $5, sku = $6,
		    stock_quantity = $7, images = $8, tags = $9, is_active = $10, 
		    weight = $11, dimensions = $12, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`

	err := r.db.QueryRow(
		query,
		product.ID,
		product.CategoryID,
		product.Name,
		product.Description,
		product.Price,
		product.SKU,
		product.StockQuantity,
		pq.Array(product.Images),
		pq.Array(product.Tags),
		product.IsActive,
		product.Weight,
		product.Dimensions,
	).Scan(&product.UpdatedAt)

	return err
}

func (r *ProductRepository) Delete(id uuid.UUID) error {
	query := `UPDATE products SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

func (r *ProductRepository) GetByVendorID(vendorID uuid.UUID, offset, limit int) ([]*models.Product, int64, error) {
	// Get total count
	var total int64
	countQuery := `SELECT COUNT(*) FROM products WHERE vendor_id = $1 AND is_active = true`
	err := r.db.QueryRow(countQuery, vendorID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// Get products
	query := `
		SELECT p.id, p.vendor_id, p.category_id, p.name, p.description, p.price, p.sku, 
		       p.stock_quantity, p.images, p.tags, p.is_active, p.weight, p.dimensions,
		       p.created_at, p.updated_at,
		       pc.name as category_name, pc.icon as category_icon
		FROM products p
		JOIN product_categories pc ON p.category_id = pc.id
		WHERE p.vendor_id = $1 AND p.is_active = true
		ORDER BY p.created_at DESC
		LIMIT $2 OFFSET $3`

	rows, err := r.db.Query(query, vendorID, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var products []*models.Product
	for rows.Next() {
		product := &models.Product{}
		var images, tags pq.StringArray
		var categoryName, categoryIcon string

		err := rows.Scan(
			&product.ID,
			&product.VendorID,
			&product.CategoryID,
			&product.Name,
			&product.Description,
			&product.Price,
			&product.SKU,
			&product.StockQuantity,
			&images,
			&tags,
			&product.IsActive,
			&product.Weight,
			&product.Dimensions,
			&product.CreatedAt,
			&product.UpdatedAt,
			&categoryName,
			&categoryIcon,
		)

		if err != nil {
			return nil, 0, err
		}

		product.Images = []string(images)
		product.Tags = []string(tags)
		product.Category = &models.CategoryInfo{
			ID:   product.CategoryID,
			Name: categoryName,
			Icon: categoryIcon,
		}

		products = append(products, product)
	}

	return products, total, nil
}

func (r *ProductRepository) GetByCategoryID(categoryID uuid.UUID, offset, limit int) ([]*models.Product, int64, error) {
	// Get total count
	var total int64
	countQuery := `SELECT COUNT(*) FROM products WHERE category_id = $1 AND is_active = true`
	err := r.db.QueryRow(countQuery, categoryID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// Get products
	query := `
		SELECT p.id, p.vendor_id, p.category_id, p.name, p.description, p.price, p.sku, 
		       p.stock_quantity, p.images, p.tags, p.is_active, p.weight, p.dimensions,
		       p.created_at, p.updated_at,
		       u.first_name, u.last_name, u.email
		FROM products p
		JOIN users u ON p.vendor_id = u.id
		WHERE p.category_id = $1 AND p.is_active = true
		ORDER BY p.created_at DESC
		LIMIT $2 OFFSET $3`

	rows, err := r.db.Query(query, categoryID, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var products []*models.Product
	for rows.Next() {
		product := &models.Product{}
		var images, tags pq.StringArray
		var vendorFirstName, vendorLastName, vendorEmail string

		err := rows.Scan(
			&product.ID,
			&product.VendorID,
			&product.CategoryID,
			&product.Name,
			&product.Description,
			&product.Price,
			&product.SKU,
			&product.StockQuantity,
			&images,
			&tags,
			&product.IsActive,
			&product.Weight,
			&product.Dimensions,
			&product.CreatedAt,
			&product.UpdatedAt,
			&vendorFirstName,
			&vendorLastName,
			&vendorEmail,
		)

		if err != nil {
			return nil, 0, err
		}

		product.Images = []string(images)
		product.Tags = []string(tags)
		product.Vendor = &models.VendorInfo{
			ID:        product.VendorID,
			FirstName: vendorFirstName,
			LastName:  vendorLastName,
			Email:     vendorEmail,
		}

		products = append(products, product)
	}

	return products, total, nil
}

// Multi-vendor support
func (r *ProductRepository) GetVendorProducts(productID uuid.UUID) ([]*models.VendorProduct, error) {
	query := `
		SELECT vp.id, vp.vendor_id, vp.product_id, vp.vendor_sku, vp.vendor_price, 
		       vp.stock_quantity, vp.min_order_quantity, vp.max_order_quantity, 
		       vp.is_active, vp.created_at, vp.updated_at,
		       u.first_name, u.last_name, u.email
		FROM vendor_products vp
		JOIN users u ON vp.vendor_id = u.id
		WHERE vp.product_id = $1 AND vp.is_active = true
		ORDER BY vp.vendor_price ASC`

	rows, err := r.db.Query(query, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var vendorProducts []*models.VendorProduct
	for rows.Next() {
		vp := &models.VendorProduct{}
		var vendorFirstName, vendorLastName, vendorEmail string

		err := rows.Scan(
			&vp.ID,
			&vp.VendorID,
			&vp.ProductID,
			&vp.VendorSKU,
			&vp.VendorPrice,
			&vp.StockQuantity,
			&vp.MinOrderQuantity,
			&vp.MaxOrderQuantity,
			&vp.IsActive,
			&vp.CreatedAt,
			&vp.UpdatedAt,
			&vendorFirstName,
			&vendorLastName,
			&vendorEmail,
		)

		if err != nil {
			return nil, err
		}

		vp.Vendor = &models.VendorInfo{
			ID:        vp.VendorID,
			FirstName: vendorFirstName,
			LastName:  vendorLastName,
			Email:     vendorEmail,
		}

		vendorProducts = append(vendorProducts, vp)
	}

	return vendorProducts, nil
}

func (r *ProductRepository) AddVendorProduct(vp *models.VendorProduct) error {
	query := `
		INSERT INTO vendor_products (id, vendor_id, product_id, vendor_sku, vendor_price, 
		                           stock_quantity, min_order_quantity, max_order_quantity, 
		                           is_active, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		RETURNING created_at, updated_at`

	err := r.db.QueryRow(
		query,
		vp.ID,
		vp.VendorID,
		vp.ProductID,
		vp.VendorSKU,
		vp.VendorPrice,
		vp.StockQuantity,
		vp.MinOrderQuantity,
		vp.MaxOrderQuantity,
		vp.IsActive,
	).Scan(&vp.CreatedAt, &vp.UpdatedAt)

	return err
}

func (r *ProductRepository) UpdateVendorProduct(vp *models.VendorProduct) error {
	query := `
		UPDATE vendor_products 
		SET vendor_sku = $3, vendor_price = $4, stock_quantity = $5, 
		    min_order_quantity = $6, max_order_quantity = $7, is_active = $8,
		    updated_at = CURRENT_TIMESTAMP
		WHERE vendor_id = $1 AND product_id = $2
		RETURNING updated_at`

	err := r.db.QueryRow(
		query,
		vp.VendorID,
		vp.ProductID,
		vp.VendorSKU,
		vp.VendorPrice,
		vp.StockQuantity,
		vp.MinOrderQuantity,
		vp.MaxOrderQuantity,
		vp.IsActive,
	).Scan(&vp.UpdatedAt)

	return err
}

func (r *ProductRepository) RemoveVendorProduct(vendorID, productID uuid.UUID) error {
	query := `UPDATE vendor_products SET is_active = false WHERE vendor_id = $1 AND product_id = $2`
	_, err := r.db.Exec(query, vendorID, productID)
	return err
}

// Search functionality
func (r *ProductRepository) Search(query *models.ProductSearchQuery) ([]*models.Product, int64, error) {
	// Build WHERE clause
	whereClause, args := r.buildSearchWhereClause(query)

	// Count total records
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT p.id) 
		FROM products p
		JOIN users u ON p.vendor_id = u.id
		JOIN product_categories pc ON p.category_id = pc.id
		LEFT JOIN vendor_products vp ON p.id = vp.product_id
		%s`, whereClause)

	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// Build ORDER BY clause
	orderBy := "p.created_at DESC"
	if query.SortBy != "" {
		direction := "ASC"
		if query.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("p.%s %s", query.SortBy, direction)
	}

	// Get products
	offset := (query.Page - 1) * query.Limit
	productQuery := fmt.Sprintf(`
		SELECT DISTINCT p.id, p.vendor_id, p.category_id, p.name, p.description, p.price, p.sku, 
		       p.stock_quantity, p.images, p.tags, p.is_active, p.weight, p.dimensions,
		       p.created_at, p.updated_at,
		       u.first_name, u.last_name, u.email,
		       pc.name as category_name, pc.icon as category_icon
		FROM products p
		JOIN users u ON p.vendor_id = u.id
		JOIN product_categories pc ON p.category_id = pc.id
		LEFT JOIN vendor_products vp ON p.id = vp.product_id
		%s 
		ORDER BY %s
		LIMIT $%d OFFSET $%d`, whereClause, orderBy, len(args)+1, len(args)+2)

	args = append(args, query.Limit, offset)

	rows, err := r.db.Query(productQuery, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var products []*models.Product
	for rows.Next() {
		product := &models.Product{}
		var images, tags pq.StringArray
		var vendorFirstName, vendorLastName, vendorEmail, categoryName, categoryIcon string

		err := rows.Scan(
			&product.ID,
			&product.VendorID,
			&product.CategoryID,
			&product.Name,
			&product.Description,
			&product.Price,
			&product.SKU,
			&product.StockQuantity,
			&images,
			&tags,
			&product.IsActive,
			&product.Weight,
			&product.Dimensions,
			&product.CreatedAt,
			&product.UpdatedAt,
			&vendorFirstName,
			&vendorLastName,
			&vendorEmail,
			&categoryName,
			&categoryIcon,
		)

		if err != nil {
			return nil, 0, err
		}

		product.Images = []string(images)
		product.Tags = []string(tags)
		product.Vendor = &models.VendorInfo{
			ID:        product.VendorID,
			FirstName: vendorFirstName,
			LastName:  vendorLastName,
			Email:     vendorEmail,
		}
		product.Category = &models.CategoryInfo{
			ID:   product.CategoryID,
			Name: categoryName,
			Icon: categoryIcon,
		}

		products = append(products, product)
	}

	return products, total, nil
}

// Helper methods
func (r *ProductRepository) buildSearchWhereClause(query *models.ProductSearchQuery) (string, []interface{}) {
	conditions := []string{"p.is_active = true"}
	args := []interface{}{}
	argIndex := 1

	if query.Name != "" {
		conditions = append(conditions, fmt.Sprintf("p.name ILIKE $%d", argIndex))
		args = append(args, "%"+query.Name+"%")
		argIndex++
	}

	if query.CategoryID != nil {
		conditions = append(conditions, fmt.Sprintf("p.category_id = $%d", argIndex))
		args = append(args, *query.CategoryID)
		argIndex++
	}

	if query.VendorID != nil {
		conditions = append(conditions, fmt.Sprintf("(p.vendor_id = $%d OR vp.vendor_id = $%d)", argIndex, argIndex))
		args = append(args, *query.VendorID)
		argIndex++
	}

	if query.MinPrice != nil {
		conditions = append(conditions, fmt.Sprintf("(p.price >= $%d OR vp.vendor_price >= $%d)", argIndex, argIndex))
		args = append(args, *query.MinPrice)
		argIndex++
	}

	if query.MaxPrice != nil {
		conditions = append(conditions, fmt.Sprintf("(p.price <= $%d OR vp.vendor_price <= $%d)", argIndex, argIndex))
		args = append(args, *query.MaxPrice)
		argIndex++
	}

	if len(query.Tags) > 0 {
		conditions = append(conditions, fmt.Sprintf("p.tags && $%d", argIndex))
		args = append(args, pq.Array(query.Tags))
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	return whereClause, args
}
