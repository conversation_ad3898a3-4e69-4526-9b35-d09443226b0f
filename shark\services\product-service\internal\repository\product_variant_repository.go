package repository

import (
	"database/sql"
	"encoding/json"
	"shark/product-service/internal/models"

	"github.com/google/uuid"
)

type ProductVariantRepository struct {
	db *sql.DB
}

func NewProductVariantRepository(db *sql.DB) *ProductVariantRepository {
	return &ProductVariantRepository{db: db}
}

// Product Variants
func (r *ProductVariantRepository) CreateVariant(variant *models.ProductVariant) error {
	attributesJSON, _ := json.Marshal(variant.Attributes)
	
	query := `
		INSERT INTO product_variants (id, product_id, name, description, price_adjustment, 
		                            sku_suffix, attributes, is_default, is_active, sort_order)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING created_at, updated_at`
	
	err := r.db.QueryRow(
		query,
		variant.ID,
		variant.ProductID,
		variant.Name,
		variant.Description,
		variant.PriceAdjustment,
		variant.SKUSuffix,
		attributesJSON,
		variant.IsDefault,
		variant.IsActive,
		variant.SortOrder,
	).Scan(&variant.CreatedAt, &variant.UpdatedAt)
	
	return err
}

func (r *ProductVariantRepository) GetVariantsByProductID(productID uuid.UUID) ([]*models.ProductVariant, error) {
	query := `
		SELECT id, product_id, name, description, price_adjustment, sku_suffix, 
		       attributes, is_default, is_active, sort_order, created_at, updated_at
		FROM product_variants 
		WHERE product_id = $1 AND is_active = true
		ORDER BY sort_order ASC, name ASC`
	
	rows, err := r.db.Query(query, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var variants []*models.ProductVariant
	for rows.Next() {
		variant := &models.ProductVariant{}
		var attributesJSON []byte
		
		err := rows.Scan(
			&variant.ID,
			&variant.ProductID,
			&variant.Name,
			&variant.Description,
			&variant.PriceAdjustment,
			&variant.SKUSuffix,
			&attributesJSON,
			&variant.IsDefault,
			&variant.IsActive,
			&variant.SortOrder,
			&variant.CreatedAt,
			&variant.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		
		if len(attributesJSON) > 0 {
			json.Unmarshal(attributesJSON, &variant.Attributes)
		}
		
		variants = append(variants, variant)
	}
	
	return variants, nil
}

func (r *ProductVariantRepository) GetVariantByID(id uuid.UUID) (*models.ProductVariant, error) {
	variant := &models.ProductVariant{}
	var attributesJSON []byte
	
	query := `
		SELECT id, product_id, name, description, price_adjustment, sku_suffix, 
		       attributes, is_default, is_active, sort_order, created_at, updated_at
		FROM product_variants WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&variant.ID,
		&variant.ProductID,
		&variant.Name,
		&variant.Description,
		&variant.PriceAdjustment,
		&variant.SKUSuffix,
		&attributesJSON,
		&variant.IsDefault,
		&variant.IsActive,
		&variant.SortOrder,
		&variant.CreatedAt,
		&variant.UpdatedAt,
	)
	
	if err != nil {
		return nil, err
	}
	
	if len(attributesJSON) > 0 {
		json.Unmarshal(attributesJSON, &variant.Attributes)
	}
	
	return variant, err
}

func (r *ProductVariantRepository) UpdateVariant(variant *models.ProductVariant) error {
	attributesJSON, _ := json.Marshal(variant.Attributes)
	
	query := `
		UPDATE product_variants 
		SET name = $2, description = $3, price_adjustment = $4, sku_suffix = $5, 
		    attributes = $6, is_default = $7, is_active = $8, sort_order = $9, 
		    updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`
	
	err := r.db.QueryRow(
		query,
		variant.ID,
		variant.Name,
		variant.Description,
		variant.PriceAdjustment,
		variant.SKUSuffix,
		attributesJSON,
		variant.IsDefault,
		variant.IsActive,
		variant.SortOrder,
	).Scan(&variant.UpdatedAt)
	
	return err
}

func (r *ProductVariantRepository) DeleteVariant(id uuid.UUID) error {
	query := `DELETE FROM product_variants WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

// Product Add-ons
func (r *ProductVariantRepository) CreateAddOn(addOn *models.ProductAddOn) error {
	query := `
		INSERT INTO product_addons (id, product_id, name, description, price, 
		                          is_required, is_active, max_quantity, sort_order)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING created_at, updated_at`
	
	err := r.db.QueryRow(
		query,
		addOn.ID,
		addOn.ProductID,
		addOn.Name,
		addOn.Description,
		addOn.Price,
		addOn.IsRequired,
		addOn.IsActive,
		addOn.MaxQuantity,
		addOn.SortOrder,
	).Scan(&addOn.CreatedAt, &addOn.UpdatedAt)
	
	return err
}

func (r *ProductVariantRepository) GetAddOnsByProductID(productID uuid.UUID) ([]*models.ProductAddOn, error) {
	query := `
		SELECT id, product_id, name, description, price, is_required, is_active, 
		       max_quantity, sort_order, created_at, updated_at
		FROM product_addons 
		WHERE product_id = $1 AND is_active = true
		ORDER BY sort_order ASC, name ASC`
	
	rows, err := r.db.Query(query, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var addOns []*models.ProductAddOn
	for rows.Next() {
		addOn := &models.ProductAddOn{}
		err := rows.Scan(
			&addOn.ID,
			&addOn.ProductID,
			&addOn.Name,
			&addOn.Description,
			&addOn.Price,
			&addOn.IsRequired,
			&addOn.IsActive,
			&addOn.MaxQuantity,
			&addOn.SortOrder,
			&addOn.CreatedAt,
			&addOn.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		addOns = append(addOns, addOn)
	}
	
	return addOns, nil
}

func (r *ProductVariantRepository) GetAddOnByID(id uuid.UUID) (*models.ProductAddOn, error) {
	addOn := &models.ProductAddOn{}
	query := `
		SELECT id, product_id, name, description, price, is_required, is_active, 
		       max_quantity, sort_order, created_at, updated_at
		FROM product_addons WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&addOn.ID,
		&addOn.ProductID,
		&addOn.Name,
		&addOn.Description,
		&addOn.Price,
		&addOn.IsRequired,
		&addOn.IsActive,
		&addOn.MaxQuantity,
		&addOn.SortOrder,
		&addOn.CreatedAt,
		&addOn.UpdatedAt,
	)
	
	return addOn, err
}

func (r *ProductVariantRepository) UpdateAddOn(addOn *models.ProductAddOn) error {
	query := `
		UPDATE product_addons 
		SET name = $2, description = $3, price = $4, is_required = $5, is_active = $6, 
		    max_quantity = $7, sort_order = $8, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`
	
	err := r.db.QueryRow(
		query,
		addOn.ID,
		addOn.Name,
		addOn.Description,
		addOn.Price,
		addOn.IsRequired,
		addOn.IsActive,
		addOn.MaxQuantity,
		addOn.SortOrder,
	).Scan(&addOn.UpdatedAt)
	
	return err
}

func (r *ProductVariantRepository) DeleteAddOn(id uuid.UUID) error {
	query := `DELETE FROM product_addons WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}
