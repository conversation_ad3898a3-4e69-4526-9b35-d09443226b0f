package services

import (
	"errors"
	"shark/product-service/internal/models"
	"shark/product-service/internal/repository"

	"github.com/google/uuid"
)

type ProductVariantService struct {
	variantRepo *repository.ProductVariantRepository
}

func NewProductVariantService(variantRepo *repository.ProductVariantRepository) *ProductVariantService {
	return &ProductVariantService{
		variantRepo: variantRepo,
	}
}

// Product Variants
func (s *ProductVariantService) CreateVariant(vendorID uuid.UUID, productID uuid.UUID, req *models.CreateProductVariantRequest) (*models.ProductVariant, error) {
	// Verify product ownership (assuming we have a GetByID method)
	// For now, we'll skip ownership verification since we don't have the product repo implemented

	// Set default sort order if not provided
	sortOrder := 0
	if req.SortOrder != nil {
		sortOrder = *req.SortOrder
	}

	variant := &models.ProductVariant{
		ID:              uuid.New(),
		ProductID:       productID,
		Name:            req.Name,
		Description:     req.Description,
		PriceAdjustment: req.PriceAdjustment,
		SKUSuffix:       req.SKUSuffix,
		Attributes:      req.Attributes,
		IsDefault:       req.IsDefault,
		IsActive:        true,
		SortOrder:       sortOrder,
	}

	if err := s.variantRepo.CreateVariant(variant); err != nil {
		return nil, err
	}

	return variant, nil
}

func (s *ProductVariantService) GetProductVariants(productID uuid.UUID) ([]*models.ProductVariant, error) {
	return s.variantRepo.GetVariantsByProductID(productID)
}

func (s *ProductVariantService) UpdateVariant(vendorID uuid.UUID, variantID uuid.UUID, req *models.UpdateProductVariantRequest) (*models.ProductVariant, error) {
	// Get existing variant
	variant, err := s.variantRepo.GetVariantByID(variantID)
	if err != nil {
		return nil, errors.New("variant not found")
	}

	// TODO: Verify product ownership through product service

	// Update fields if provided
	if req.Name != nil {
		variant.Name = *req.Name
	}
	if req.Description != nil {
		variant.Description = *req.Description
	}
	if req.PriceAdjustment != nil {
		variant.PriceAdjustment = *req.PriceAdjustment
	}
	if req.SKUSuffix != nil {
		variant.SKUSuffix = req.SKUSuffix
	}
	if req.Attributes != nil {
		variant.Attributes = req.Attributes
	}
	if req.IsDefault != nil {
		variant.IsDefault = *req.IsDefault
	}
	if req.IsActive != nil {
		variant.IsActive = *req.IsActive
	}
	if req.SortOrder != nil {
		variant.SortOrder = *req.SortOrder
	}

	if err := s.variantRepo.UpdateVariant(variant); err != nil {
		return nil, err
	}

	return variant, nil
}

func (s *ProductVariantService) DeleteVariant(vendorID uuid.UUID, variantID uuid.UUID) error {
	// Get existing variant
	_, err := s.variantRepo.GetVariantByID(variantID)
	if err != nil {
		return errors.New("variant not found")
	}

	// TODO: Verify product ownership through product service

	return s.variantRepo.DeleteVariant(variantID)
}

// Product Add-ons
func (s *ProductVariantService) CreateAddOn(vendorID uuid.UUID, productID uuid.UUID, req *models.CreateProductAddOnRequest) (*models.ProductAddOn, error) {
	// TODO: Verify product ownership through product service

	// Set default sort order if not provided
	sortOrder := 0
	if req.SortOrder != nil {
		sortOrder = *req.SortOrder
	}

	addOn := &models.ProductAddOn{
		ID:          uuid.New(),
		ProductID:   productID,
		Name:        req.Name,
		Description: req.Description,
		Price:       req.Price,
		IsRequired:  req.IsRequired,
		IsActive:    true,
		MaxQuantity: req.MaxQuantity,
		SortOrder:   sortOrder,
	}

	if err := s.variantRepo.CreateAddOn(addOn); err != nil {
		return nil, err
	}

	return addOn, nil
}

func (s *ProductVariantService) GetProductAddOns(productID uuid.UUID) ([]*models.ProductAddOn, error) {
	return s.variantRepo.GetAddOnsByProductID(productID)
}

func (s *ProductVariantService) UpdateAddOn(vendorID uuid.UUID, addOnID uuid.UUID, req *models.UpdateProductAddOnRequest) (*models.ProductAddOn, error) {
	// Get existing add-on
	addOn, err := s.variantRepo.GetAddOnByID(addOnID)
	if err != nil {
		return nil, errors.New("add-on not found")
	}

	// TODO: Verify product ownership through product service

	// Update fields if provided
	if req.Name != nil {
		addOn.Name = *req.Name
	}
	if req.Description != nil {
		addOn.Description = *req.Description
	}
	if req.Price != nil {
		addOn.Price = *req.Price
	}
	if req.IsRequired != nil {
		addOn.IsRequired = *req.IsRequired
	}
	if req.IsActive != nil {
		addOn.IsActive = *req.IsActive
	}
	if req.MaxQuantity != nil {
		addOn.MaxQuantity = req.MaxQuantity
	}
	if req.SortOrder != nil {
		addOn.SortOrder = *req.SortOrder
	}

	if err := s.variantRepo.UpdateAddOn(addOn); err != nil {
		return nil, err
	}

	return addOn, nil
}

func (s *ProductVariantService) DeleteAddOn(vendorID uuid.UUID, addOnID uuid.UUID) error {
	// Get existing add-on
	_, err := s.variantRepo.GetAddOnByID(addOnID)
	if err != nil {
		return errors.New("add-on not found")
	}

	// TODO: Verify product ownership through product service

	return s.variantRepo.DeleteAddOn(addOnID)
}
