package main

import (
	"log"
	"net/http"
	"os"

	"shark/service-catalog/internal/config"
	"shark/service-catalog/internal/handlers"
	"shark/service-catalog/internal/middleware"
	"shark/service-catalog/internal/repository"
	"shark/service-catalog/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Load configuration
	cfg := config.Load()

	// Initialize database connection
	db, err := config.InitDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize repositories
	serviceRepo := repository.NewServiceRepository(db)
	categoryRepo := repository.NewCategoryRepository(db)
	variantRepo := repository.NewVariantRepository(db)

	// Initialize services
	serviceService := services.NewServiceService(serviceRepo, categoryRepo, variantRepo)
	categoryService := services.NewCategoryService(categoryRepo)
	variantService := services.NewVariantService(variantRepo, serviceRepo)

	// Initialize handlers
	serviceHandler := handlers.NewServiceHandler(serviceService)
	categoryHandler := handlers.NewCategoryHandler(categoryService)
	variantHandler := handlers.NewVariantHandler(variantService)

	// Setup router
	router := setupRouter(serviceHandler, categoryHandler, variantHandler, cfg)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8002"
	}

	log.Printf("Service Catalog Service starting on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func setupRouter(serviceHandler *handlers.ServiceHandler, categoryHandler *handlers.CategoryHandler, variantHandler *handlers.VariantHandler, cfg *config.Config) http.Handler {
	// Set Gin mode
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// CORS middleware
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "service": "service-catalog"})
	})

	// API routes
	api := router.Group("/api/v1")
	{
		// Public routes (no authentication required)
		public := api.Group("/public")
		{
			// Categories (public read access)
			public.GET("/categories", categoryHandler.GetCategories)
			public.GET("/categories/tree", categoryHandler.GetCategoryTree)
			public.GET("/categories/root", categoryHandler.GetRootCategories)
			public.GET("/categories/:id", categoryHandler.GetCategory)
			public.GET("/categories/:id/subcategories", categoryHandler.GetSubcategories)

			// Services (public read access)
			public.GET("/services/search", serviceHandler.SearchServices)
			public.GET("/services/:id", serviceHandler.GetService)
			public.GET("/services/stats", serviceHandler.GetServiceStats)

			// Service variants and add-ons (public read access)
			public.GET("/service/:service_id/variants", variantHandler.GetServiceVariants)
			public.GET("/service/:service_id/addons", variantHandler.GetServiceAddOns)
		}

		// Protected routes (authentication required)
		protected := api.Group("")
		protected.Use(middleware.AuthMiddleware(cfg.JWTSecret))
		{
			// Vendor routes (vendor or admin access)
			vendor := protected.Group("/vendor")
			vendor.Use(middleware.VendorMiddleware())
			{
				// Service management
				vendor.POST("/services", serviceHandler.CreateService)
				vendor.GET("/services", serviceHandler.GetVendorServices)
				vendor.PUT("/services/:id", serviceHandler.UpdateService)
				vendor.DELETE("/services/:id", serviceHandler.DeleteService)

				// Service variants management
				vendor.POST("/service/:service_id/variants", variantHandler.CreateVariant)
				vendor.PUT("/variants/:variant_id", variantHandler.UpdateVariant)
				vendor.DELETE("/variants/:variant_id", variantHandler.DeleteVariant)

				// Service add-ons management
				vendor.POST("/service/:service_id/addons", variantHandler.CreateAddOn)
				vendor.PUT("/addons/:addon_id", variantHandler.UpdateAddOn)
				vendor.DELETE("/addons/:addon_id", variantHandler.DeleteAddOn)
			}

			// Admin routes (admin only)
			admin := protected.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				// Category management
				admin.POST("/categories", categoryHandler.CreateCategory)
				admin.PUT("/categories/:id", categoryHandler.UpdateCategory)
				admin.DELETE("/categories/:id", categoryHandler.DeleteCategory)

				// Advanced service management
				admin.GET("/services/all", serviceHandler.SearchServices)
			}
		}
	}

	return c.Handler(router)
}
