package models

import (
	"time"

	"github.com/google/uuid"
)

// Service represents a service offered by a vendor
type Service struct {
	ID          uuid.UUID `json:"id" db:"id"`
	VendorID    uuid.UUID `json:"vendor_id" db:"vendor_id"`
	CategoryID  uuid.UUID `json:"category_id" db:"category_id"`
	Name        string    `json:"name" db:"name" validate:"required,min=3,max=200"`
	Description string    `json:"description" db:"description" validate:"required,min=10,max=1000"`
	Price       float64   `json:"price" db:"price" validate:"required,min=0"`
	PriceType   string    `json:"price_type" db:"price_type" validate:"required,oneof=fixed hourly custom"`
	Duration    *int      `json:"duration,omitempty" db:"duration"` // in minutes
	Images      []string  `json:"images" db:"images"`
	Tags        []string  `json:"tags" db:"tags"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`

	// Related data (populated when needed)
	Category    *ServiceCategory `json:"category,omitempty"`
	Vendor      *VendorInfo      `json:"vendor,omitempty"`
	Reviews     []ServiceReview  `json:"reviews,omitempty"`
	AvgRating   *float64         `json:"avg_rating,omitempty"`
	ReviewCount *int             `json:"review_count,omitempty"`
	Variants    []ServiceVariant `json:"variants,omitempty"`
	AddOns      []ServiceAddOn   `json:"add_ons,omitempty"`
}

// ServiceCategory represents service categories and subcategories
type ServiceCategory struct {
	ID          uuid.UUID  `json:"id" db:"id"`
	Name        string     `json:"name" db:"name" validate:"required,min=2,max=100"`
	Description string     `json:"description" db:"description"`
	Icon        string     `json:"icon" db:"icon"`
	ParentID    *uuid.UUID `json:"parent_id,omitempty" db:"parent_id"`
	IsActive    bool       `json:"is_active" db:"is_active"`
	SortOrder   int        `json:"sort_order" db:"sort_order"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`

	// Related data
	Children     []ServiceCategory `json:"children,omitempty"`
	Parent       *ServiceCategory  `json:"parent,omitempty"`
	ServiceCount *int              `json:"service_count,omitempty"`
}

// VendorInfo represents basic vendor information
type VendorInfo struct {
	ID         uuid.UUID `json:"id"`
	FirstName  string    `json:"first_name"`
	LastName   string    `json:"last_name"`
	Email      string    `json:"email"`
	Phone      *string   `json:"phone,omitempty"`
	Avatar     *string   `json:"avatar,omitempty"`
	IsActive   bool      `json:"is_active"`
	IsVerified bool      `json:"is_verified"`
}

// ServiceReview represents a review for a service
type ServiceReview struct {
	ID        uuid.UUID `json:"id"`
	UserID    uuid.UUID `json:"user_id"`
	ServiceID uuid.UUID `json:"service_id"`
	Rating    int       `json:"rating"`
	Comment   *string   `json:"comment,omitempty"`
	CreatedAt time.Time `json:"created_at"`

	// User info
	UserName string `json:"user_name,omitempty"`
}

// Request/Response DTOs
type CreateServiceRequest struct {
	CategoryID  uuid.UUID `json:"category_id" validate:"required"`
	Name        string    `json:"name" validate:"required,min=3,max=200"`
	Description string    `json:"description" validate:"required,min=10,max=1000"`
	Price       float64   `json:"price" validate:"required,min=0"`
	PriceType   string    `json:"price_type" validate:"required,oneof=fixed hourly custom"`
	Duration    *int      `json:"duration,omitempty" validate:"omitempty,min=1"`
	Images      []string  `json:"images,omitempty"`
	Tags        []string  `json:"tags,omitempty"`
}

type UpdateServiceRequest struct {
	CategoryID  *uuid.UUID `json:"category_id,omitempty"`
	Name        *string    `json:"name,omitempty" validate:"omitempty,min=3,max=200"`
	Description *string    `json:"description,omitempty" validate:"omitempty,min=10,max=1000"`
	Price       *float64   `json:"price,omitempty" validate:"omitempty,min=0"`
	PriceType   *string    `json:"price_type,omitempty" validate:"omitempty,oneof=fixed hourly custom"`
	Duration    *int       `json:"duration,omitempty" validate:"omitempty,min=1"`
	Images      []string   `json:"images,omitempty"`
	Tags        []string   `json:"tags,omitempty"`
	IsActive    *bool      `json:"is_active,omitempty"`
}

type CreateCategoryRequest struct {
	Name        string     `json:"name" validate:"required,min=2,max=100"`
	Description string     `json:"description,omitempty"`
	Icon        string     `json:"icon,omitempty"`
	ParentID    *uuid.UUID `json:"parent_id,omitempty"`
	SortOrder   *int       `json:"sort_order,omitempty"`
}

type UpdateCategoryRequest struct {
	Name        *string    `json:"name,omitempty" validate:"omitempty,min=2,max=100"`
	Description *string    `json:"description,omitempty"`
	Icon        *string    `json:"icon,omitempty"`
	ParentID    *uuid.UUID `json:"parent_id,omitempty"`
	IsActive    *bool      `json:"is_active,omitempty"`
	SortOrder   *int       `json:"sort_order,omitempty"`
}

// Search and Filter DTOs
type ServiceSearchQuery struct {
	Query      string     `form:"q"`
	CategoryID *uuid.UUID `form:"category_id"`
	VendorID   *uuid.UUID `form:"vendor_id"`
	MinPrice   *float64   `form:"min_price" validate:"omitempty,min=0"`
	MaxPrice   *float64   `form:"max_price" validate:"omitempty,min=0"`
	PriceType  string     `form:"price_type" validate:"omitempty,oneof=fixed hourly custom"`
	Tags       []string   `form:"tags"`
	IsActive   *bool      `form:"is_active"`
	SortBy     string     `form:"sort_by" validate:"omitempty,oneof=name price created_at rating"`
	SortOrder  string     `form:"sort_order" validate:"omitempty,oneof=asc desc"`
	Page       int        `form:"page,default=1" validate:"min=1"`
	Limit      int        `form:"limit,default=20" validate:"min=1,max=100"`
}

type CategorySearchQuery struct {
	Query     string     `form:"q"`
	ParentID  *uuid.UUID `form:"parent_id"`
	IsActive  *bool      `form:"is_active"`
	SortBy    string     `form:"sort_by" validate:"omitempty,oneof=name sort_order created_at"`
	SortOrder string     `form:"sort_order" validate:"omitempty,oneof=asc desc"`
	Page      int        `form:"page,default=1" validate:"min=1"`
	Limit     int        `form:"limit,default=20" validate:"min=1,max=100"`
}

// API Response wrapper
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
	Errors  []string    `json:"errors,omitempty"`
}

// Pagination
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"total_pages"`
}

// Service Statistics
type ServiceStats struct {
	TotalServices     int64           `json:"total_services"`
	ActiveServices    int64           `json:"active_services"`
	TotalCategories   int64           `json:"total_categories"`
	TotalVendors      int64           `json:"total_vendors"`
	AvgServicePrice   float64         `json:"avg_service_price"`
	PopularCategories []CategoryStats `json:"popular_categories"`
}

type CategoryStats struct {
	CategoryID   uuid.UUID `json:"category_id"`
	CategoryName string    `json:"category_name"`
	ServiceCount int64     `json:"service_count"`
}

// ServiceVariant represents different variants of a service (e.g., Basic, Premium, Deluxe)
type ServiceVariant struct {
	ID          uuid.UUID `json:"id" db:"id"`
	ServiceID   uuid.UUID `json:"service_id" db:"service_id"`
	Name        string    `json:"name" db:"name" validate:"required,min=1,max=100"`
	Description string    `json:"description" db:"description" validate:"required,min=1,max=500"`
	Price       float64   `json:"price" db:"price" validate:"required,min=0"`
	PriceType   string    `json:"price_type" db:"price_type" validate:"required,oneof=fixed hourly custom"`
	Duration    *int      `json:"duration,omitempty" db:"duration"` // in minutes
	IsDefault   bool      `json:"is_default" db:"is_default"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	SortOrder   int       `json:"sort_order" db:"sort_order"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// ServiceAddOn represents additional services that can be added to a main service
type ServiceAddOn struct {
	ID          uuid.UUID `json:"id" db:"id"`
	ServiceID   uuid.UUID `json:"service_id" db:"service_id"`
	Name        string    `json:"name" db:"name" validate:"required,min=1,max=100"`
	Description string    `json:"description" db:"description" validate:"required,min=1,max=500"`
	Price       float64   `json:"price" db:"price" validate:"required,min=0"`
	PriceType   string    `json:"price_type" db:"price_type" validate:"required,oneof=fixed hourly custom"`
	Duration    *int      `json:"duration,omitempty" db:"duration"` // additional minutes
	IsRequired  bool      `json:"is_required" db:"is_required"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	MaxQuantity *int      `json:"max_quantity,omitempty" db:"max_quantity"` // null = unlimited
	SortOrder   int       `json:"sort_order" db:"sort_order"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Request DTOs for variants and add-ons
type CreateServiceVariantRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description string  `json:"description" validate:"required,min=1,max=500"`
	Price       float64 `json:"price" validate:"required,min=0"`
	PriceType   string  `json:"price_type" validate:"required,oneof=fixed hourly custom"`
	Duration    *int    `json:"duration,omitempty"`
	IsDefault   bool    `json:"is_default"`
	SortOrder   *int    `json:"sort_order,omitempty"`
}

type UpdateServiceVariantRequest struct {
	Name        *string  `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Description *string  `json:"description,omitempty" validate:"omitempty,min=1,max=500"`
	Price       *float64 `json:"price,omitempty" validate:"omitempty,min=0"`
	PriceType   *string  `json:"price_type,omitempty" validate:"omitempty,oneof=fixed hourly custom"`
	Duration    *int     `json:"duration,omitempty"`
	IsDefault   *bool    `json:"is_default,omitempty"`
	IsActive    *bool    `json:"is_active,omitempty"`
	SortOrder   *int     `json:"sort_order,omitempty"`
}

type CreateServiceAddOnRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description string  `json:"description" validate:"required,min=1,max=500"`
	Price       float64 `json:"price" validate:"required,min=0"`
	PriceType   string  `json:"price_type" validate:"required,oneof=fixed hourly custom"`
	Duration    *int    `json:"duration,omitempty"`
	IsRequired  bool    `json:"is_required"`
	MaxQuantity *int    `json:"max_quantity,omitempty"`
	SortOrder   *int    `json:"sort_order,omitempty"`
}

type UpdateServiceAddOnRequest struct {
	Name        *string  `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Description *string  `json:"description,omitempty" validate:"omitempty,min=1,max=500"`
	Price       *float64 `json:"price,omitempty" validate:"omitempty,min=0"`
	PriceType   *string  `json:"price_type,omitempty" validate:"omitempty,oneof=fixed hourly custom"`
	Duration    *int     `json:"duration,omitempty"`
	IsRequired  *bool    `json:"is_required,omitempty"`
	IsActive    *bool    `json:"is_active,omitempty"`
	MaxQuantity *int     `json:"max_quantity,omitempty"`
	SortOrder   *int     `json:"sort_order,omitempty"`
}

// JWT Claims (for authentication)
type JWTClaims struct {
	UserID uuid.UUID `json:"user_id"`
	Email  string    `json:"email"`
	Roles  []string  `json:"roles"`
	Exp    int64     `json:"exp"`
	Iat    int64     `json:"iat"`
}
