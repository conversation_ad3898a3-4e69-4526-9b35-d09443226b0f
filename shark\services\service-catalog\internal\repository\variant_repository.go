package repository

import (
	"database/sql"
	"shark/service-catalog/internal/models"

	"github.com/google/uuid"
)

type VariantRepository struct {
	db *sql.DB
}

func NewVariantRepository(db *sql.DB) *VariantRepository {
	return &VariantRepository{db: db}
}

// Service Variants
func (r *VariantRepository) CreateVariant(variant *models.ServiceVariant) error {
	query := `
		INSERT INTO service_variants (id, service_id, name, description, price, price_type, 
		                            duration, is_default, is_active, sort_order)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING created_at, updated_at`
	
	err := r.db.QueryRow(
		query,
		variant.ID,
		variant.ServiceID,
		variant.Name,
		variant.Description,
		variant.Price,
		variant.PriceType,
		variant.Duration,
		variant.IsDefault,
		variant.IsActive,
		variant.SortOrder,
	).Scan(&variant.CreatedAt, &variant.UpdatedAt)
	
	return err
}

func (r *VariantRepository) GetVariantsByServiceID(serviceID uuid.UUID) ([]*models.ServiceVariant, error) {
	query := `
		SELECT id, service_id, name, description, price, price_type, duration, 
		       is_default, is_active, sort_order, created_at, updated_at
		FROM service_variants 
		WHERE service_id = $1 AND is_active = true
		ORDER BY sort_order ASC, name ASC`
	
	rows, err := r.db.Query(query, serviceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var variants []*models.ServiceVariant
	for rows.Next() {
		variant := &models.ServiceVariant{}
		err := rows.Scan(
			&variant.ID,
			&variant.ServiceID,
			&variant.Name,
			&variant.Description,
			&variant.Price,
			&variant.PriceType,
			&variant.Duration,
			&variant.IsDefault,
			&variant.IsActive,
			&variant.SortOrder,
			&variant.CreatedAt,
			&variant.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		variants = append(variants, variant)
	}
	
	return variants, nil
}

func (r *VariantRepository) GetVariantByID(id uuid.UUID) (*models.ServiceVariant, error) {
	variant := &models.ServiceVariant{}
	query := `
		SELECT id, service_id, name, description, price, price_type, duration, 
		       is_default, is_active, sort_order, created_at, updated_at
		FROM service_variants WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&variant.ID,
		&variant.ServiceID,
		&variant.Name,
		&variant.Description,
		&variant.Price,
		&variant.PriceType,
		&variant.Duration,
		&variant.IsDefault,
		&variant.IsActive,
		&variant.SortOrder,
		&variant.CreatedAt,
		&variant.UpdatedAt,
	)
	
	return variant, err
}

func (r *VariantRepository) UpdateVariant(variant *models.ServiceVariant) error {
	query := `
		UPDATE service_variants 
		SET name = $2, description = $3, price = $4, price_type = $5, duration = $6,
		    is_default = $7, is_active = $8, sort_order = $9, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`
	
	err := r.db.QueryRow(
		query,
		variant.ID,
		variant.Name,
		variant.Description,
		variant.Price,
		variant.PriceType,
		variant.Duration,
		variant.IsDefault,
		variant.IsActive,
		variant.SortOrder,
	).Scan(&variant.UpdatedAt)
	
	return err
}

func (r *VariantRepository) DeleteVariant(id uuid.UUID) error {
	query := `DELETE FROM service_variants WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

// Service Add-ons
func (r *VariantRepository) CreateAddOn(addOn *models.ServiceAddOn) error {
	query := `
		INSERT INTO service_addons (id, service_id, name, description, price, price_type, 
		                          duration, is_required, is_active, max_quantity, sort_order)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING created_at, updated_at`
	
	err := r.db.QueryRow(
		query,
		addOn.ID,
		addOn.ServiceID,
		addOn.Name,
		addOn.Description,
		addOn.Price,
		addOn.PriceType,
		addOn.Duration,
		addOn.IsRequired,
		addOn.IsActive,
		addOn.MaxQuantity,
		addOn.SortOrder,
	).Scan(&addOn.CreatedAt, &addOn.UpdatedAt)
	
	return err
}

func (r *VariantRepository) GetAddOnsByServiceID(serviceID uuid.UUID) ([]*models.ServiceAddOn, error) {
	query := `
		SELECT id, service_id, name, description, price, price_type, duration, 
		       is_required, is_active, max_quantity, sort_order, created_at, updated_at
		FROM service_addons 
		WHERE service_id = $1 AND is_active = true
		ORDER BY sort_order ASC, name ASC`
	
	rows, err := r.db.Query(query, serviceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var addOns []*models.ServiceAddOn
	for rows.Next() {
		addOn := &models.ServiceAddOn{}
		err := rows.Scan(
			&addOn.ID,
			&addOn.ServiceID,
			&addOn.Name,
			&addOn.Description,
			&addOn.Price,
			&addOn.PriceType,
			&addOn.Duration,
			&addOn.IsRequired,
			&addOn.IsActive,
			&addOn.MaxQuantity,
			&addOn.SortOrder,
			&addOn.CreatedAt,
			&addOn.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		addOns = append(addOns, addOn)
	}
	
	return addOns, nil
}

func (r *VariantRepository) GetAddOnByID(id uuid.UUID) (*models.ServiceAddOn, error) {
	addOn := &models.ServiceAddOn{}
	query := `
		SELECT id, service_id, name, description, price, price_type, duration, 
		       is_required, is_active, max_quantity, sort_order, created_at, updated_at
		FROM service_addons WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&addOn.ID,
		&addOn.ServiceID,
		&addOn.Name,
		&addOn.Description,
		&addOn.Price,
		&addOn.PriceType,
		&addOn.Duration,
		&addOn.IsRequired,
		&addOn.IsActive,
		&addOn.MaxQuantity,
		&addOn.SortOrder,
		&addOn.CreatedAt,
		&addOn.UpdatedAt,
	)
	
	return addOn, err
}

func (r *VariantRepository) UpdateAddOn(addOn *models.ServiceAddOn) error {
	query := `
		UPDATE service_addons 
		SET name = $2, description = $3, price = $4, price_type = $5, duration = $6,
		    is_required = $7, is_active = $8, max_quantity = $9, sort_order = $10, 
		    updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`
	
	err := r.db.QueryRow(
		query,
		addOn.ID,
		addOn.Name,
		addOn.Description,
		addOn.Price,
		addOn.PriceType,
		addOn.Duration,
		addOn.IsRequired,
		addOn.IsActive,
		addOn.MaxQuantity,
		addOn.SortOrder,
	).Scan(&addOn.UpdatedAt)
	
	return err
}

func (r *VariantRepository) DeleteAddOn(id uuid.UUID) error {
	query := `DELETE FROM service_addons WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}
