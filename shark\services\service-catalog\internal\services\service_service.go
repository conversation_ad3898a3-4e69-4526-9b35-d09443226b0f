package services

import (
	"errors"
	"shark/service-catalog/internal/models"
	"shark/service-catalog/internal/repository"

	"github.com/google/uuid"
)

type ServiceService struct {
	serviceRepo  *repository.ServiceRepository
	categoryRepo *repository.CategoryRepository
	variantRepo  *repository.VariantRepository
}

func NewServiceService(serviceRepo *repository.ServiceRepository, categoryRepo *repository.CategoryRepository, variantRepo *repository.VariantRepository) *ServiceService {
	return &ServiceService{
		serviceRepo:  serviceRepo,
		categoryRepo: categoryRepo,
		variantRepo:  variantRepo,
	}
}

func (s *ServiceService) CreateService(vendorID uuid.UUID, req *models.CreateServiceRequest) (*models.Service, error) {
	// Validate category exists
	_, err := s.categoryRepo.GetByID(req.CategoryID)
	if err != nil {
		return nil, errors.New("invalid category")
	}

	// Create service
	service := &models.Service{
		ID:          uuid.New(),
		VendorID:    vendorID,
		CategoryID:  req.CategoryID,
		Name:        req.Name,
		Description: req.Description,
		Price:       req.Price,
		PriceType:   req.PriceType,
		Duration:    req.Duration,
		Images:      req.Images,
		Tags:        req.Tags,
		IsActive:    true,
	}

	if err := s.serviceRepo.Create(service); err != nil {
		return nil, err
	}

	return service, nil
}

func (s *ServiceService) GetService(id uuid.UUID) (*models.Service, error) {
	service, err := s.serviceRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Load category information
	category, err := s.categoryRepo.GetByID(service.CategoryID)
	if err == nil {
		service.Category = category
	}

	// Load variants
	variants, err := s.variantRepo.GetVariantsByServiceID(service.ID)
	if err == nil {
		// Convert []*ServiceVariant to []ServiceVariant
		variantSlice := make([]models.ServiceVariant, len(variants))
		for i, variant := range variants {
			variantSlice[i] = *variant
		}
		service.Variants = variantSlice
	}

	// Load add-ons
	addOns, err := s.variantRepo.GetAddOnsByServiceID(service.ID)
	if err == nil {
		// Convert []*ServiceAddOn to []ServiceAddOn
		addOnSlice := make([]models.ServiceAddOn, len(addOns))
		for i, addOn := range addOns {
			addOnSlice[i] = *addOn
		}
		service.AddOns = addOnSlice
	}

	return service, nil
}

func (s *ServiceService) UpdateService(vendorID, serviceID uuid.UUID, req *models.UpdateServiceRequest) (*models.Service, error) {
	// Get existing service
	service, err := s.serviceRepo.GetByID(serviceID)
	if err != nil {
		return nil, err
	}

	// Check ownership
	if service.VendorID != vendorID {
		return nil, errors.New("unauthorized: service belongs to another vendor")
	}

	// Validate category if provided
	if req.CategoryID != nil {
		_, err := s.categoryRepo.GetByID(*req.CategoryID)
		if err != nil {
			return nil, errors.New("invalid category")
		}
		service.CategoryID = *req.CategoryID
	}

	// Update fields if provided
	if req.Name != nil {
		service.Name = *req.Name
	}
	if req.Description != nil {
		service.Description = *req.Description
	}
	if req.Price != nil {
		service.Price = *req.Price
	}
	if req.PriceType != nil {
		service.PriceType = *req.PriceType
	}
	if req.Duration != nil {
		service.Duration = req.Duration
	}
	if req.Images != nil {
		service.Images = req.Images
	}
	if req.Tags != nil {
		service.Tags = req.Tags
	}
	if req.IsActive != nil {
		service.IsActive = *req.IsActive
	}

	if err := s.serviceRepo.Update(service); err != nil {
		return nil, err
	}

	return service, nil
}

func (s *ServiceService) DeleteService(vendorID, serviceID uuid.UUID) error {
	// Get existing service
	service, err := s.serviceRepo.GetByID(serviceID)
	if err != nil {
		return err
	}

	// Check ownership
	if service.VendorID != vendorID {
		return errors.New("unauthorized: service belongs to another vendor")
	}

	return s.serviceRepo.Delete(serviceID)
}

func (s *ServiceService) GetVendorServices(vendorID uuid.UUID, page, limit int) (*models.PaginatedResponse, error) {
	offset := (page - 1) * limit
	services, total, err := s.serviceRepo.GetByVendorID(vendorID, offset, limit)
	if err != nil {
		return nil, err
	}

	// Load category information for each service
	for _, service := range services {
		category, err := s.categoryRepo.GetByID(service.CategoryID)
		if err == nil {
			service.Category = category
		}
	}

	totalPages := int(total) / limit
	if int(total)%limit > 0 {
		totalPages++
	}

	return &models.PaginatedResponse{
		Data:       services,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (s *ServiceService) SearchServices(query *models.ServiceSearchQuery) (*models.PaginatedResponse, error) {
	services, total, err := s.serviceRepo.Search(query)
	if err != nil {
		return nil, err
	}

	// Load category information for each service
	for _, service := range services {
		category, err := s.categoryRepo.GetByID(service.CategoryID)
		if err == nil {
			service.Category = category
		}
	}

	totalPages := int(total) / query.Limit
	if int(total)%query.Limit > 0 {
		totalPages++
	}

	return &models.PaginatedResponse{
		Data:       services,
		Total:      total,
		Page:       query.Page,
		Limit:      query.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *ServiceService) GetServiceStats() (*models.ServiceStats, error) {
	stats, err := s.serviceRepo.GetStats()
	if err != nil {
		return nil, err
	}

	// Get total categories count
	categoryQuery := &models.CategorySearchQuery{
		IsActive: boolPtr(true),
		Page:     1,
		Limit:    1,
	}
	_, total, err := s.categoryRepo.GetAll(categoryQuery)
	if err == nil {
		stats.TotalCategories = total
	}

	return stats, nil
}

// Helper function
func boolPtr(b bool) *bool {
	return &b
}
