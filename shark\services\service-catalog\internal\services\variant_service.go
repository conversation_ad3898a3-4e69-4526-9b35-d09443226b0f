package services

import (
	"errors"
	"shark/service-catalog/internal/models"
	"shark/service-catalog/internal/repository"

	"github.com/google/uuid"
)

type VariantService struct {
	variantRepo *repository.VariantRepository
	serviceRepo *repository.ServiceRepository
}

func NewVariantService(variantRepo *repository.VariantRepository, serviceRepo *repository.ServiceRepository) *VariantService {
	return &VariantService{
		variantRepo: variantRepo,
		serviceRepo: serviceRepo,
	}
}

// Service Variants
func (s *VariantService) CreateVariant(vendorID uuid.UUID, serviceID uuid.UUID, req *models.CreateServiceVariantRequest) (*models.ServiceVariant, error) {
	// Verify service ownership
	service, err := s.serviceRepo.GetByID(serviceID)
	if err != nil {
		return nil, errors.New("service not found")
	}
	
	if service.VendorID != vendorID {
		return nil, errors.New("unauthorized: service belongs to another vendor")
	}
	
	// If this is set as default, unset other defaults
	if req.IsDefault {
		// This would require additional logic to unset other defaults
		// For now, we'll allow multiple defaults and handle in UI
	}
	
	// Set default sort order if not provided
	sortOrder := 0
	if req.SortOrder != nil {
		sortOrder = *req.SortOrder
	}
	
	variant := &models.ServiceVariant{
		ID:          uuid.New(),
		ServiceID:   serviceID,
		Name:        req.Name,
		Description: req.Description,
		Price:       req.Price,
		PriceType:   req.PriceType,
		Duration:    req.Duration,
		IsDefault:   req.IsDefault,
		IsActive:    true,
		SortOrder:   sortOrder,
	}
	
	if err := s.variantRepo.CreateVariant(variant); err != nil {
		return nil, err
	}
	
	return variant, nil
}

func (s *VariantService) GetServiceVariants(serviceID uuid.UUID) ([]*models.ServiceVariant, error) {
	return s.variantRepo.GetVariantsByServiceID(serviceID)
}

func (s *VariantService) UpdateVariant(vendorID uuid.UUID, variantID uuid.UUID, req *models.UpdateServiceVariantRequest) (*models.ServiceVariant, error) {
	// Get existing variant
	variant, err := s.variantRepo.GetVariantByID(variantID)
	if err != nil {
		return nil, errors.New("variant not found")
	}
	
	// Verify service ownership
	service, err := s.serviceRepo.GetByID(variant.ServiceID)
	if err != nil {
		return nil, errors.New("service not found")
	}
	
	if service.VendorID != vendorID {
		return nil, errors.New("unauthorized: variant belongs to another vendor")
	}
	
	// Update fields if provided
	if req.Name != nil {
		variant.Name = *req.Name
	}
	if req.Description != nil {
		variant.Description = *req.Description
	}
	if req.Price != nil {
		variant.Price = *req.Price
	}
	if req.PriceType != nil {
		variant.PriceType = *req.PriceType
	}
	if req.Duration != nil {
		variant.Duration = req.Duration
	}
	if req.IsDefault != nil {
		variant.IsDefault = *req.IsDefault
	}
	if req.IsActive != nil {
		variant.IsActive = *req.IsActive
	}
	if req.SortOrder != nil {
		variant.SortOrder = *req.SortOrder
	}
	
	if err := s.variantRepo.UpdateVariant(variant); err != nil {
		return nil, err
	}
	
	return variant, nil
}

func (s *VariantService) DeleteVariant(vendorID uuid.UUID, variantID uuid.UUID) error {
	// Get existing variant
	variant, err := s.variantRepo.GetVariantByID(variantID)
	if err != nil {
		return errors.New("variant not found")
	}
	
	// Verify service ownership
	service, err := s.serviceRepo.GetByID(variant.ServiceID)
	if err != nil {
		return errors.New("service not found")
	}
	
	if service.VendorID != vendorID {
		return errors.New("unauthorized: variant belongs to another vendor")
	}
	
	return s.variantRepo.DeleteVariant(variantID)
}

// Service Add-ons
func (s *VariantService) CreateAddOn(vendorID uuid.UUID, serviceID uuid.UUID, req *models.CreateServiceAddOnRequest) (*models.ServiceAddOn, error) {
	// Verify service ownership
	service, err := s.serviceRepo.GetByID(serviceID)
	if err != nil {
		return nil, errors.New("service not found")
	}
	
	if service.VendorID != vendorID {
		return nil, errors.New("unauthorized: service belongs to another vendor")
	}
	
	// Set default sort order if not provided
	sortOrder := 0
	if req.SortOrder != nil {
		sortOrder = *req.SortOrder
	}
	
	addOn := &models.ServiceAddOn{
		ID:          uuid.New(),
		ServiceID:   serviceID,
		Name:        req.Name,
		Description: req.Description,
		Price:       req.Price,
		PriceType:   req.PriceType,
		Duration:    req.Duration,
		IsRequired:  req.IsRequired,
		IsActive:    true,
		MaxQuantity: req.MaxQuantity,
		SortOrder:   sortOrder,
	}
	
	if err := s.variantRepo.CreateAddOn(addOn); err != nil {
		return nil, err
	}
	
	return addOn, nil
}

func (s *VariantService) GetServiceAddOns(serviceID uuid.UUID) ([]*models.ServiceAddOn, error) {
	return s.variantRepo.GetAddOnsByServiceID(serviceID)
}

func (s *VariantService) UpdateAddOn(vendorID uuid.UUID, addOnID uuid.UUID, req *models.UpdateServiceAddOnRequest) (*models.ServiceAddOn, error) {
	// Get existing add-on
	addOn, err := s.variantRepo.GetAddOnByID(addOnID)
	if err != nil {
		return nil, errors.New("add-on not found")
	}
	
	// Verify service ownership
	service, err := s.serviceRepo.GetByID(addOn.ServiceID)
	if err != nil {
		return nil, errors.New("service not found")
	}
	
	if service.VendorID != vendorID {
		return nil, errors.New("unauthorized: add-on belongs to another vendor")
	}
	
	// Update fields if provided
	if req.Name != nil {
		addOn.Name = *req.Name
	}
	if req.Description != nil {
		addOn.Description = *req.Description
	}
	if req.Price != nil {
		addOn.Price = *req.Price
	}
	if req.PriceType != nil {
		addOn.PriceType = *req.PriceType
	}
	if req.Duration != nil {
		addOn.Duration = req.Duration
	}
	if req.IsRequired != nil {
		addOn.IsRequired = *req.IsRequired
	}
	if req.IsActive != nil {
		addOn.IsActive = *req.IsActive
	}
	if req.MaxQuantity != nil {
		addOn.MaxQuantity = req.MaxQuantity
	}
	if req.SortOrder != nil {
		addOn.SortOrder = *req.SortOrder
	}
	
	if err := s.variantRepo.UpdateAddOn(addOn); err != nil {
		return nil, err
	}
	
	return addOn, nil
}

func (s *VariantService) DeleteAddOn(vendorID uuid.UUID, addOnID uuid.UUID) error {
	// Get existing add-on
	addOn, err := s.variantRepo.GetAddOnByID(addOnID)
	if err != nil {
		return errors.New("add-on not found")
	}
	
	// Verify service ownership
	service, err := s.serviceRepo.GetByID(addOn.ServiceID)
	if err != nil {
		return errors.New("service not found")
	}
	
	if service.VendorID != vendorID {
		return errors.New("unauthorized: add-on belongs to another vendor")
	}
	
	return s.variantRepo.DeleteAddOn(addOnID)
}
