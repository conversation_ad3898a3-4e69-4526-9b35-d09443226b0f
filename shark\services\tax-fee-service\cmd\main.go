package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"shark/tax-fee-service/internal/handlers"
	"shark/tax-fee-service/internal/repository"
	"shark/tax-fee-service/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
	_ "github.com/lib/pq"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Connect to database
	db, err := connectToDatabase()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize repositories
	taxRepo := repository.NewTaxRepository(db)
	feeRepo := repository.NewFeeRepository(db)

	// Initialize services
	taxFeeService := service.NewTaxFeeService(taxRepo, feeRepo)

	// Initialize handlers
	taxFeeHandler := handlers.NewTaxFeeHandler(taxFeeService)

	// Setup router
	router := setupRouter(taxFeeHandler)

	// Start server
	port := getEnv("PORT", "8082")
	log.Printf("Tax & Fee Service starting on port %s", port)
	log.Fatal(router.Run(":" + port))
}

func setupRouter(taxFeeHandler *handlers.TaxFeeHandler) *gin.Engine {
	// Set Gin mode
	if getEnv("GIN_MODE", "debug") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// CORS middleware
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})
	router.Use(func(ctx *gin.Context) {
		c.HandlerFunc(ctx.Writer, ctx.Request)
		ctx.Next()
	})

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "tax-fee-service",
		})
	})

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Tax & Fee calculation routes
		taxFee := v1.Group("/tax-fee")
		{
			// Calculation endpoints
			taxFee.POST("/calculate-tax", taxFeeHandler.CalculateTax)
			taxFee.POST("/calculate-fee", taxFeeHandler.CalculateFee)

			// Tax rule management
			taxRules := taxFee.Group("/tax-rules")
			{
				taxRules.GET("", taxFeeHandler.ListTaxRules)
				taxRules.POST("", taxFeeHandler.CreateTaxRule)
				taxRules.GET("/:id", taxFeeHandler.GetTaxRule)
				taxRules.PUT("/:id", taxFeeHandler.UpdateTaxRule)
				taxRules.DELETE("/:id", taxFeeHandler.DeleteTaxRule)
			}

			// Fee management
			fees := taxFee.Group("/fees")
			{
				fees.GET("", taxFeeHandler.ListFees)
				fees.POST("", taxFeeHandler.CreateFee)
				fees.GET("/:id", taxFeeHandler.GetFee)
				fees.PUT("/:id", taxFeeHandler.UpdateFee)
				fees.DELETE("/:id", taxFeeHandler.DeleteFee)
			}
		}
	}

	return router
}

func connectToDatabase() (*sql.DB, error) {
	host := getEnv("DB_HOST", "localhost")
	port := getEnv("DB_PORT", "5433")
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "")
	dbname := getEnv("DB_NAME", "dodo")
	sslmode := getEnv("DB_SSL_MODE", "disable")

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		host, port, user, password, dbname, sslmode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	log.Println("✅ Connected to database successfully")
	return db, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
