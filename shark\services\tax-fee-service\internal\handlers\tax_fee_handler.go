package handlers

import (
	"fmt"
	"net/http"
	"shark/tax-fee-service/internal/models"
	"shark/tax-fee-service/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type TaxFeeHandler struct {
	taxFeeService *service.TaxFeeService
}

func NewTaxFeeHandler(taxFeeService *service.TaxFeeService) *TaxFeeHandler {
	return &TaxFeeHandler{
		taxFeeService: taxFeeService,
	}
}

// Tax Calculation Endpoints

// POST /api/v1/tax-fee/calculate-tax
func (h *TaxFeeHandler) CalculateTax(c *gin.Context) {
	var req models.CalculateTaxRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.TaxCalculationResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	result, err := h.taxFeeService.CalculateTax(&req)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, models.TaxCalculationResponse{
			Success: false,
			Message: "Failed to calculate tax",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.TaxCalculationResponse{
		Success: true,
		Data:    result,
		Message: "Tax calculated successfully",
	})
}

// POST /api/v1/tax-fee/calculate-fee
func (h *TaxFeeHandler) CalculateFee(c *gin.Context) {
	var req models.CalculateFeeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.FeeCalculationResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	result, err := h.taxFeeService.CalculateFee(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.FeeCalculationResponse{
			Success: false,
			Message: "Failed to calculate fee",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.FeeCalculationResponse{
		Success: true,
		Data:    result,
		Message: "Fee calculated successfully",
	})
}

// Tax Rule Management Endpoints

// GET /api/v1/tax-fee/tax-rules
func (h *TaxFeeHandler) ListTaxRules(c *gin.Context) {
	var query models.TaxRuleSearchQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRulesResponse{
			Success: false,
			Message: "Invalid query parameters",
			Errors:  []string{err.Error()},
		})
		return
	}

	taxRules, total, err := h.taxFeeService.ListTaxRules(&query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.TaxRulesResponse{
			Success: false,
			Message: "Failed to list tax rules",
			Errors:  []string{err.Error()},
		})
		return
	}

	// Calculate pagination info
	totalPages := (total + query.Limit - 1) / query.Limit
	hasNext := query.Page < totalPages
	hasPrev := query.Page > 1

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    taxRules,
		"pagination": gin.H{
			"page":        query.Page,
			"limit":       query.Limit,
			"total":       total,
			"total_pages": totalPages,
			"has_next":    hasNext,
			"has_prev":    hasPrev,
		},
		"message": "Tax rules retrieved successfully",
	})
}

// POST /api/v1/tax-fee/tax-rules
func (h *TaxFeeHandler) CreateTaxRule(c *gin.Context) {
	var req models.CreateTaxRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRuleResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	taxRule, err := h.taxFeeService.CreateTaxRule(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRuleResponse{
			Success: false,
			Message: "Failed to create tax rule",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusCreated, models.TaxRuleResponse{
		Success: true,
		Data:    taxRule,
		Message: "Tax rule created successfully",
	})
}

// GET /api/v1/tax-fee/tax-rules/:id
func (h *TaxFeeHandler) GetTaxRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRuleResponse{
			Success: false,
			Message: "Invalid tax rule ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	taxRule, err := h.taxFeeService.GetTaxRule(id)
	if err != nil {
		c.JSON(http.StatusNotFound, models.TaxRuleResponse{
			Success: false,
			Message: "Tax rule not found",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.TaxRuleResponse{
		Success: true,
		Data:    taxRule,
		Message: "Tax rule retrieved successfully",
	})
}

// PUT /api/v1/tax-fee/tax-rules/:id
func (h *TaxFeeHandler) UpdateTaxRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRuleResponse{
			Success: false,
			Message: "Invalid tax rule ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.UpdateTaxRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRuleResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	taxRule, err := h.taxFeeService.UpdateTaxRule(id, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRuleResponse{
			Success: false,
			Message: "Failed to update tax rule",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.TaxRuleResponse{
		Success: true,
		Data:    taxRule,
		Message: "Tax rule updated successfully",
	})
}

// DELETE /api/v1/tax-fee/tax-rules/:id
func (h *TaxFeeHandler) DeleteTaxRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRuleResponse{
			Success: false,
			Message: "Invalid tax rule ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	err = h.taxFeeService.DeleteTaxRule(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.TaxRuleResponse{
			Success: false,
			Message: "Failed to delete tax rule",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Tax rule deleted successfully",
	})
}

// Fee Management Endpoints

// GET /api/v1/tax-fee/fees
func (h *TaxFeeHandler) ListFees(c *gin.Context) {
	var query models.FeeSearchQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, models.FeesResponse{
			Success: false,
			Message: "Invalid query parameters",
			Errors:  []string{err.Error()},
		})
		return
	}

	fees, total, err := h.taxFeeService.ListFees(&query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.FeesResponse{
			Success: false,
			Message: "Failed to list fees",
			Errors:  []string{err.Error()},
		})
		return
	}

	// Calculate pagination info
	totalPages := (total + query.Limit - 1) / query.Limit
	hasNext := query.Page < totalPages
	hasPrev := query.Page > 1

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fees,
		"pagination": gin.H{
			"page":        query.Page,
			"limit":       query.Limit,
			"total":       total,
			"total_pages": totalPages,
			"has_next":    hasNext,
			"has_prev":    hasPrev,
		},
		"message": "Fees retrieved successfully",
	})
}

// POST /api/v1/tax-fee/fees
func (h *TaxFeeHandler) CreateFee(c *gin.Context) {
	var req models.CreateFeeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.FeeResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	fee, err := h.taxFeeService.CreateFee(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.FeeResponse{
			Success: false,
			Message: "Failed to create fee",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusCreated, models.FeeResponse{
		Success: true,
		Data:    fee,
		Message: "Fee created successfully",
	})
}

// GET /api/v1/tax-fee/fees/:id
func (h *TaxFeeHandler) GetFee(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.FeeResponse{
			Success: false,
			Message: "Invalid fee ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	fee, err := h.taxFeeService.GetFee(id)
	if err != nil {
		c.JSON(http.StatusNotFound, models.FeeResponse{
			Success: false,
			Message: "Fee not found",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.FeeResponse{
		Success: true,
		Data:    fee,
		Message: "Fee retrieved successfully",
	})
}

// PUT /api/v1/tax-fee/fees/:id
func (h *TaxFeeHandler) UpdateFee(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.FeeResponse{
			Success: false,
			Message: "Invalid fee ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	var req models.UpdateFeeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.FeeResponse{
			Success: false,
			Message: "Invalid request",
			Errors:  []string{err.Error()},
		})
		return
	}

	fee, err := h.taxFeeService.UpdateFee(id, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.FeeResponse{
			Success: false,
			Message: "Failed to update fee",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, models.FeeResponse{
		Success: true,
		Data:    fee,
		Message: "Fee updated successfully",
	})
}

// DELETE /api/v1/tax-fee/fees/:id
func (h *TaxFeeHandler) DeleteFee(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.FeeResponse{
			Success: false,
			Message: "Invalid fee ID",
			Errors:  []string{err.Error()},
		})
		return
	}

	err = h.taxFeeService.DeleteFee(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.FeeResponse{
			Success: false,
			Message: "Failed to delete fee",
			Errors:  []string{err.Error()},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Fee deleted successfully",
	})
}

// Helper methods
func (h *TaxFeeHandler) getUserID(c *gin.Context) (uuid.UUID, error) {
	// Extract user ID from JWT token or session
	// This is a simplified implementation
	userIDStr := c.GetHeader("X-User-ID")
	if userIDStr == "" {
		// Try to get from query parameter for testing
		userIDStr = c.Query("user_id")
	}

	if userIDStr == "" {
		return uuid.Nil, fmt.Errorf("user ID not found")
	}

	return uuid.Parse(userIDStr)
}

func (h *TaxFeeHandler) validatePagination(c *gin.Context) (int, int, error) {
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	return page, limit, nil
}
