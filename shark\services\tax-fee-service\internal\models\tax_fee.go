package models

import (
	"time"

	"github.com/google/uuid"
)

// TaxRule represents a tax calculation rule
type TaxRule struct {
	ID         uuid.UUID `json:"id" db:"id"`
	Name       string    `json:"name" db:"name"`
	TaxType    string    `json:"tax_type" db:"tax_type"`
	Rate       float64   `json:"rate" db:"rate"`
	IsPercentage bool    `json:"is_percentage" db:"is_percentage"`
	Country    string    `json:"country" db:"country"`
	State      string    `json:"state" db:"state"`
	City       string    `json:"city" db:"city"`
	PostalCode string    `json:"postal_code" db:"postal_code"`
	IsActive   bool      `json:"is_active" db:"is_active"`
	Priority   int       `json:"priority" db:"priority"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
}

// Fee represents a fee calculation rule
type Fee struct {
	ID               uuid.UUID  `json:"id" db:"id"`
	Name             string     `json:"name" db:"name"`
	FeeType          string     `json:"fee_type" db:"fee_type"`
	CalculationType  string     `json:"calculation_type" db:"calculation_type"`
	Amount           float64    `json:"amount" db:"amount"`
	MinimumAmount    float64    `json:"minimum_amount" db:"minimum_amount"`
	MaximumAmount    *float64   `json:"maximum_amount,omitempty" db:"maximum_amount"`
	ApplicableTo     string     `json:"applicable_to" db:"applicable_to"`
	IsActive         bool       `json:"is_active" db:"is_active"`
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at" db:"updated_at"`

	// Related data
	Tiers []FeeTier `json:"tiers,omitempty"`
}

// FeeTier represents tiered fee calculation
type FeeTier struct {
	ID        uuid.UUID `json:"id" db:"id"`
	FeeID     uuid.UUID `json:"fee_id" db:"fee_id"`
	MinAmount float64   `json:"min_amount" db:"min_amount"`
	MaxAmount *float64  `json:"max_amount,omitempty" db:"max_amount"`
	TierFee   float64   `json:"tier_fee" db:"tier_fee"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// Calculation results
type TaxCalculationResult struct {
	TotalTax  float64         `json:"total_tax"`
	Breakdown []TaxBreakdown  `json:"breakdown"`
}

type FeeCalculationResult struct {
	TotalFees float64         `json:"total_fees"`
	Breakdown []FeeBreakdown  `json:"breakdown"`
}

type TaxBreakdown struct {
	Name   string  `json:"name"`
	Type   string  `json:"type"`
	Rate   float64 `json:"rate"`
	Amount float64 `json:"amount"`
}

type FeeBreakdown struct {
	Name   string  `json:"name"`
	Type   string  `json:"type"`
	Amount float64 `json:"amount"`
}

// Request DTOs
type CalculateTaxRequest struct {
	Subtotal   float64 `json:"subtotal" validate:"required,min=0"`
	Country    string  `json:"country" validate:"required,len=2"`
	State      string  `json:"state,omitempty"`
	City       string  `json:"city,omitempty"`
	PostalCode string  `json:"postal_code,omitempty"`
}

type CalculateFeeRequest struct {
	Subtotal     float64 `json:"subtotal" validate:"required,min=0"`
	ApplicableTo string  `json:"applicable_to" validate:"required,oneof=all products services orders bookings"`
}

type CreateTaxRuleRequest struct {
	Name         string  `json:"name" validate:"required,min=1,max=100"`
	TaxType      string  `json:"tax_type" validate:"required,min=1,max=50"`
	Rate         float64 `json:"rate" validate:"required,min=0,max=1"`
	IsPercentage bool    `json:"is_percentage"`
	Country      string  `json:"country" validate:"required,len=2"`
	State        string  `json:"state,omitempty"`
	City         string  `json:"city,omitempty"`
	PostalCode   string  `json:"postal_code,omitempty"`
	Priority     int     `json:"priority" validate:"min=0"`
}

type UpdateTaxRuleRequest struct {
	Name         *string  `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	TaxType      *string  `json:"tax_type,omitempty" validate:"omitempty,min=1,max=50"`
	Rate         *float64 `json:"rate,omitempty" validate:"omitempty,min=0,max=1"`
	IsPercentage *bool    `json:"is_percentage,omitempty"`
	State        *string  `json:"state,omitempty"`
	City         *string  `json:"city,omitempty"`
	PostalCode   *string  `json:"postal_code,omitempty"`
	Priority     *int     `json:"priority,omitempty" validate:"omitempty,min=0"`
	IsActive     *bool    `json:"is_active,omitempty"`
}

type CreateFeeRequest struct {
	Name            string   `json:"name" validate:"required,min=1,max=100"`
	FeeType         string   `json:"fee_type" validate:"required,min=1,max=50"`
	CalculationType string   `json:"calculation_type" validate:"required,oneof=fixed percentage tiered"`
	Amount          float64  `json:"amount" validate:"required,min=0"`
	MinimumAmount   float64  `json:"minimum_amount" validate:"min=0"`
	MaximumAmount   *float64 `json:"maximum_amount,omitempty" validate:"omitempty,min=0"`
	ApplicableTo    string   `json:"applicable_to" validate:"required,oneof=all products services orders bookings"`
	Tiers           []CreateFeeTierRequest `json:"tiers,omitempty"`
}

type UpdateFeeRequest struct {
	Name            *string  `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	FeeType         *string  `json:"fee_type,omitempty" validate:"omitempty,min=1,max=50"`
	Amount          *float64 `json:"amount,omitempty" validate:"omitempty,min=0"`
	MinimumAmount   *float64 `json:"minimum_amount,omitempty" validate:"omitempty,min=0"`
	MaximumAmount   *float64 `json:"maximum_amount,omitempty" validate:"omitempty,min=0"`
	ApplicableTo    *string  `json:"applicable_to,omitempty" validate:"omitempty,oneof=all products services orders bookings"`
	IsActive        *bool    `json:"is_active,omitempty"`
}

type CreateFeeTierRequest struct {
	MinAmount float64  `json:"min_amount" validate:"required,min=0"`
	MaxAmount *float64 `json:"max_amount,omitempty" validate:"omitempty,min=0"`
	TierFee   float64  `json:"tier_fee" validate:"required,min=0"`
}

// Response DTOs
type TaxRuleResponse struct {
	Success bool     `json:"success"`
	Data    *TaxRule `json:"data,omitempty"`
	Message string   `json:"message,omitempty"`
	Errors  []string `json:"errors,omitempty"`
}

type TaxRulesResponse struct {
	Success bool       `json:"success"`
	Data    []*TaxRule `json:"data,omitempty"`
	Message string     `json:"message,omitempty"`
	Errors  []string   `json:"errors,omitempty"`
}

type FeeResponse struct {
	Success bool   `json:"success"`
	Data    *Fee   `json:"data,omitempty"`
	Message string `json:"message,omitempty"`
	Errors  []string `json:"errors,omitempty"`
}

type FeesResponse struct {
	Success bool   `json:"success"`
	Data    []*Fee `json:"data,omitempty"`
	Message string `json:"message,omitempty"`
	Errors  []string `json:"errors,omitempty"`
}

type TaxCalculationResponse struct {
	Success bool                  `json:"success"`
	Data    *TaxCalculationResult `json:"data,omitempty"`
	Message string                `json:"message,omitempty"`
	Errors  []string              `json:"errors,omitempty"`
}

type FeeCalculationResponse struct {
	Success bool                  `json:"success"`
	Data    *FeeCalculationResult `json:"data,omitempty"`
	Message string                `json:"message,omitempty"`
	Errors  []string              `json:"errors,omitempty"`
}

// Search and filter
type TaxRuleSearchQuery struct {
	Name     string `form:"name"`
	TaxType  string `form:"tax_type"`
	Country  string `form:"country"`
	State    string `form:"state"`
	IsActive *bool  `form:"is_active"`
	SortBy   string `form:"sort_by" validate:"omitempty,oneof=name tax_type rate priority created_at"`
	SortOrder string `form:"sort_order" validate:"omitempty,oneof=asc desc"`
	Page     int    `form:"page,default=1" validate:"min=1"`
	Limit    int    `form:"limit,default=20" validate:"min=1,max=100"`
}

type FeeSearchQuery struct {
	Name            string `form:"name"`
	FeeType         string `form:"fee_type"`
	CalculationType string `form:"calculation_type"`
	ApplicableTo    string `form:"applicable_to"`
	IsActive        *bool  `form:"is_active"`
	SortBy          string `form:"sort_by" validate:"omitempty,oneof=name fee_type amount created_at"`
	SortOrder       string `form:"sort_order" validate:"omitempty,oneof=asc desc"`
	Page            int    `form:"page,default=1" validate:"min=1"`
	Limit           int    `form:"limit,default=20" validate:"min=1,max=100"`
}

// Constants
const (
	CalculationTypeFixed      = "fixed"
	CalculationTypePercentage = "percentage"
	CalculationTypeTiered     = "tiered"
)

const (
	ApplicableToAll      = "all"
	ApplicableToProducts = "products"
	ApplicableToServices = "services"
	ApplicableToOrders   = "orders"
	ApplicableToBookings = "bookings"
)

const (
	TaxTypeSales = "sales_tax"
	TaxTypeVAT   = "vat"
	TaxTypeGST   = "gst"
	TaxTypeHST   = "hst"
)

const (
	FeeTypeProcessing   = "processing"
	FeeTypeService      = "service"
	FeeTypeConvenience  = "convenience"
	FeeTypeTransaction  = "transaction"
	FeeTypeHandling     = "handling"
)

// Validation constants
const (
	MaxTaxRate     = 1.0    // 100%
	MaxFeeAmount   = 1000.0 // $1000
	MaxFeeTiers    = 10
	MaxTaxRules    = 100
	MaxFees        = 50
)
