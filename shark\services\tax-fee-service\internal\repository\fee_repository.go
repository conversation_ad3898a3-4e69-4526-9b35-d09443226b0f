package repository

import (
	"database/sql"
	"fmt"
	"shark/tax-fee-service/internal/models"
	"strings"

	"github.com/google/uuid"
)

type FeeRepository struct {
	db *sql.DB
}

func NewFeeRepository(db *sql.DB) *FeeRepository {
	return &FeeRepository{db: db}
}

// CRUD Operations
func (r *FeeRepository) Create(fee *models.Fee) error {
	tx, err := r.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Insert fee
	query := `
		INSERT INTO fees (id, name, fee_type, calculation_type, amount, minimum_amount, 
		                 maximum_amount, applicable_to, is_active, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		RETURNING created_at, updated_at`

	err = tx.QueryRow(
		query,
		fee.ID,
		fee.Name,
		fee.FeeType,
		fee.CalculationType,
		fee.Amount,
		fee.MinimumAmount,
		fee.MaximumAmount,
		fee.ApplicableTo,
		fee.IsActive,
	).Scan(&fee.CreatedAt, &fee.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create fee: %w", err)
	}

	// Insert fee tiers if applicable
	if fee.CalculationType == models.CalculationTypeTiered && len(fee.Tiers) > 0 {
		for i := range fee.Tiers {
			tier := &fee.Tiers[i]
			tier.FeeID = fee.ID

			_, err = tx.Exec(`
				INSERT INTO fee_tiers (id, fee_id, min_amount, max_amount, tier_fee, created_at)
				VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)`,
				tier.ID, tier.FeeID, tier.MinAmount, tier.MaxAmount, tier.TierFee)

			if err != nil {
				return fmt.Errorf("failed to create fee tier: %w", err)
			}
		}
	}

	return tx.Commit()
}

func (r *FeeRepository) GetByID(id uuid.UUID) (*models.Fee, error) {
	fee := &models.Fee{}
	query := `
		SELECT id, name, fee_type, calculation_type, amount, minimum_amount, 
		       maximum_amount, applicable_to, is_active, created_at, updated_at
		FROM fees WHERE id = $1`

	err := r.db.QueryRow(query, id).Scan(
		&fee.ID,
		&fee.Name,
		&fee.FeeType,
		&fee.CalculationType,
		&fee.Amount,
		&fee.MinimumAmount,
		&fee.MaximumAmount,
		&fee.ApplicableTo,
		&fee.IsActive,
		&fee.CreatedAt,
		&fee.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, fmt.Errorf("fee not found")
	}
	if err != nil {
		return nil, err
	}

	// Load fee tiers if applicable
	if fee.CalculationType == models.CalculationTypeTiered {
		tiers, err := r.getFeeTiers(fee.ID)
		if err != nil {
			return nil, err
		}
		fee.Tiers = tiers
	}

	return fee, nil
}

func (r *FeeRepository) List(query *models.FeeSearchQuery) ([]*models.Fee, int, error) {
	// Build WHERE clause
	whereClause, args := r.buildWhereClause(query)

	// Count total records
	countQuery := "SELECT COUNT(*) FROM fees" + whereClause
	var total int
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count fees: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "created_at DESC"
	if query.SortBy != "" {
		direction := "ASC"
		if query.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("%s %s", query.SortBy, direction)
	}

	// Calculate offset
	offset := (query.Page - 1) * query.Limit

	// Main query
	mainQuery := fmt.Sprintf(`
		SELECT id, name, fee_type, calculation_type, amount, minimum_amount, 
		       maximum_amount, applicable_to, is_active, created_at, updated_at
		FROM fees%s ORDER BY %s LIMIT $%d OFFSET $%d`,
		whereClause, orderBy, len(args)+1, len(args)+2)

	args = append(args, query.Limit, offset)

	rows, err := r.db.Query(mainQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list fees: %w", err)
	}
	defer rows.Close()

	var fees []*models.Fee
	for rows.Next() {
		fee := &models.Fee{}
		err := rows.Scan(
			&fee.ID,
			&fee.Name,
			&fee.FeeType,
			&fee.CalculationType,
			&fee.Amount,
			&fee.MinimumAmount,
			&fee.MaximumAmount,
			&fee.ApplicableTo,
			&fee.IsActive,
			&fee.CreatedAt,
			&fee.UpdatedAt,
		)

		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan fee: %w", err)
		}

		// Load fee tiers if applicable
		if fee.CalculationType == models.CalculationTypeTiered {
			tiers, err := r.getFeeTiers(fee.ID)
			if err != nil {
				return nil, 0, err
			}
			fee.Tiers = tiers
		}

		fees = append(fees, fee)
	}

	return fees, total, nil
}

func (r *FeeRepository) Update(id uuid.UUID, updates *models.UpdateFeeRequest) error {
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if updates.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *updates.Name)
		argIndex++
	}

	if updates.FeeType != nil {
		setParts = append(setParts, fmt.Sprintf("fee_type = $%d", argIndex))
		args = append(args, *updates.FeeType)
		argIndex++
	}

	if updates.Amount != nil {
		setParts = append(setParts, fmt.Sprintf("amount = $%d", argIndex))
		args = append(args, *updates.Amount)
		argIndex++
	}

	if updates.MinimumAmount != nil {
		setParts = append(setParts, fmt.Sprintf("minimum_amount = $%d", argIndex))
		args = append(args, *updates.MinimumAmount)
		argIndex++
	}

	if updates.MaximumAmount != nil {
		setParts = append(setParts, fmt.Sprintf("maximum_amount = $%d", argIndex))
		args = append(args, *updates.MaximumAmount)
		argIndex++
	}

	if updates.ApplicableTo != nil {
		setParts = append(setParts, fmt.Sprintf("applicable_to = $%d", argIndex))
		args = append(args, *updates.ApplicableTo)
		argIndex++
	}

	if updates.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *updates.IsActive)
		argIndex++
	}

	if len(setParts) == 0 {
		return fmt.Errorf("no fields to update")
	}

	setParts = append(setParts, "updated_at = CURRENT_TIMESTAMP")

	query := fmt.Sprintf("UPDATE fees SET %s WHERE id = $%d",
		strings.Join(setParts, ", "), argIndex)
	args = append(args, id)

	result, err := r.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to update fee: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("fee not found")
	}

	return nil
}

func (r *FeeRepository) Delete(id uuid.UUID) error {
	result, err := r.db.Exec("UPDATE fees SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1", id)
	if err != nil {
		return fmt.Errorf("failed to delete fee: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("fee not found")
	}

	return nil
}

// Fee calculation methods
func (r *FeeRepository) GetActiveFeesByType(applicableTo string) ([]*models.Fee, error) {
	query := `
		SELECT id, name, fee_type, calculation_type, amount, minimum_amount, 
		       maximum_amount, applicable_to, is_active, created_at, updated_at
		FROM fees 
		WHERE is_active = true 
		AND (applicable_to = 'all' OR applicable_to = $1)
		ORDER BY name ASC`

	rows, err := r.db.Query(query, applicableTo)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var fees []*models.Fee
	for rows.Next() {
		fee := &models.Fee{}
		err := rows.Scan(
			&fee.ID,
			&fee.Name,
			&fee.FeeType,
			&fee.CalculationType,
			&fee.Amount,
			&fee.MinimumAmount,
			&fee.MaximumAmount,
			&fee.ApplicableTo,
			&fee.IsActive,
			&fee.CreatedAt,
			&fee.UpdatedAt,
		)

		if err != nil {
			return nil, err
		}

		// Load fee tiers if applicable
		if fee.CalculationType == models.CalculationTypeTiered {
			tiers, err := r.getFeeTiers(fee.ID)
			if err != nil {
				return nil, err
			}
			fee.Tiers = tiers
		}

		fees = append(fees, fee)
	}

	return fees, nil
}

func (r *FeeRepository) GetActiveFees() ([]*models.Fee, error) {
	query := `
		SELECT id, name, fee_type, calculation_type, amount, minimum_amount, 
		       maximum_amount, applicable_to, is_active, created_at, updated_at
		FROM fees 
		WHERE is_active = true 
		ORDER BY name ASC`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var fees []*models.Fee
	for rows.Next() {
		fee := &models.Fee{}
		err := rows.Scan(
			&fee.ID,
			&fee.Name,
			&fee.FeeType,
			&fee.CalculationType,
			&fee.Amount,
			&fee.MinimumAmount,
			&fee.MaximumAmount,
			&fee.ApplicableTo,
			&fee.IsActive,
			&fee.CreatedAt,
			&fee.UpdatedAt,
		)

		if err != nil {
			return nil, err
		}

		fees = append(fees, fee)
	}

	return fees, nil
}

// Helper methods
func (r *FeeRepository) getFeeTiers(feeID uuid.UUID) ([]models.FeeTier, error) {
	query := `
		SELECT id, fee_id, min_amount, max_amount, tier_fee, created_at
		FROM fee_tiers 
		WHERE fee_id = $1 
		ORDER BY min_amount ASC`

	rows, err := r.db.Query(query, feeID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tiers []models.FeeTier
	for rows.Next() {
		tier := models.FeeTier{}
		err := rows.Scan(
			&tier.ID,
			&tier.FeeID,
			&tier.MinAmount,
			&tier.MaxAmount,
			&tier.TierFee,
			&tier.CreatedAt,
		)

		if err != nil {
			return nil, err
		}

		tiers = append(tiers, tier)
	}

	return tiers, nil
}

func (r *FeeRepository) buildWhereClause(query *models.FeeSearchQuery) (string, []interface{}) {
	conditions := []string{}
	args := []interface{}{}
	argIndex := 1

	if query.Name != "" {
		conditions = append(conditions, fmt.Sprintf("name ILIKE $%d", argIndex))
		args = append(args, "%"+query.Name+"%")
		argIndex++
	}

	if query.FeeType != "" {
		conditions = append(conditions, fmt.Sprintf("fee_type = $%d", argIndex))
		args = append(args, query.FeeType)
		argIndex++
	}

	if query.CalculationType != "" {
		conditions = append(conditions, fmt.Sprintf("calculation_type = $%d", argIndex))
		args = append(args, query.CalculationType)
		argIndex++
	}

	if query.ApplicableTo != "" {
		conditions = append(conditions, fmt.Sprintf("applicable_to = $%d", argIndex))
		args = append(args, query.ApplicableTo)
		argIndex++
	}

	if query.IsActive != nil {
		conditions = append(conditions, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *query.IsActive)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	return whereClause, args
}
