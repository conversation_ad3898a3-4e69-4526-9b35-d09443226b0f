package repository

import (
	"database/sql"
	"fmt"
	"shark/tax-fee-service/internal/models"
	"strings"

	"github.com/google/uuid"
)

type TaxRepository struct {
	db *sql.DB
}

func NewTaxRepository(db *sql.DB) *TaxRepository {
	return &TaxRepository{db: db}
}

// CRUD Operations
func (r *TaxRepository) Create(taxRule *models.TaxRule) error {
	query := `
		INSERT INTO tax_rules (id, name, tax_type, rate, is_percentage, country, state, 
		                      city, postal_code, is_active, priority, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		RETURNING created_at, updated_at`

	err := r.db.QueryRow(
		query,
		taxRule.ID,
		taxRule.Name,
		taxRule.TaxType,
		taxRule.Rate,
		taxRule.IsPercentage,
		taxRule.Country,
		taxRule.State,
		taxRule.City,
		taxRule.PostalCode,
		taxRule.IsActive,
		taxRule.Priority,
	).Scan(&taxRule.CreatedAt, &taxRule.UpdatedAt)

	return err
}

func (r *TaxRepository) GetByID(id uuid.UUID) (*models.TaxRule, error) {
	taxRule := &models.TaxRule{}
	query := `
		SELECT id, name, tax_type, rate, is_percentage, country, state, city, 
		       postal_code, is_active, priority, created_at, updated_at
		FROM tax_rules WHERE id = $1`

	err := r.db.QueryRow(query, id).Scan(
		&taxRule.ID,
		&taxRule.Name,
		&taxRule.TaxType,
		&taxRule.Rate,
		&taxRule.IsPercentage,
		&taxRule.Country,
		&taxRule.State,
		&taxRule.City,
		&taxRule.PostalCode,
		&taxRule.IsActive,
		&taxRule.Priority,
		&taxRule.CreatedAt,
		&taxRule.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, fmt.Errorf("tax rule not found")
	}

	return taxRule, err
}

func (r *TaxRepository) List(query *models.TaxRuleSearchQuery) ([]*models.TaxRule, int, error) {
	// Build WHERE clause
	whereClause, args := r.buildWhereClause(query)

	// Count total records
	countQuery := "SELECT COUNT(*) FROM tax_rules" + whereClause
	var total int
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count tax rules: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "created_at DESC"
	if query.SortBy != "" {
		direction := "ASC"
		if query.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("%s %s", query.SortBy, direction)
	}

	// Calculate offset
	offset := (query.Page - 1) * query.Limit

	// Main query
	mainQuery := fmt.Sprintf(`
		SELECT id, name, tax_type, rate, is_percentage, country, state, city, 
		       postal_code, is_active, priority, created_at, updated_at
		FROM tax_rules%s ORDER BY %s LIMIT $%d OFFSET $%d`,
		whereClause, orderBy, len(args)+1, len(args)+2)

	args = append(args, query.Limit, offset)

	rows, err := r.db.Query(mainQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list tax rules: %w", err)
	}
	defer rows.Close()

	var taxRules []*models.TaxRule
	for rows.Next() {
		taxRule := &models.TaxRule{}
		err := rows.Scan(
			&taxRule.ID,
			&taxRule.Name,
			&taxRule.TaxType,
			&taxRule.Rate,
			&taxRule.IsPercentage,
			&taxRule.Country,
			&taxRule.State,
			&taxRule.City,
			&taxRule.PostalCode,
			&taxRule.IsActive,
			&taxRule.Priority,
			&taxRule.CreatedAt,
			&taxRule.UpdatedAt,
		)

		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan tax rule: %w", err)
		}

		taxRules = append(taxRules, taxRule)
	}

	return taxRules, total, nil
}

func (r *TaxRepository) Update(id uuid.UUID, updates *models.UpdateTaxRuleRequest) error {
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if updates.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *updates.Name)
		argIndex++
	}

	if updates.TaxType != nil {
		setParts = append(setParts, fmt.Sprintf("tax_type = $%d", argIndex))
		args = append(args, *updates.TaxType)
		argIndex++
	}

	if updates.Rate != nil {
		setParts = append(setParts, fmt.Sprintf("rate = $%d", argIndex))
		args = append(args, *updates.Rate)
		argIndex++
	}

	if updates.IsPercentage != nil {
		setParts = append(setParts, fmt.Sprintf("is_percentage = $%d", argIndex))
		args = append(args, *updates.IsPercentage)
		argIndex++
	}

	if updates.State != nil {
		setParts = append(setParts, fmt.Sprintf("state = $%d", argIndex))
		args = append(args, *updates.State)
		argIndex++
	}

	if updates.City != nil {
		setParts = append(setParts, fmt.Sprintf("city = $%d", argIndex))
		args = append(args, *updates.City)
		argIndex++
	}

	if updates.PostalCode != nil {
		setParts = append(setParts, fmt.Sprintf("postal_code = $%d", argIndex))
		args = append(args, *updates.PostalCode)
		argIndex++
	}

	if updates.Priority != nil {
		setParts = append(setParts, fmt.Sprintf("priority = $%d", argIndex))
		args = append(args, *updates.Priority)
		argIndex++
	}

	if updates.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *updates.IsActive)
		argIndex++
	}

	if len(setParts) == 0 {
		return fmt.Errorf("no fields to update")
	}

	setParts = append(setParts, "updated_at = CURRENT_TIMESTAMP")

	query := fmt.Sprintf("UPDATE tax_rules SET %s WHERE id = $%d",
		strings.Join(setParts, ", "), argIndex)
	args = append(args, id)

	result, err := r.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to update tax rule: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("tax rule not found")
	}

	return nil
}

func (r *TaxRepository) Delete(id uuid.UUID) error {
	result, err := r.db.Exec("UPDATE tax_rules SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1", id)
	if err != nil {
		return fmt.Errorf("failed to delete tax rule: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("tax rule not found")
	}

	return nil
}

// Location-based tax calculation
func (r *TaxRepository) GetTaxRulesByLocation(country, state, city, postalCode string) ([]*models.TaxRule, error) {
	query := `
		SELECT id, name, tax_type, rate, is_percentage, country, state, city, 
		       postal_code, is_active, priority, created_at, updated_at
		FROM tax_rules 
		WHERE is_active = true 
		AND (country IS NULL OR country = $1)
		AND (state IS NULL OR state = $2)
		AND (city IS NULL OR city = $3)
		AND (postal_code IS NULL OR postal_code = $4)
		ORDER BY priority ASC, created_at ASC`

	rows, err := r.db.Query(query, strings.ToUpper(country), strings.ToUpper(state), city, postalCode)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var taxRules []*models.TaxRule
	for rows.Next() {
		taxRule := &models.TaxRule{}
		err := rows.Scan(
			&taxRule.ID,
			&taxRule.Name,
			&taxRule.TaxType,
			&taxRule.Rate,
			&taxRule.IsPercentage,
			&taxRule.Country,
			&taxRule.State,
			&taxRule.City,
			&taxRule.PostalCode,
			&taxRule.IsActive,
			&taxRule.Priority,
			&taxRule.CreatedAt,
			&taxRule.UpdatedAt,
		)

		if err != nil {
			return nil, err
		}

		taxRules = append(taxRules, taxRule)
	}

	return taxRules, nil
}

func (r *TaxRepository) GetActiveTaxRules() ([]*models.TaxRule, error) {
	query := `
		SELECT id, name, tax_type, rate, is_percentage, country, state, city, 
		       postal_code, is_active, priority, created_at, updated_at
		FROM tax_rules 
		WHERE is_active = true 
		ORDER BY country, state, priority ASC`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var taxRules []*models.TaxRule
	for rows.Next() {
		taxRule := &models.TaxRule{}
		err := rows.Scan(
			&taxRule.ID,
			&taxRule.Name,
			&taxRule.TaxType,
			&taxRule.Rate,
			&taxRule.IsPercentage,
			&taxRule.Country,
			&taxRule.State,
			&taxRule.City,
			&taxRule.PostalCode,
			&taxRule.IsActive,
			&taxRule.Priority,
			&taxRule.CreatedAt,
			&taxRule.UpdatedAt,
		)

		if err != nil {
			return nil, err
		}

		taxRules = append(taxRules, taxRule)
	}

	return taxRules, nil
}

// Helper methods
func (r *TaxRepository) buildWhereClause(query *models.TaxRuleSearchQuery) (string, []interface{}) {
	conditions := []string{}
	args := []interface{}{}
	argIndex := 1

	if query.Name != "" {
		conditions = append(conditions, fmt.Sprintf("name ILIKE $%d", argIndex))
		args = append(args, "%"+query.Name+"%")
		argIndex++
	}

	if query.TaxType != "" {
		conditions = append(conditions, fmt.Sprintf("tax_type = $%d", argIndex))
		args = append(args, query.TaxType)
		argIndex++
	}

	if query.Country != "" {
		conditions = append(conditions, fmt.Sprintf("country = $%d", argIndex))
		args = append(args, strings.ToUpper(query.Country))
		argIndex++
	}

	if query.State != "" {
		conditions = append(conditions, fmt.Sprintf("state = $%d", argIndex))
		args = append(args, strings.ToUpper(query.State))
		argIndex++
	}

	if query.IsActive != nil {
		conditions = append(conditions, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *query.IsActive)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	return whereClause, args
}
