package service

import (
	"fmt"
	"shark/tax-fee-service/internal/models"
	"shark/tax-fee-service/internal/repository"
	"strings"

	"github.com/google/uuid"
)

type TaxFeeService struct {
	taxRepo *repository.TaxRepository
	feeRepo *repository.FeeRepository
}

func NewTaxFeeService(taxRepo *repository.TaxRepository, feeRepo *repository.FeeRepository) *TaxFeeService {
	return &TaxFeeService{
		taxRepo: taxRepo,
		feeRepo: feeRepo,
	}
}

// Tax Calculation
func (s *TaxFeeService) CalculateTax(req *models.CalculateTaxRequest) (*models.TaxCalculationResult, error) {
	if err := s.validateTaxRequest(req); err != nil {
		return nil, err
	}

	// Get applicable tax rules for the location
	taxRules, err := s.taxRepo.GetTaxRulesByLocation(req.Country, req.State, req.City, req.PostalCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get tax rules: %w", err)
	}

	result := &models.TaxCalculationResult{
		Breakdown: []models.TaxBreakdown{},
	}

	// Calculate tax for each applicable rule
	for _, rule := range taxRules {
		if !rule.IsActive {
			continue
		}

		var taxAmount float64
		if rule.IsPercentage {
			taxAmount = req.Subtotal * rule.Rate
		} else {
			taxAmount = rule.Rate // Fixed amount
		}

		result.TotalTax += taxAmount
		result.Breakdown = append(result.Breakdown, models.TaxBreakdown{
			Name:   rule.Name,
			Type:   rule.TaxType,
			Rate:   rule.Rate,
			Amount: taxAmount,
		})
	}

	return result, nil
}

// Fee Calculation
func (s *TaxFeeService) CalculateFee(req *models.CalculateFeeRequest) (*models.FeeCalculationResult, error) {
	if err := s.validateFeeRequest(req); err != nil {
		return nil, err
	}

	// Get applicable fees
	fees, err := s.feeRepo.GetActiveFeesByType(req.ApplicableTo)
	if err != nil {
		return nil, fmt.Errorf("failed to get fees: %w", err)
	}

	result := &models.FeeCalculationResult{
		Breakdown: []models.FeeBreakdown{},
	}

	// Calculate each applicable fee
	for _, fee := range fees {
		if !fee.IsActive {
			continue
		}

		feeAmount, err := s.calculateSingleFee(fee, req.Subtotal)
		if err != nil {
			return nil, fmt.Errorf("failed to calculate fee %s: %w", fee.Name, err)
		}

		result.TotalFees += feeAmount
		result.Breakdown = append(result.Breakdown, models.FeeBreakdown{
			Name:   fee.Name,
			Type:   fee.FeeType,
			Amount: feeAmount,
		})
	}

	return result, nil
}

// Tax Rule Management
func (s *TaxFeeService) CreateTaxRule(req *models.CreateTaxRuleRequest) (*models.TaxRule, error) {
	if err := s.validateCreateTaxRuleRequest(req); err != nil {
		return nil, err
	}

	// Check for duplicate tax rules in the same location
	existing, err := s.taxRepo.GetTaxRulesByLocation(req.Country, req.State, req.City, req.PostalCode)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing tax rules: %w", err)
	}

	for _, rule := range existing {
		if rule.TaxType == req.TaxType && rule.IsActive {
			return nil, fmt.Errorf("tax rule of type %s already exists for this location", req.TaxType)
		}
	}

	taxRule := &models.TaxRule{
		ID:           uuid.New(),
		Name:         req.Name,
		TaxType:      req.TaxType,
		Rate:         req.Rate,
		IsPercentage: req.IsPercentage,
		Country:      strings.ToUpper(req.Country),
		State:        strings.ToUpper(req.State),
		City:         req.City,
		PostalCode:   req.PostalCode,
		IsActive:     true,
		Priority:     req.Priority,
	}

	err = s.taxRepo.Create(taxRule)
	if err != nil {
		return nil, fmt.Errorf("failed to create tax rule: %w", err)
	}

	return taxRule, nil
}

func (s *TaxFeeService) GetTaxRule(id uuid.UUID) (*models.TaxRule, error) {
	taxRule, err := s.taxRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get tax rule: %w", err)
	}

	return taxRule, nil
}

func (s *TaxFeeService) ListTaxRules(query *models.TaxRuleSearchQuery) ([]*models.TaxRule, int, error) {
	// Set defaults
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.Limit <= 0 {
		query.Limit = 20
	}
	if query.Limit > 100 {
		query.Limit = 100
	}

	taxRules, total, err := s.taxRepo.List(query)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list tax rules: %w", err)
	}

	return taxRules, total, nil
}

func (s *TaxFeeService) UpdateTaxRule(id uuid.UUID, req *models.UpdateTaxRuleRequest) (*models.TaxRule, error) {
	if err := s.validateUpdateTaxRuleRequest(req); err != nil {
		return nil, err
	}

	// Check if tax rule exists
	existing, err := s.taxRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("tax rule not found: %w", err)
	}

	// Prevent updating critical fields if rule is being used
	if existing.IsActive && req.Rate != nil {
		// In a real system, you'd check if this tax rule is being used in active orders
		// For now, we'll allow the update but log a warning
	}

	err = s.taxRepo.Update(id, req)
	if err != nil {
		return nil, fmt.Errorf("failed to update tax rule: %w", err)
	}

	// Return updated tax rule
	return s.taxRepo.GetByID(id)
}

func (s *TaxFeeService) DeleteTaxRule(id uuid.UUID) error {
	// Check if tax rule exists
	existing, err := s.taxRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("tax rule not found: %w", err)
	}

	// Prevent deleting active tax rules that are being used
	if existing.IsActive {
		// In a real system, check if this tax rule is being used
		// For now, we'll just deactivate it instead of deleting
		req := &models.UpdateTaxRuleRequest{
			IsActive: &[]bool{false}[0],
		}
		_, err = s.UpdateTaxRule(id, req)
		return err
	}

	err = s.taxRepo.Delete(id)
	if err != nil {
		return fmt.Errorf("failed to delete tax rule: %w", err)
	}

	return nil
}

// Fee Management
func (s *TaxFeeService) CreateFee(req *models.CreateFeeRequest) (*models.Fee, error) {
	if err := s.validateCreateFeeRequest(req); err != nil {
		return nil, err
	}

	fee := &models.Fee{
		ID:              uuid.New(),
		Name:            req.Name,
		FeeType:         req.FeeType,
		CalculationType: req.CalculationType,
		Amount:          req.Amount,
		MinimumAmount:   req.MinimumAmount,
		MaximumAmount:   req.MaximumAmount,
		ApplicableTo:    req.ApplicableTo,
		IsActive:        true,
	}

	// Handle tiered fees
	if req.CalculationType == models.CalculationTypeTiered && len(req.Tiers) > 0 {
		for _, tierReq := range req.Tiers {
			tier := models.FeeTier{
				ID:        uuid.New(),
				FeeID:     fee.ID,
				MinAmount: tierReq.MinAmount,
				MaxAmount: tierReq.MaxAmount,
				TierFee:   tierReq.TierFee,
			}
			fee.Tiers = append(fee.Tiers, tier)
		}
	}

	err := s.feeRepo.Create(fee)
	if err != nil {
		return nil, fmt.Errorf("failed to create fee: %w", err)
	}

	return fee, nil
}

func (s *TaxFeeService) GetFee(id uuid.UUID) (*models.Fee, error) {
	fee, err := s.feeRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get fee: %w", err)
	}

	return fee, nil
}

func (s *TaxFeeService) ListFees(query *models.FeeSearchQuery) ([]*models.Fee, int, error) {
	// Set defaults
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.Limit <= 0 {
		query.Limit = 20
	}
	if query.Limit > 100 {
		query.Limit = 100
	}

	fees, total, err := s.feeRepo.List(query)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list fees: %w", err)
	}

	return fees, total, nil
}

func (s *TaxFeeService) UpdateFee(id uuid.UUID, req *models.UpdateFeeRequest) (*models.Fee, error) {
	if err := s.validateUpdateFeeRequest(req); err != nil {
		return nil, err
	}

	// Check if fee exists
	_, err := s.feeRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("fee not found: %w", err)
	}

	err = s.feeRepo.Update(id, req)
	if err != nil {
		return nil, fmt.Errorf("failed to update fee: %w", err)
	}

	// Return updated fee
	return s.feeRepo.GetByID(id)
}

func (s *TaxFeeService) DeleteFee(id uuid.UUID) error {
	// Check if fee exists
	_, err := s.feeRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("fee not found: %w", err)
	}

	err = s.feeRepo.Delete(id)
	if err != nil {
		return fmt.Errorf("failed to delete fee: %w", err)
	}

	return nil
}

// Helper methods
func (s *TaxFeeService) calculateSingleFee(fee *models.Fee, subtotal float64) (float64, error) {
	var feeAmount float64

	switch fee.CalculationType {
	case models.CalculationTypeFixed:
		feeAmount = fee.Amount

	case models.CalculationTypePercentage:
		feeAmount = subtotal * (fee.Amount / 100)
		// Apply minimum and maximum limits
		if feeAmount < fee.MinimumAmount {
			feeAmount = fee.MinimumAmount
		}
		if fee.MaximumAmount != nil && feeAmount > *fee.MaximumAmount {
			feeAmount = *fee.MaximumAmount
		}

	case models.CalculationTypeTiered:
		// Calculate tiered fee
		feeAmount = s.calculateTieredFee(fee.Tiers, subtotal)

	default:
		return 0, fmt.Errorf("unsupported calculation type: %s", fee.CalculationType)
	}

	return feeAmount, nil
}

func (s *TaxFeeService) calculateTieredFee(tiers []models.FeeTier, amount float64) float64 {
	for _, tier := range tiers {
		if amount >= tier.MinAmount {
			if tier.MaxAmount == nil || amount <= *tier.MaxAmount {
				return tier.TierFee
			}
		}
	}
	return 0 // No applicable tier found
}

// Validation methods
func (s *TaxFeeService) validateTaxRequest(req *models.CalculateTaxRequest) error {
	if req.Subtotal < 0 {
		return fmt.Errorf("subtotal cannot be negative")
	}

	if len(req.Country) != 2 {
		return fmt.Errorf("country must be a 2-letter ISO code")
	}

	return nil
}

func (s *TaxFeeService) validateFeeRequest(req *models.CalculateFeeRequest) error {
	if req.Subtotal < 0 {
		return fmt.Errorf("subtotal cannot be negative")
	}

	validTypes := []string{models.ApplicableToAll, models.ApplicableToProducts, models.ApplicableToServices, models.ApplicableToOrders, models.ApplicableToBookings}
	for _, validType := range validTypes {
		if req.ApplicableTo == validType {
			return nil
		}
	}

	return fmt.Errorf("invalid applicable_to value: %s", req.ApplicableTo)
}

func (s *TaxFeeService) validateCreateTaxRuleRequest(req *models.CreateTaxRuleRequest) error {
	if req.Rate < 0 || req.Rate > models.MaxTaxRate {
		return fmt.Errorf("tax rate must be between 0 and %.2f", models.MaxTaxRate)
	}

	if len(req.Country) != 2 {
		return fmt.Errorf("country must be a 2-letter ISO code")
	}

	if req.Priority < 0 {
		return fmt.Errorf("priority cannot be negative")
	}

	return nil
}

func (s *TaxFeeService) validateUpdateTaxRuleRequest(req *models.UpdateTaxRuleRequest) error {
	if req.Rate != nil && (*req.Rate < 0 || *req.Rate > models.MaxTaxRate) {
		return fmt.Errorf("tax rate must be between 0 and %.2f", models.MaxTaxRate)
	}

	if req.Priority != nil && *req.Priority < 0 {
		return fmt.Errorf("priority cannot be negative")
	}

	return nil
}

func (s *TaxFeeService) validateCreateFeeRequest(req *models.CreateFeeRequest) error {
	if req.Amount < 0 || req.Amount > models.MaxFeeAmount {
		return fmt.Errorf("fee amount must be between 0 and %.2f", models.MaxFeeAmount)
	}

	if req.MinimumAmount < 0 {
		return fmt.Errorf("minimum amount cannot be negative")
	}

	if req.MaximumAmount != nil && *req.MaximumAmount < req.MinimumAmount {
		return fmt.Errorf("maximum amount cannot be less than minimum amount")
	}

	if req.CalculationType == models.CalculationTypeTiered {
		if len(req.Tiers) == 0 {
			return fmt.Errorf("tiered fees must have at least one tier")
		}
		if len(req.Tiers) > models.MaxFeeTiers {
			return fmt.Errorf("cannot have more than %d fee tiers", models.MaxFeeTiers)
		}
	}

	return nil
}

func (s *TaxFeeService) validateUpdateFeeRequest(req *models.UpdateFeeRequest) error {
	if req.Amount != nil && (*req.Amount < 0 || *req.Amount > models.MaxFeeAmount) {
		return fmt.Errorf("fee amount must be between 0 and %.2f", models.MaxFeeAmount)
	}

	if req.MinimumAmount != nil && *req.MinimumAmount < 0 {
		return fmt.Errorf("minimum amount cannot be negative")
	}

	return nil
}
