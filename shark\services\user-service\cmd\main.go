package main

import (
	"log"
	"net/http"
	"os"

	"shark/user-service/internal/config"
	"shark/user-service/internal/handlers"
	"shark/user-service/internal/middleware"
	"shark/user-service/internal/repository"
	"shark/user-service/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Load configuration
	cfg := config.Load()

	// Initialize database connection
	db, err := config.InitDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize repositories
	userRepo := repository.NewUserRepository(db)
	roleRepo := repository.NewRoleRepository(db)

	// Initialize services
	authService := services.NewAuthService(userRepo, roleRepo, cfg.JWTSecret)
	userService := services.NewUserService(userRepo, roleRepo)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService)
	userHandler := handlers.NewUserHandler(userService)

	// Setup router
	router := setupRouter(authHandler, userHandler, cfg)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8001"
	}

	log.Printf("User Service starting on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func setupRouter(authHandler *handlers.AuthHandler, userHandler *handlers.UserHandler, cfg *config.Config) http.Handler {
	// Set Gin mode
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// CORS middleware
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "service": "user-service"})
	})

	// API routes
	api := router.Group("/api/v1")
	{
		// Auth routes (public)
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
			auth.POST("/forgot-password", authHandler.ForgotPassword)
			auth.POST("/reset-password", authHandler.ResetPassword)
		}

		// User routes (protected)
		users := api.Group("/users")
		users.Use(middleware.AuthMiddleware(cfg.JWTSecret))
		{
			users.GET("/profile", userHandler.GetProfile)
			users.PUT("/profile", userHandler.UpdateProfile)
			users.POST("/switch-role", userHandler.SwitchRole)
			users.GET("/roles", userHandler.GetUserRoles)
			users.PUT("/password", userHandler.ChangePassword)
			users.DELETE("/account", userHandler.DeleteAccount)
		}

		// Public routes for database data fetching
		public := api.Group("/public")
		{
			public.GET("/users", userHandler.GetAllUsers)        // Get all users (paginated)
			public.GET("/users/search", userHandler.SearchUsers) // Search users
			public.GET("/users/:id", userHandler.GetUserByID)    // Get user by ID
		}

		// Admin routes (admin only)
		admin := api.Group("/admin")
		admin.Use(middleware.AuthMiddleware(cfg.JWTSecret))
		admin.Use(middleware.AdminMiddleware())
		{
			admin.GET("/users", userHandler.GetAllUsers)
			admin.GET("/users/:id", userHandler.GetUserByID)
			admin.PUT("/users/:id/status", userHandler.UpdateUserStatus)
			admin.PUT("/users/:id/roles", userHandler.UpdateUserRoles)
		}
	}

	return c.Handler(router)
}
