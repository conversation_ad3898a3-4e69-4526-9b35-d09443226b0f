package config

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

// ConfigLoader handles loading configuration from multiple sources
type ConfigLoader struct {
	configPaths []string
	loaded      map[string]string
}

// NewConfigLoader creates a new configuration loader
func NewConfigLoader() *ConfigLoader {
	return &ConfigLoader{
		loaded: make(map[string]string),
	}
}

// LoadConfigs loads configuration from multiple files in order
// Later files override earlier ones
func (c *ConfigLoader) LoadConfigs(paths ...string) error {
	c.configPaths = paths
	
	for _, path := range paths {
		if err := c.loadFile(path); err != nil {
			// Don't fail if file doesn't exist, just skip it
			if !os.IsNotExist(err) {
				return fmt.Errorf("failed to load config from %s: %w", path, err)
			}
		}
	}
	
	// Set environment variables
	for key, value := range c.loaded {
		if os.Getenv(key) == "" { // Don't override existing env vars
			os.Setenv(key, value)
		}
	}
	
	return nil
}

// loadFile loads a single .env file
func (c *ConfigLoader) loadFile(path string) error {
	// Make path relative to project root
	if !filepath.IsAbs(path) {
		// Try to find project root by looking for go.mod files
		projectRoot, err := findProjectRoot()
		if err == nil {
			path = filepath.Join(projectRoot, path)
		}
	}
	
	file, err := os.Open(path)
	if err != nil {
		return err
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		
		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		// Parse key=value
		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}
		
		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])
		
		// Remove quotes if present
		if len(value) >= 2 {
			if (value[0] == '"' && value[len(value)-1] == '"') ||
				(value[0] == '\'' && value[len(value)-1] == '\'') {
				value = value[1 : len(value)-1]
			}
		}
		
		c.loaded[key] = value
	}
	
	return scanner.Err()
}

// findProjectRoot finds the project root by looking for specific files
func findProjectRoot() (string, error) {
	dir, err := os.Getwd()
	if err != nil {
		return "", err
	}
	
	for {
		// Check for project indicators
		indicators := []string{"go.mod", "docker-compose.yml", ".git"}
		for _, indicator := range indicators {
			if _, err := os.Stat(filepath.Join(dir, indicator)); err == nil {
				return dir, nil
			}
		}
		
		parent := filepath.Dir(dir)
		if parent == dir {
			break // Reached root
		}
		dir = parent
	}
	
	return "", fmt.Errorf("project root not found")
}

// GetString gets a string value from environment
func GetString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// GetInt gets an integer value from environment
func GetInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// GetBool gets a boolean value from environment
func GetBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// LoadSharkConfig loads the standard Shark platform configuration
func LoadSharkConfig(serviceName string) error {
	loader := NewConfigLoader()
	
	// Load in order of precedence (later files override earlier ones)
	return loader.LoadConfigs(
		"config/database.env",           // Database config
		"config/shared.env",             // Shared config
		"config/services.env",           // Service ports
		fmt.Sprintf("services/%s/.env", serviceName), // Service-specific config
		".env.local",                    // Local overrides (git-ignored)
	)
}
