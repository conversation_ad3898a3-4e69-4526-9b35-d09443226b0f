package database

import (
	"database/sql"
	"fmt"
	"os"
	"strconv"
	"time"

	_ "github.com/lib/pq"
)

// Config holds database configuration
type Config struct {
	Host            string
	Port            int
	User            string
	Password        string
	Name            string
	SSLMode         string
	MaxConnections  int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
	ConnTimeout     time.Duration
}

// DefaultConfig returns default database configuration
func DefaultConfig() *Config {
	return &Config{
		Host:            "localhost",
		Port:            5432,
		User:            "shark_user",
		Password:        "shark_password",
		Name:            "shark_db",
		SSLMode:         "disable",
		MaxConnections:  25,
		MaxIdleConns:    5,
		ConnMaxLifetime: time.Hour,
		ConnTimeout:     30 * time.Second,
	}
}

// LoadFromEnv loads database configuration from environment variables
func LoadFromEnv() *Config {
	cfg := DefaultConfig()

	if host := getEnv("DB_HOST", ""); host != "" {
		cfg.Host = host
	}
	if port := getEnvAsInt("DB_PORT", 0); port != 0 {
		cfg.Port = port
	}
	if user := getEnv("DB_USER", ""); user != "" {
		cfg.User = user
	}
	if password := getEnv("DB_PASSWORD", ""); password != "" {
		cfg.Password = password
	}
	if name := getEnv("DB_NAME", ""); name != "" {
		cfg.Name = name
	}
	if sslMode := getEnv("DB_SSL_MODE", ""); sslMode != "" {
		cfg.SSLMode = sslMode
	}
	if maxConns := getEnvAsInt("DB_MAX_CONNECTIONS", 0); maxConns != 0 {
		cfg.MaxConnections = maxConns
	}
	if maxIdle := getEnvAsInt("DB_MAX_IDLE_CONNECTIONS", 0); maxIdle != 0 {
		cfg.MaxIdleConns = maxIdle
	}

	return cfg
}

// Connect creates a new database connection with the given configuration
func Connect(cfg *Config) (*sql.DB, error) {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.Name, cfg.SSLMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(cfg.MaxConnections)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// Test the connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

// ConnectWithDefaults creates a database connection using default configuration from environment
func ConnectWithDefaults() (*sql.DB, error) {
	cfg := LoadFromEnv()
	return Connect(cfg)
}

// ConnectWithURL creates a database connection using a database URL
func ConnectWithURL(databaseURL string) (*sql.DB, error) {
	if databaseURL == "" {
		return ConnectWithDefaults()
	}

	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	// Set default connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

// MustConnect creates a database connection or panics on failure
// Use this only in main functions where failure should stop the application
func MustConnect(cfg *Config) *sql.DB {
	db, err := Connect(cfg)
	if err != nil {
		panic(fmt.Sprintf("Failed to connect to database: %v", err))
	}
	return db
}

// MustConnectWithDefaults creates a database connection using defaults or panics
func MustConnectWithDefaults() *sql.DB {
	db, err := ConnectWithDefaults()
	if err != nil {
		panic(fmt.Sprintf("Failed to connect to database: %v", err))
	}
	return db
}

// Helper functions (these should be moved to a shared utils package)
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
