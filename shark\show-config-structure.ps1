# Show Configuration Structure
Write-Host "CONFIGURATION SYSTEM OVERVIEW" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor White

Write-Host "`nDatabase Configuration (config/database.env):" -ForegroundColor Yellow
if (Test-Path "config/database.env") {
    Get-Content "config/database.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object { 
        Write-Host "   $_" -ForegroundColor Green
    }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`nShared Configuration (config/shared.env):" -ForegroundColor Yellow
if (Test-Path "config/shared.env") {
    Get-Content "config/shared.env" | Where-Object { $_ -and !$_.StartsWith("#") } | Select-Object -First 5 | ForEach-Object { 
        Write-Host "   $_" -ForegroundColor Green
    }
    Write-Host "   ... (and more)" -ForegroundColor Gray
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`nService Ports (config/services.env):" -ForegroundColor Yellow
if (Test-Path "config/services.env") {
    Get-Content "config/services.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object { 
        Write-Host "   $_" -ForegroundColor Green
    }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`nUser Service Specific (services/user-service/.env):" -ForegroundColor Yellow
if (Test-Path "services/user-service/.env") {
    Get-Content "services/user-service/.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object { 
        Write-Host "   $_" -ForegroundColor Blue
    }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`nBENEFITS:" -ForegroundColor Cyan
Write-Host "- Single source of truth for database config" -ForegroundColor Green
Write-Host "- No duplication of database credentials" -ForegroundColor Green
Write-Host "- Easy to update database settings" -ForegroundColor Green
Write-Host "- Service-specific configs remain separate" -ForegroundColor Green

Write-Host "`nConfiguration System Ready!" -ForegroundColor Green
