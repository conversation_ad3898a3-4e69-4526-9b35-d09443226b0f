# Shark Platform API Testing Script
Write-Host "🦈 Testing Shark Platform APIs..." -ForegroundColor Cyan

# Test all health endpoints
Write-Host "`n=== HEALTH CHECKS ===" -ForegroundColor Yellow

$services = @(
    @{Name="User Service"; Port=8001},
    @{Name="Service Catalog"; Port=8002},
    @{Name="Booking Service"; Port=8003},
    @{Name="Payment Service"; Port=8004},
    @{Name="Product Service"; Port=8010},
    @{Name="Notification Service"; Port=8006},
    @{Name="Review Service"; Port=8007},
    @{Name="Order Service"; Port=8008},
    @{Name="Analytics Service"; Port=8009}
)

foreach ($service in $services) {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:$($service.Port)/health" -Method GET -TimeoutSec 5
        Write-Host "✅ $($service.Name) (Port $($service.Port)): $($response.status)" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ $($service.Name) (Port $($service.Port)): FAILED" -ForegroundColor Red
    }
}

# Test User Service APIs
Write-Host "`n=== USER SERVICE TESTS ===" -ForegroundColor Yellow

# Test user registration
Write-Host "`n1. Testing User Registration..." -ForegroundColor Cyan
$registerData = @{
    first_name = "John"
    last_name = "Doe"
    email = "<EMAIL>"
    password = "password123"
    phone = "+**********"
} | ConvertTo-Json

try {
    $registerResponse = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/auth/register" -Method POST -Body $registerData -ContentType "application/json"
    Write-Host "✅ User Registration: SUCCESS" -ForegroundColor Green
    $userId = $registerResponse.data.user.id
    $token = $registerResponse.data.token
    Write-Host "   User ID: $userId" -ForegroundColor Gray
} catch {
    Write-Host "❌ User Registration: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test user login
Write-Host "`n2. Testing User Login..." -ForegroundColor Cyan
$loginData = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "✅ User Login: SUCCESS" -ForegroundColor Green
    $token = $loginResponse.data.token
    Write-Host "   Token: $($token.Substring(0,20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ User Login: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test Service Catalog APIs
Write-Host "`n=== SERVICE CATALOG TESTS ===" -ForegroundColor Yellow

# Test get categories
Write-Host "`n3. Testing Get Categories..." -ForegroundColor Cyan
try {
    $categoriesResponse = Invoke-RestMethod -Uri "http://localhost:8002/api/v1/public/categories/root" -Method GET
    Write-Host "✅ Get Categories: SUCCESS" -ForegroundColor Green
    Write-Host "   Found $($categoriesResponse.data.Count) categories" -ForegroundColor Gray
} catch {
    Write-Host "❌ Get Categories: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test search services
Write-Host "`n4. Testing Search Services..." -ForegroundColor Cyan
try {
    $servicesResponse = Invoke-RestMethod -Uri "http://localhost:8002/api/v1/public/services/search?limit=5" -Method GET
    Write-Host "✅ Search Services: SUCCESS" -ForegroundColor Green
    Write-Host "   Found $($servicesResponse.data.data.Count) services" -ForegroundColor Gray
} catch {
    Write-Host "❌ Search Services: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test service variants
Write-Host "`n5. Testing Service Variants..." -ForegroundColor Cyan
try {
    $variantsResponse = Invoke-RestMethod -Uri "http://localhost:8002/api/v1/public/service/880e8400-e29b-41d4-a716-446655440001/variants" -Method GET
    Write-Host "✅ Service Variants: SUCCESS" -ForegroundColor Green
    Write-Host "   Found $($variantsResponse.data.Count) variants" -ForegroundColor Gray
} catch {
    Write-Host "❌ Service Variants: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test service add-ons
Write-Host "`n6. Testing Service Add-ons..." -ForegroundColor Cyan
try {
    $addonsResponse = Invoke-RestMethod -Uri "http://localhost:8002/api/v1/public/service/880e8400-e29b-41d4-a716-446655440001/addons" -Method GET
    Write-Host "✅ Service Add-ons: SUCCESS" -ForegroundColor Green
    Write-Host "   Found $($addonsResponse.data.Count) add-ons" -ForegroundColor Gray
} catch {
    Write-Host "❌ Service Add-ons: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test Booking Service APIs
Write-Host "`n=== BOOKING SERVICE TESTS ===" -ForegroundColor Yellow

# Test vendor availability
Write-Host "`n7. Testing Vendor Availability..." -ForegroundColor Cyan
try {
    $availabilityResponse = Invoke-RestMethod -Uri "http://localhost:8003/api/v1/public/availability/vendor/550e8400-e29b-41d4-a716-446655440003" -Method GET
    Write-Host "✅ Vendor Availability: SUCCESS" -ForegroundColor Green
} catch {
    Write-Host "❌ Vendor Availability: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test Product Service APIs
Write-Host "`n=== PRODUCT SERVICE TESTS ===" -ForegroundColor Yellow

# Test product search
Write-Host "`n8. Testing Product Search..." -ForegroundColor Cyan
try {
    $productSearchResponse = Invoke-RestMethod -Uri "http://localhost:8010/api/v1/public/products/search" -Method GET
    Write-Host "✅ Product Search: SUCCESS" -ForegroundColor Green
} catch {
    Write-Host "❌ Product Search: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test product variants
Write-Host "`n9. Testing Product Variants..." -ForegroundColor Cyan
try {
    $productVariantsResponse = Invoke-RestMethod -Uri "http://localhost:8010/api/v1/public/product/550e8400-e29b-41d4-a716-446655440001/variants" -Method GET
    Write-Host "✅ Product Variants: SUCCESS" -ForegroundColor Green
} catch {
    Write-Host "❌ Product Variants: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test Other Services
Write-Host "`n=== OTHER SERVICES TESTS ===" -ForegroundColor Yellow

# Test Analytics
Write-Host "`n10. Testing Analytics..." -ForegroundColor Cyan
try {
    $analyticsResponse = Invoke-RestMethod -Uri "http://localhost:8009/api/v1/public/analytics" -Method GET
    Write-Host "✅ Analytics: SUCCESS" -ForegroundColor Green
} catch {
    Write-Host "❌ Analytics: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 API Testing Complete!" -ForegroundColor Cyan
Write-Host "Check the results above to see which APIs are working." -ForegroundColor White
