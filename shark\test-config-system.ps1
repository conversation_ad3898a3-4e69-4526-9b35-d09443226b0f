# Test the New Configuration System
Write-Host "🔧 TESTING NEW CONFIGURATION SYSTEM" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor White

Write-Host "`n📁 Configuration Files Structure:" -ForegroundColor Yellow
Write-Host "shark/" -ForegroundColor Gray
Write-Host "├── config/" -ForegroundColor Gray
Write-Host "│   ├── database.env      # 🗄️  Database settings (shared)" -ForegroundColor Green
Write-Host "│   ├── shared.env        # 🌐 Shared settings (JWT, Redis, etc.)" -ForegroundColor Green
Write-Host "│   └── services.env      # 🚀 Service ports" -ForegroundColor Green
Write-Host "├── services/" -ForegroundColor Gray
Write-Host "│   ├── user-service/.env # 👤 User service specific" -ForegroundColor Blue
Write-Host "│   ├── service-catalog/.env # 📋 Catalog service specific" -ForegroundColor Blue
Write-Host "│   └── product-service/.env # 📦 Product service specific" -ForegroundColor Blue
Write-Host "└── .env.local           # 🏠 Local overrides (optional)" -ForegroundColor Magenta

Write-Host "`n📋 Database Configuration (config/database.env):" -ForegroundColor Yellow
if (Test-Path "config/database.env") {
    Get-Content "config/database.env" | ForEach-Object { 
        if ($_ -and !$_.StartsWith("#")) {
            Write-Host "   $_" -ForegroundColor Green
        }
    }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`n🌐 Shared Configuration (config/shared.env):" -ForegroundColor Yellow
if (Test-Path "config/shared.env") {
    Get-Content "config/shared.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object { 
        Write-Host "   $_" -ForegroundColor Green
    }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`n🚀 Service Ports (config/services.env):" -ForegroundColor Yellow
if (Test-Path "config/services.env") {
    Get-Content "config/services.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object { 
        Write-Host "   $_" -ForegroundColor Green
    }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`nUser Service Specific (services/user-service/.env):" -ForegroundColor Yellow
if (Test-Path "services/user-service/.env") {
    Get-Content "services/user-service/.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object { 
        Write-Host "   $_" -ForegroundColor Blue
    }
} else {
    Write-Host "   File not found" -ForegroundColor Red
}

Write-Host "`n🎯 BENEFITS OF THIS APPROACH:" -ForegroundColor Cyan
Write-Host "✅ Single source of truth for database config" -ForegroundColor Green
Write-Host "✅ No duplication of database credentials" -ForegroundColor Green
Write-Host "✅ Easy to update database settings" -ForegroundColor Green
Write-Host "✅ Service-specific configs remain separate" -ForegroundColor Green
Write-Host "✅ Environment-specific overrides possible" -ForegroundColor Green
Write-Host "✅ Hierarchical configuration loading" -ForegroundColor Green

Write-Host "`n📚 CONFIGURATION HIERARCHY (precedence order):" -ForegroundColor Yellow
Write-Host "1. config/database.env    (lowest precedence)" -ForegroundColor Gray
Write-Host "2. config/shared.env" -ForegroundColor Gray
Write-Host "3. config/services.env" -ForegroundColor Gray
Write-Host "4. services/{service}/.env" -ForegroundColor Gray
Write-Host "5. .env.local            (highest precedence)" -ForegroundColor Gray
Write-Host "6. System environment variables (overrides all)" -ForegroundColor Gray

Write-Host "`n🔧 HOW TO USE:" -ForegroundColor Cyan
Write-Host "1. Put database config in config/database.env" -ForegroundColor White
Write-Host "2. Put shared config in config/shared.env" -ForegroundColor White
Write-Host "3. Put service-specific config in services/{service}/.env" -ForegroundColor White
Write-Host "4. Use the shared config loader in your services" -ForegroundColor White

Write-Host "`n🎉 Configuration System Ready!" -ForegroundColor Green
