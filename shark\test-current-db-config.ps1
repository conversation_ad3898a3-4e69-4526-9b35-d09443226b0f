# Test Current Database Configuration Setup
Write-Host "TESTING CURRENT DATABASE CONFIGURATION SETUP" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor White

# Test 1: Check configuration files exist
Write-Host "`n1. CONFIGURATION FILES CHECK:" -ForegroundColor Yellow

$configFiles = @(
    @{Path="config/database.env"; Name="Database Config"},
    @{Path="config/shared.env"; Name="Shared Config"},
    @{Path="config/services.env"; Name="Services Config"},
    @{Path="services/user-service/.env"; Name="User Service Config"}
)

foreach ($file in $configFiles) {
    if (Test-Path $file.Path) {
        Write-Host "  ✅ $($file.Name): EXISTS" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $($file.Name): MISSING" -ForegroundColor Red
    }
}

# Test 2: Check database configuration values
Write-Host "`n2. DATABASE CONFIGURATION VALUES:" -ForegroundColor Yellow
if (Test-Path "config/database.env") {
    Write-Host "  Database Config:" -ForegroundColor Cyan
    Get-Content "config/database.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
        Write-Host "    $_" -ForegroundColor Green
    }
} else {
    Write-Host "  ❌ Database config file missing!" -ForegroundColor Red
}

# Test 3: Check for configuration conflicts
Write-Host "`n3. CONFIGURATION CONFLICT CHECK:" -ForegroundColor Yellow

# Check if .env.shared still exists (should be removed)
if (Test-Path ".env.shared") {
    Write-Host "  ⚠️  WARNING: .env.shared still exists (should be removed)" -ForegroundColor Yellow
} else {
    Write-Host "  ✅ .env.shared properly removed" -ForegroundColor Green
}

# Test 4: Check shared database package
Write-Host "`n4. SHARED DATABASE PACKAGE CHECK:" -ForegroundColor Yellow
if (Test-Path "shared/database/connection.go") {
    Write-Host "  ✅ Shared database package: EXISTS" -ForegroundColor Green
} else {
    Write-Host "  ❌ Shared database package: MISSING" -ForegroundColor Red
}

if (Test-Path "shared/config/loader.go") {
    Write-Host "  ✅ Shared config loader: EXISTS" -ForegroundColor Green
} else {
    Write-Host "  ❌ Shared config loader: MISSING" -ForegroundColor Red
}

# Test 5: Test actual database connection
Write-Host "`n5. DATABASE CONNECTION TEST:" -ForegroundColor Yellow

# Set environment variables from config files
if (Test-Path "config/database.env") {
    Get-Content "config/database.env" | Where-Object { $_ -and !$_.StartsWith("#") } | ForEach-Object {
        $parts = $_ -split "=", 2
        if ($parts.Length -eq 2) {
            $key = $parts[0].Trim()
            $value = $parts[1].Trim()
            [Environment]::SetEnvironmentVariable($key, $value, "Process")
        }
    }
}

# Test User Service health
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8011/health" -Method GET -TimeoutSec 3
    Write-Host "  ✅ User Service: RUNNING ($($response.status))" -ForegroundColor Green
} catch {
    Write-Host "  ❌ User Service: NOT RUNNING" -ForegroundColor Red
}

# Test database connection via shared package
Write-Host "`n6. SHARED DATABASE PACKAGE TEST:" -ForegroundColor Yellow
try {
    $testResult = & go run test-shared-database.go 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Shared database package: WORKING" -ForegroundColor Green
        # Show key results
        $testResult | Where-Object { $_ -match "Found \d+ users" } | ForEach-Object {
            Write-Host "    $_" -ForegroundColor Cyan
        }
    } else {
        Write-Host "  ❌ Shared database package: FAILED" -ForegroundColor Red
        Write-Host "    Error: $testResult" -ForegroundColor Red
    }
} catch {
    Write-Host "  ⚠️  Could not test shared database package" -ForegroundColor Yellow
}

# Summary
Write-Host "`n" + "="*50 -ForegroundColor White
Write-Host "DATABASE CONFIGURATION SUMMARY" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor White

Write-Host "`nCURRENT SETUP:" -ForegroundColor Green
Write-Host "✅ config/database.env - Database credentials" -ForegroundColor White
Write-Host "✅ config/shared.env - Shared application config" -ForegroundColor White
Write-Host "✅ config/services.env - Service ports" -ForegroundColor White
Write-Host "✅ shared/database/ - Shared database connection code" -ForegroundColor White
Write-Host "✅ shared/config/ - Configuration loading utilities" -ForegroundColor White

Write-Host "`nCONFIGURATION FLOW:" -ForegroundColor Yellow
Write-Host "config/database.env → Environment Variables → Service Config → Database Connection" -ForegroundColor White

Write-Host "`nNEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Migrate services to use shared/database package" -ForegroundColor White
Write-Host "2. Remove duplicate InitDB functions from services" -ForegroundColor White
Write-Host "3. Use shared/config loader for configuration loading" -ForegroundColor White

Write-Host "`n🎉 Database Configuration Check Complete!" -ForegroundColor Green
