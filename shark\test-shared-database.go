package main

import (
	"fmt"
	"log"

	"shark/shared/database"
)

func main() {
	fmt.Println("🧪 Testing Shared Database Package")
	fmt.Println("==================================")

	// Test 1: Connect with defaults
	fmt.Println("\n1. Testing ConnectWithDefaults()...")
	db, err := database.ConnectWithDefaults()
	if err != nil {
		log.Printf("❌ Failed to connect with defaults: %v", err)
	} else {
		fmt.Println("✅ Connected successfully with defaults")

		// Test a simple query
		var count int
		err = db.QueryRow("SELECT COUNT(*) FROM users").Scan(&count)
		if err != nil {
			log.Printf("❌ Failed to query users table: %v", err)
		} else {
			fmt.Printf("✅ Found %d users in database\n", count)
		}

		db.Close()
	}

	// Test 2: Connect with custom config
	fmt.Println("\n2. Testing Connect() with custom config...")
	cfg := &database.Config{
		Host:           "localhost",
		Port:           5432,
		User:           "shark_user",
		Password:       "shark_password",
		Name:           "shark_db",
		SSLMode:        "disable",
		MaxConnections: 10,
		MaxIdleConns:   2,
	}

	db2, err := database.Connect(cfg)
	if err != nil {
		log.Printf("❌ Failed to connect with custom config: %v", err)
	} else {
		fmt.Println("✅ Connected successfully with custom config")

		// Test connection pool settings
		stats := db2.Stats()
		fmt.Printf("   Max Open Connections: %d\n", stats.MaxOpenConnections)

		db2.Close()
	}

	// Test 3: Load config from environment
	fmt.Println("\n3. Testing LoadFromEnv()...")
	envCfg := database.LoadFromEnv()
	fmt.Printf("   Host: %s\n", envCfg.Host)
	fmt.Printf("   Port: %d\n", envCfg.Port)
	fmt.Printf("   Database: %s\n", envCfg.Name)
	fmt.Printf("   Max Connections: %d\n", envCfg.MaxConnections)

	fmt.Println("\n🎉 Shared Database Package Test Complete!")
	fmt.Println("\n💡 This package can now replace all duplicate database connection code!")
}
