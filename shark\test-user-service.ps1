# Test User Service Registration and Login
Write-Host "Testing User Service Authentication..." -ForegroundColor Cyan

# Test user registration
Write-Host "`n1. Testing User Registration..." -ForegroundColor Yellow
$registerData = @{
    first_name = "<PERSON>"
    last_name = "<PERSON><PERSON>"
    email = "john.test.$(Get-Random)@example.com"
    password = "password123"
    phone = "+1234567890"
}

$registerJson = $registerData | ConvertTo-Json
Write-Host "Request Data: $registerJson" -ForegroundColor Gray

try {
    $registerResponse = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/auth/register" -Method POST -Body $registerJson -ContentType "application/json"
    Write-Host "✅ User Registration: SUCCESS" -ForegroundColor Green
    Write-Host "Response: $($registerResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
    
    $userId = $registerResponse.data.user.id
    $token = $registerResponse.data.token
    $userEmail = $registerResponse.data.user.email
    
    Write-Host "User ID: $userId" -ForegroundColor Cyan
    Write-Host "Email: $userEmail" -ForegroundColor Cyan
    Write-Host "Token: $($token.Substring(0,50))..." -ForegroundColor Cyan
    
    # Test user login with the registered user
    Write-Host "`n2. Testing User Login..." -ForegroundColor Yellow
    $loginData = @{
        email = $userEmail
        password = "password123"
    }
    
    $loginJson = $loginData | ConvertTo-Json
    
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/auth/login" -Method POST -Body $loginJson -ContentType "application/json"
    Write-Host "✅ User Login: SUCCESS" -ForegroundColor Green
    Write-Host "Login Token: $($loginResponse.data.token.Substring(0,50))..." -ForegroundColor Cyan
    
    # Test protected endpoint
    Write-Host "`n3. Testing Protected Endpoint..." -ForegroundColor Yellow
    $headers = @{
        "Authorization" = "Bearer $($loginResponse.data.token)"
        "Content-Type" = "application/json"
    }
    
    $profileResponse = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/user/profile" -Method GET -Headers $headers
    Write-Host "✅ Get Profile: SUCCESS" -ForegroundColor Green
    Write-Host "Profile: $($profileResponse.data.first_name) $($profileResponse.data.last_name)" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`nUser Service Testing Complete!" -ForegroundColor Cyan
