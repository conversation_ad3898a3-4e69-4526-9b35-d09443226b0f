# Verify Database Setup Across All Services
Write-Host "🔍 VERIFYING DATABASE SETUP ACROSS ALL SERVICES" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor White

# Function to read .env file
function Get-EnvValue {
    param($FilePath, $Key)
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath
        $line = $content | Where-Object { $_ -match "^$Key=" }
        if ($line) {
            return $line.Split('=', 2)[1]
        }
    }
    return "NOT_FOUND"
}

# Services to check
$services = @(
    @{Name="User Service"; Path="services/user-service/.env"},
    @{Name="Service Catalog"; Path="services/service-catalog/.env"},
    @{Name="Product Service"; Path="services/product-service/.env"},
    @{Name="Booking Service"; Path="services/booking-service/.env"},
    @{Name="Payment Service"; Path="services/payment-service/.env"}
)

Write-Host "`n📋 DATABASE CONFIGURATION PER SERVICE:" -ForegroundColor Yellow
Write-Host "--------------------------------------" -ForegroundColor Gray

$dbConfigs = @()

foreach ($service in $services) {
    $envPath = $service.Path
    
    if (Test-Path $envPath) {
        $dbHost = Get-EnvValue $envPath "DB_HOST"
        $dbPort = Get-EnvValue $envPath "DB_PORT"
        $dbName = Get-EnvValue $envPath "DB_NAME"
        $dbUser = Get-EnvValue $envPath "DB_USER"
        $servicePort = Get-EnvValue $envPath "PORT"
        
        Write-Host "`n🔧 $($service.Name):" -ForegroundColor Cyan
        Write-Host "   Service Port: $servicePort" -ForegroundColor Gray
        Write-Host "   DB Host: $dbHost" -ForegroundColor Gray
        Write-Host "   DB Port: $dbPort" -ForegroundColor Gray
        Write-Host "   DB Name: $dbName" -ForegroundColor Gray
        Write-Host "   DB User: $dbUser" -ForegroundColor Gray
        
        $dbConfigs += @{
            Service = $service.Name
            DBHost = $dbHost
            DBPort = $dbPort
            DBName = $dbName
            DBUser = $dbUser
            ServicePort = $servicePort
        }
    } else {
        Write-Host "`n❌ $($service.Name): .env file not found" -ForegroundColor Red
    }
}

# Analyze database usage
Write-Host "`n📊 DATABASE USAGE ANALYSIS:" -ForegroundColor Yellow
Write-Host "----------------------------" -ForegroundColor Gray

$uniqueDatabases = $dbConfigs | Group-Object -Property DBName | Where-Object { $_.Name -ne "NOT_FOUND" }

if ($uniqueDatabases.Count -eq 1) {
    $dbName = $uniqueDatabases[0].Name
    $serviceCount = $uniqueDatabases[0].Count
    
    Write-Host "`n✅ SHARED DATABASE PATTERN DETECTED" -ForegroundColor Green
    Write-Host "   Database Name: $dbName" -ForegroundColor Cyan
    Write-Host "   Services Using This DB: $serviceCount" -ForegroundColor Cyan
    
    Write-Host "`n   Services connected to '$dbName':" -ForegroundColor Gray
    foreach ($config in $dbConfigs) {
        if ($config.DBName -eq $dbName) {
            Write-Host "   • $($config.Service) (Port: $($config.ServicePort))" -ForegroundColor Gray
        }
    }
    
    Write-Host "`n💡 This means:" -ForegroundColor Yellow
    Write-Host "   • All services share the same PostgreSQL database" -ForegroundColor White
    Write-Host "   • Each service has its own tables within that database" -ForegroundColor White
    Write-Host "   • Services are logically separated but physically shared" -ForegroundColor White
    
} elseif ($uniqueDatabases.Count -gt 1) {
    Write-Host "`n🔄 MULTIPLE DATABASE PATTERN DETECTED" -ForegroundColor Yellow
    Write-Host "   Number of Different Databases: $($uniqueDatabases.Count)" -ForegroundColor Cyan
    
    foreach ($dbGroup in $uniqueDatabases) {
        Write-Host "`n   Database: $($dbGroup.Name)" -ForegroundColor Cyan
        foreach ($service in $dbGroup.Group) {
            Write-Host "   • $($service.Service)" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "`n❌ NO DATABASE CONFIGURATION FOUND" -ForegroundColor Red
}

# Test actual database connections
Write-Host "`n🔌 TESTING ACTUAL DATABASE CONNECTIONS:" -ForegroundColor Yellow
Write-Host "--------------------------------------" -ForegroundColor Gray

$healthEndpoints = @(
    @{Name="User Service"; URL="http://localhost:8011/health"},
    @{Name="Service Catalog"; URL="http://localhost:8002/health"},
    @{Name="Product Service"; URL="http://localhost:8010/health"}
)

foreach ($endpoint in $healthEndpoints) {
    try {
        $response = Invoke-RestMethod -Uri $endpoint.URL -Method GET -TimeoutSec 3
        Write-Host "✅ $($endpoint.Name): Connected and Running" -ForegroundColor Green
    } catch {
        Write-Host "❌ $($endpoint.Name): Not responding" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n📋 SUMMARY:" -ForegroundColor Cyan
Write-Host "----------" -ForegroundColor Gray
Write-Host "• You have SEPARATE .env files for service independence" -ForegroundColor White
Write-Host "• All services connect to the SAME database (shark_db)" -ForegroundColor White
Write-Host "• Each service runs on a DIFFERENT port" -ForegroundColor White
Write-Host "• This is a SHARED DATABASE microservices pattern" -ForegroundColor White

Write-Host "`n🎯 CONCLUSION:" -ForegroundColor Green
Write-Host "Your setup uses ONE PostgreSQL database with multiple services" -ForegroundColor White
Write-Host "Each .env file configures the service's specific settings" -ForegroundColor White
